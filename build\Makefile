# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\mingw64\bin\cmake.exe

# The command to remove a file.
RM = C:\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\KWL-Software\EVT\EngineFrameworkInterface

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\KWL-Software\EVT\EngineFrameworkInterface\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	C:\mingw64\bin\cmake.exe -E echo "No interactive CMake dialog available."
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	C:\mingw64\bin\cmake.exe --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\KWL-Software\EVT\EngineFrameworkInterface\build\CMakeFiles E:\KWL-Software\EVT\EngineFrameworkInterface\build\\CMakeFiles\progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\KWL-Software\EVT\EngineFrameworkInterface\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named EngineFrameworkInterface

# Build rule for target.
EngineFrameworkInterface: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 EngineFrameworkInterface
.PHONY : EngineFrameworkInterface

# fast build rule for target.
EngineFrameworkInterface/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/build
.PHONY : EngineFrameworkInterface/fast

src/data_process.obj: src/data_process.cpp.obj
.PHONY : src/data_process.obj

# target to build an object file
src/data_process.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/data_process.cpp.obj
.PHONY : src/data_process.cpp.obj

src/data_process.i: src/data_process.cpp.i
.PHONY : src/data_process.i

# target to preprocess a source file
src/data_process.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/data_process.cpp.i
.PHONY : src/data_process.cpp.i

src/data_process.s: src/data_process.cpp.s
.PHONY : src/data_process.s

# target to generate assembly for a file
src/data_process.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/data_process.cpp.s
.PHONY : src/data_process.cpp.s

src/data_recorder.obj: src/data_recorder.cpp.obj
.PHONY : src/data_recorder.obj

# target to build an object file
src/data_recorder.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/data_recorder.cpp.obj
.PHONY : src/data_recorder.cpp.obj

src/data_recorder.i: src/data_recorder.cpp.i
.PHONY : src/data_recorder.i

# target to preprocess a source file
src/data_recorder.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/data_recorder.cpp.i
.PHONY : src/data_recorder.cpp.i

src/data_recorder.s: src/data_recorder.cpp.s
.PHONY : src/data_recorder.s

# target to generate assembly for a file
src/data_recorder.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/data_recorder.cpp.s
.PHONY : src/data_recorder.cpp.s

src/device_manager.obj: src/device_manager.cpp.obj
.PHONY : src/device_manager.obj

# target to build an object file
src/device_manager.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/device_manager.cpp.obj
.PHONY : src/device_manager.cpp.obj

src/device_manager.i: src/device_manager.cpp.i
.PHONY : src/device_manager.i

# target to preprocess a source file
src/device_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/device_manager.cpp.i
.PHONY : src/device_manager.cpp.i

src/device_manager.s: src/device_manager.cpp.s
.PHONY : src/device_manager.s

# target to generate assembly for a file
src/device_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/device_manager.cpp.s
.PHONY : src/device_manager.cpp.s

src/engine_core.obj: src/engine_core.cpp.obj
.PHONY : src/engine_core.obj

# target to build an object file
src/engine_core.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/engine_core.cpp.obj
.PHONY : src/engine_core.cpp.obj

src/engine_core.i: src/engine_core.cpp.i
.PHONY : src/engine_core.i

# target to preprocess a source file
src/engine_core.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/engine_core.cpp.i
.PHONY : src/engine_core.cpp.i

src/engine_core.s: src/engine_core.cpp.s
.PHONY : src/engine_core.s

# target to generate assembly for a file
src/engine_core.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/engine_core.cpp.s
.PHONY : src/engine_core.cpp.s

src/engine_framework_interface.obj: src/engine_framework_interface.cpp.obj
.PHONY : src/engine_framework_interface.obj

# target to build an object file
src/engine_framework_interface.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/engine_framework_interface.cpp.obj
.PHONY : src/engine_framework_interface.cpp.obj

src/engine_framework_interface.i: src/engine_framework_interface.cpp.i
.PHONY : src/engine_framework_interface.i

# target to preprocess a source file
src/engine_framework_interface.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/engine_framework_interface.cpp.i
.PHONY : src/engine_framework_interface.cpp.i

src/engine_framework_interface.s: src/engine_framework_interface.cpp.s
.PHONY : src/engine_framework_interface.s

# target to generate assembly for a file
src/engine_framework_interface.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/engine_framework_interface.cpp.s
.PHONY : src/engine_framework_interface.cpp.s

src/engine_manager.obj: src/engine_manager.cpp.obj
.PHONY : src/engine_manager.obj

# target to build an object file
src/engine_manager.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/engine_manager.cpp.obj
.PHONY : src/engine_manager.cpp.obj

src/engine_manager.i: src/engine_manager.cpp.i
.PHONY : src/engine_manager.i

# target to preprocess a source file
src/engine_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/engine_manager.cpp.i
.PHONY : src/engine_manager.cpp.i

src/engine_manager.s: src/engine_manager.cpp.s
.PHONY : src/engine_manager.s

# target to generate assembly for a file
src/engine_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/engine_manager.cpp.s
.PHONY : src/engine_manager.cpp.s

src/engine_variable_system.obj: src/engine_variable_system.cpp.obj
.PHONY : src/engine_variable_system.obj

# target to build an object file
src/engine_variable_system.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/engine_variable_system.cpp.obj
.PHONY : src/engine_variable_system.cpp.obj

src/engine_variable_system.i: src/engine_variable_system.cpp.i
.PHONY : src/engine_variable_system.i

# target to preprocess a source file
src/engine_variable_system.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/engine_variable_system.cpp.i
.PHONY : src/engine_variable_system.cpp.i

src/engine_variable_system.s: src/engine_variable_system.cpp.s
.PHONY : src/engine_variable_system.s

# target to generate assembly for a file
src/engine_variable_system.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/engine_variable_system.cpp.s
.PHONY : src/engine_variable_system.cpp.s

src/global_core.obj: src/global_core.cpp.obj
.PHONY : src/global_core.obj

# target to build an object file
src/global_core.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/global_core.cpp.obj
.PHONY : src/global_core.cpp.obj

src/global_core.i: src/global_core.cpp.i
.PHONY : src/global_core.i

# target to preprocess a source file
src/global_core.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/global_core.cpp.i
.PHONY : src/global_core.cpp.i

src/global_core.s: src/global_core.cpp.s
.PHONY : src/global_core.s

# target to generate assembly for a file
src/global_core.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/global_core.cpp.s
.PHONY : src/global_core.cpp.s

src/global_variable_service.obj: src/global_variable_service.cpp.obj
.PHONY : src/global_variable_service.obj

# target to build an object file
src/global_variable_service.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_service.cpp.obj
.PHONY : src/global_variable_service.cpp.obj

src/global_variable_service.i: src/global_variable_service.cpp.i
.PHONY : src/global_variable_service.i

# target to preprocess a source file
src/global_variable_service.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_service.cpp.i
.PHONY : src/global_variable_service.cpp.i

src/global_variable_service.s: src/global_variable_service.cpp.s
.PHONY : src/global_variable_service.s

# target to generate assembly for a file
src/global_variable_service.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_service.cpp.s
.PHONY : src/global_variable_service.cpp.s

src/global_variable_task.obj: src/global_variable_task.cpp.obj
.PHONY : src/global_variable_task.obj

# target to build an object file
src/global_variable_task.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_task.cpp.obj
.PHONY : src/global_variable_task.cpp.obj

src/global_variable_task.i: src/global_variable_task.cpp.i
.PHONY : src/global_variable_task.i

# target to preprocess a source file
src/global_variable_task.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_task.cpp.i
.PHONY : src/global_variable_task.cpp.i

src/global_variable_task.s: src/global_variable_task.cpp.s
.PHONY : src/global_variable_task.s

# target to generate assembly for a file
src/global_variable_task.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_task.cpp.s
.PHONY : src/global_variable_task.cpp.s

src/logger.obj: src/logger.cpp.obj
.PHONY : src/logger.obj

# target to build an object file
src/logger.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/logger.cpp.obj
.PHONY : src/logger.cpp.obj

src/logger.i: src/logger.cpp.i
.PHONY : src/logger.i

# target to preprocess a source file
src/logger.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/logger.cpp.i
.PHONY : src/logger.cpp.i

src/logger.s: src/logger.cpp.s
.PHONY : src/logger.s

# target to generate assembly for a file
src/logger.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/logger.cpp.s
.PHONY : src/logger.cpp.s

src/report_process.obj: src/report_process.cpp.obj
.PHONY : src/report_process.obj

# target to build an object file
src/report_process.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/report_process.cpp.obj
.PHONY : src/report_process.cpp.obj

src/report_process.i: src/report_process.cpp.i
.PHONY : src/report_process.i

# target to preprocess a source file
src/report_process.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/report_process.cpp.i
.PHONY : src/report_process.cpp.i

src/report_process.s: src/report_process.cpp.s
.PHONY : src/report_process.s

# target to generate assembly for a file
src/report_process.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/src/report_process.cpp.s
.PHONY : src/report_process.cpp.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... rebuild_cache
	@echo ... EngineFrameworkInterface
	@echo ... src/data_process.obj
	@echo ... src/data_process.i
	@echo ... src/data_process.s
	@echo ... src/data_recorder.obj
	@echo ... src/data_recorder.i
	@echo ... src/data_recorder.s
	@echo ... src/device_manager.obj
	@echo ... src/device_manager.i
	@echo ... src/device_manager.s
	@echo ... src/engine_core.obj
	@echo ... src/engine_core.i
	@echo ... src/engine_core.s
	@echo ... src/engine_framework_interface.obj
	@echo ... src/engine_framework_interface.i
	@echo ... src/engine_framework_interface.s
	@echo ... src/engine_manager.obj
	@echo ... src/engine_manager.i
	@echo ... src/engine_manager.s
	@echo ... src/engine_variable_system.obj
	@echo ... src/engine_variable_system.i
	@echo ... src/engine_variable_system.s
	@echo ... src/global_core.obj
	@echo ... src/global_core.i
	@echo ... src/global_core.s
	@echo ... src/global_variable_service.obj
	@echo ... src/global_variable_service.i
	@echo ... src/global_variable_service.s
	@echo ... src/global_variable_task.obj
	@echo ... src/global_variable_task.i
	@echo ... src/global_variable_task.s
	@echo ... src/logger.obj
	@echo ... src/logger.i
	@echo ... src/logger.s
	@echo ... src/report_process.obj
	@echo ... src/report_process.i
	@echo ... src/report_process.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

