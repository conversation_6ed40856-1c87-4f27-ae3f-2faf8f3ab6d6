#ifndef DEVICE_MANAGER_HPP
#define DEVICE_MANAGER_HPP

#include "common_types.hpp"
#include <string>
#include <unordered_map>
#include <memory>
#include <mutex>
#include <vector>
#include <filesystem>

#ifdef _WIN32
#include <windows.h>
typedef HMODULE DLL_HANDLE;
#define GET_PROC_ADDRESS(handle, name) GetProcAddress(static_cast<HMODULE>(handle), name)
#define LOAD_LIBRARY LoadLibraryA
#define FREE_LIBRARY FreeLibrary
#else
#include <dlfcn.h>
typedef void* DLL_HANDLE;
#define GET_PROC_ADDRESS(handle, name) dlsym(handle, name)
#define LOAD_LIBRARY dlopen
#define FREE_LIBRARY dlclose
#endif

// 前向声明
struct DeviceInstance;
class DeviceInstanceManager;

// DLL函数指针类型定义
typedef ATE_EC (*InitializeDllFunc)();
typedef ATE_EC (*CleanupDllFunc)();
typedef ATE_EC (*ConnectDeviceFunc)(const int32_t channel_no);
typedef ATE_EC (*DisconnectDeviceFunc)(const int32_t channel_no);
typedef ATE_EC (*GetDeviceDescriptorFunc)(const int32_t channel_no, char* device_descriptor, const int32_t buffer_size, int32_t* device_descriptor_size);
typedef ATE_EC (*ExecuteCommandUnifiedFunc)(const int32_t channel_no, const E5000_CommandMode mode, const char* command, const int32_t command_length, const int32_t timeout_ms, char* response, const int32_t buffer_size, int32_t* response_size);
typedef ATE_EC (*GetChannelStateFunc)(const int32_t channel_no, char* channel_state, const int32_t buffer_size, int32_t* channel_state_size);

/**
 * @brief DLL函数指针结构体
 * 包含所有需要从外设DLL中加载的函数指针
 */
struct DllFunctions {
    InitializeDllFunc initialize_dll;
    CleanupDllFunc cleanup_dll;
    ConnectDeviceFunc connect_device;
    DisconnectDeviceFunc disconnect_device;
    GetDeviceDescriptorFunc get_device_descriptor;
    ExecuteCommandUnifiedFunc execute_command_unified;
    GetChannelStateFunc get_channel_state;
    
    DllFunctions() {
        initialize_dll = nullptr;
        cleanup_dll = nullptr;
        connect_device = nullptr;
        disconnect_device = nullptr;
        get_device_descriptor = nullptr;
        execute_command_unified = nullptr;
        get_channel_state = nullptr;
    }
};

/**
 * @brief 设备实例信息结构体
 * 包含实例名、JSON配置文件中的index、DLL句柄和函数指针
 */
struct DeviceInstance {
    std::string instance_name;          // 实例名称
    int32_t device_index;              // JSON配置文件中的设备索引
    DLL_HANDLE dll_handle;             // DLL句柄
    std::string dll_path;              // DLL文件路径
    std::string config_file_path;      // 配置文件路径
    DllFunctions functions;            // DLL函数指针
    bool is_initialized;               // 是否已初始化
    bool is_connected;                 // 是否已连接
    
    DeviceInstance() {
        device_index = -1;
        dll_handle = nullptr;
        is_initialized = false;
        is_connected = false;
    }
};

/**
 * @brief 设备实例管理器类
 * 负责管理所有外设DLL实例，提供统一的接口访问
 * 使用单例模式确保全局唯一性
 */
class DeviceInstanceManager {
public:
    /**
     * @brief 获取单例实例
     * @return DeviceInstanceManager& 管理器实例引用
     */
    static DeviceInstanceManager& get_instance();
    
    /**
     * @brief 初始化管理器
     * @param dll_directory_path DLL目录路径
     * @return ATE_EC 错误代码
     */
    ATE_EC initialize(const std::string& dll_directory_path);
    
    /**
     * @brief 清理管理器
     * @param dll_directory_path DLL目录路径（可选，用于指定特定路径下的DLL）
     * @return ATE_EC 错误代码
     */
    ATE_EC cleanup(const std::string& dll_directory_path = "");
    
    /**
     * @brief 根据实例名查找设备实例
     * @param instance_name 实例名称
     * @return std::shared_ptr<DeviceInstance> 设备实例指针，未找到返回nullptr
     */
    std::shared_ptr<DeviceInstance> find_instance(const std::string& instance_name);
    
    /**
     * @brief 添加设备实例
     * @param instance 设备实例
     * @return ATE_EC 错误代码
     */
    ATE_EC add_instance(std::shared_ptr<DeviceInstance> instance);
    
    /**
     * @brief 移除设备实例
     * @param instance_name 实例名称
     * @return ATE_EC 错误代码
     */
    ATE_EC remove_instance(const std::string& instance_name);
    
    /**
     * @brief 获取所有实例名称
     * @return std::vector<std::string> 实例名称列表
     */
    std::vector<std::string> get_all_instance_names() const;
    
    /**
     * @brief 检查管理器是否已初始化
     * @return bool 是否已初始化
     */
    bool is_initialized() const;
    
    /**
     * @brief 获取实例数量
     * @return size_t 实例数量
     */
    size_t get_instance_count() const;
    
private:
    // 私有构造函数，实现单例模式
    DeviceInstanceManager() = default;
    ~DeviceInstanceManager();
    
    // 禁用拷贝构造和赋值操作
    DeviceInstanceManager(const DeviceInstanceManager&) = delete;
    DeviceInstanceManager& operator=(const DeviceInstanceManager&) = delete;
    
    /**
     * @brief 加载指定路径下的所有DLL
     * @param dll_directory_path DLL目录路径
     * @return ATE_EC 错误代码
     */
    ATE_EC load_dlls_from_directory(const std::string& dll_directory_path);
    
    /**
     * @brief 加载单个DLL
     * @param dll_path DLL文件路径
     * @param config_path 配置文件路径
     * @return ATE_EC 错误代码
     */
    ATE_EC load_single_dll(const std::string& dll_path, const std::string& config_path);
    
    /**
     * @brief 卸载DLL
     * @param instance 设备实例
     * @return ATE_EC 错误代码
     */
    ATE_EC unload_dll(std::shared_ptr<DeviceInstance> instance);
    
    /**
     * @brief 从DLL中加载函数指针
     * @param dll_handle DLL句柄
     * @param functions 函数指针结构体
     * @return ATE_EC 错误代码
     */
    ATE_EC load_dll_functions(DLL_HANDLE dll_handle, DllFunctions& functions);
    
    /**
     * @brief 解析JSON配置文件
     * @param config_path 配置文件路径
     * @param dll_path DLL文件路径
     * @return ATE_EC 错误代码
     */
    ATE_EC parse_config_file(const std::string& config_path, const std::string& dll_path);
    
private:
    mutable std::mutex m_mutex;                                           // 线程安全互斥锁
    std::unordered_map<std::string, std::shared_ptr<DeviceInstance>> m_instances; // 实例容器
};

#endif // DEVICE_MANAGER_HPP