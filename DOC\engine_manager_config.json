{"version": "1.0.0", "engine_cores": [{"engine_id": 1, "engine_name": "EngineCore_1", "test_project_package_path": "E:\\KWL-Software\\EVT\\TestPackages\\Engine1\\TestProject", "test_report_storage_path": "E:\\KWL-Software\\EVT\\Reports\\Engine1", "test_data_storage_path": "E:\\KWL-Software\\EVT\\Data\\Engine1", "max_concurrent_tests": 5, "auto_cleanup": true, "log_level": "INFO"}, {"engine_id": 2, "engine_name": "EngineCore_2", "test_project_package_path": "E:\\KWL-Software\\EVT\\TestPackages\\Engine2\\TestProject", "test_report_storage_path": "E:\\KWL-Software\\EVT\\Reports\\Engine2", "test_data_storage_path": "E:\\KWL-Software\\EVT\\Data\\Engine2", "max_concurrent_tests": 3, "auto_cleanup": true, "log_level": "DEBUG"}], "system_monitor": {"enabled": true, "monitor_name": "SystemMonitor_Global", "system_monitor_package_path": "E:\\KWL-Software\\EVT\\SystemMonitor\\MonitorPackage", "global_variable_system_path": "E:\\KWL-Software\\EVT\\GlobalVariables", "redis_host": "localhost", "redis_port": 6379, "redis_database": 0, "update_frequency": 100}, "global_settings": {"temp_directory": "E:\\KWL-Software\\EVT\\Temp", "backup_directory": "E:\\KWL-Software\\EVT\\Backup", "auto_start_engines": false, "auto_start_system_monitor": true}}