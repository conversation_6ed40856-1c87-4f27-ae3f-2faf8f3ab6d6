{"artifacts": [{"path": "bin/libEngineFrameworkInterface.dll"}, {"path": "lib/libEngineFrameworkInterface.dll.a"}, {"path": "bin/libEngineFrameworkInterface.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "link_directories", "target_link_libraries", "target_compile_definitions", "include_directories", "target_include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 34, "parent": 0}, {"command": 1, "file": 0, "line": 11, "parent": 0}, {"command": 2, "file": 0, "line": 51, "parent": 0}, {"command": 2, "file": 0, "line": 55, "parent": 0}, {"command": 3, "file": 0, "line": 37, "parent": 0}, {"command": 4, "file": 0, "line": 10, "parent": 0}, {"command": 5, "file": 0, "line": 40, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-std=gnu++17"}], "defines": [{"backtrace": 5, "define": "DRIVER_MANAGER_DLL_EXPORTS"}, {"define": "EngineFrameworkInterface_EXPORTS"}], "includes": [{"backtrace": 6, "path": "D:/Portable/lua/include"}, {"backtrace": 7, "path": "E:/KWL-Software/EVT/EngineFrameworkInterface/include"}, {"backtrace": 7, "path": "E:/KWL-Software/EVT/EngineFrameworkInterface/third_library/spdlog/include"}, {"backtrace": 7, "path": "E:/KWL-Software/EVT/EngineFrameworkInterface/third_library"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}], "id": "EngineFrameworkInterface::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"backtrace": 2, "fragment": "-LD:\\Portable\\lua\\lib", "role": "libraryPath"}, {"backtrace": 3, "fragment": "-llua", "role": "libraries"}, {"backtrace": 4, "fragment": "-lpdh", "role": "libraries"}, {"backtrace": 4, "fragment": "-lws2_32", "role": "libraries"}, {"backtrace": 4, "fragment": "-lwsock32", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "EngineFrameworkInterface", "nameOnDisk": "libEngineFrameworkInterface.dll", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/data_process.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/data_recorder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/device_manager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/engine_core.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/engine_framework_interface.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/engine_manager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/engine_variable_system.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/global_variable_task.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/global_variable_service.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/logger.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/report_process.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/global_core.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}