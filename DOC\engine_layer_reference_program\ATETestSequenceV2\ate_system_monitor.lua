-- ============================================================================
-- ATE系统监控模块 - Step-Based Framework
-- 文件: ate_system_monitor.lua
-- 描述: 系统级监控，监控整个ATE系统的运行状态
-- 版本: V2.1 (标准化step-based工步控制框架)
-- ============================================================================

-- 工步控制变量
local step = 0
local run_flag = {1, 1, 1, 1, 1, 0, 0, 0, 0, 0}  -- 前5个工步启用
local test_result = "UNKNOWN"
local error_status = {}

-- 测试数据记录
local test_data_points = {}
local measurement_count = 0

-- 固定功能begin
function add_string(str)
    if not error_status[str] then
        error_status[str] = true
        return true
    end
    return false
end

-- 工步保护状态检查
function seq_protect_check()
    -- 监控模块不需要设备状态检查，直接返回true
    return true
end

-- 工步保护动作执行
function seq_protect_act()
    -- 监控模块不需要保护动作，记录错误信息
    if (next(error_status) ~= nil) then
        for error_key, _ in pairs(error_status) do
            log_system_message("监控过程中发生错误: " .. error_key)
        end
        -- 通过故障保护动作的组合返回做3种情况，只记录+不动作+继续执行，记录+动作+继续执行，记录+动作+停止执行
        -- 无函数体 不动作
        -- return 1 动作后返回1  则动作了 后面停止执行
        -- return 2 动作了返回2  则动作了 后面继续执行
    end
    return false  -- 继续监控
end

-- 自定精确延时函数（毫秒级）
-- @param x 总延时时间（毫秒）
function delayms(x)
    local sleepms = 10  -- 每次循环基础延时
    local loops = math.floor(x / sleepms)  -- 完整循环次数
    local remainder = x % sleepms  -- 剩余延时
    
    -- 执行完整循环延时
    for i = 1, loops do
        -- 休眠循环基础延时 sleep_ms 采用C++注册的方式，注册一个通用的接口给lua
        if sleep_ms then sleep_ms(sleepms) end
        if seq_protect_check() == false then
            return
        end
    end
    
    -- 执行剩余延时
    if remainder > 0 then
        -- 休眠毫秒剩余
        if sleep_ms then sleep_ms(remainder) end
        if seq_protect_check() == false then
            return
        end
    end
end
-- 固定功能end

-- ============================================================================
-- 全局监控状态和配置
-- ============================================================================

-- 监控状态
local monitor_state = {
    is_running = false,
    start_time = 0,
    last_check_time = 0,
    check_interval = 1000,  -- 检查间隔(毫秒)
    emergency_stop = false,
    alert_count = 0
}

-- 监控阈值配置
local thresholds = {
    -- 系统资源阈值
    cpu_usage_warning = 80,     -- CPU使用率警告阈值(%)
    cpu_usage_critical = 95,    -- CPU使用率严重阈值(%)
    memory_usage_warning = 85,  -- 内存使用率警告阈值(%)
    memory_usage_critical = 95, -- 内存使用率严重阈值(%)
    disk_usage_warning = 90,    -- 磁盘使用率警告阈值(%)
    disk_usage_critical = 98,   -- 磁盘使用率严重阈值(%)
    
    -- 设备状态阈值
    voltage_min = 11.5,         -- 最小电压(V)
    voltage_max = 12.5,         -- 最大电压(V)
    current_max = 10.0,         -- 最大电流(A)
    temperature_max = 85,       -- 最大温度(°C)
    
    -- 通信超时阈值
    device_timeout = 5000,      -- 设备通信超时(毫秒)
    response_timeout = 3000     -- 响应超时(毫秒)
}

-- 设备状态缓存
local device_states = {
    power_supply = {
        voltage = 0,
        current = 0,
        status = "unknown",
        last_update = 0
    },
    oscilloscope = {
        status = "unknown",
        temperature = 0,
        last_update = 0
    },
    load_box = {
        status = "unknown",
        temperature = 0,
        last_update = 0
    },
    communication = {
        can_status = "unknown",
        ethernet_status = "unknown",
        last_check = 0
    }
}

-- 告警历史记录
local alert_history = {}

-- ============================================================================
-- 系统资源监控模块
-- ============================================================================

-- 获取CPU使用率
local function get_cpu_usage()
    -- 模拟获取CPU使用率，实际应调用系统API或DLL接口
    local result = execute_command_unified("wmic cpu get loadpercentage /value", 2000)
    if result and result.success then
        local usage = string.match(result.output, "LoadPercentage=(%d+)")
        return tonumber(usage) or 0
    end
    return 0
 end

-- 获取内存使用率
local function get_memory_usage()
    -- 模拟获取内存使用率
    local result = execute_command_unified("wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /value", 2000)
    if result and result.success then
        local total = string.match(result.output, "TotalVisibleMemorySize=(%d+)")
        local free = string.match(result.output, "FreePhysicalMemory=(%d+)")
        if total and free then
            local used_percent = ((tonumber(total) - tonumber(free)) / tonumber(total)) * 100
            return math.floor(used_percent)
        end
    end
    return 0
end

-- 获取磁盘使用率
local function get_disk_usage()
    -- 模拟获取C盘使用率
    local result = execute_command_unified("wmic logicaldisk where size!=0 get size,freespace,caption /value", 2000)
    if result and result.success then
        -- 解析C盘信息
        local c_size = string.match(result.output, "Caption=C:.-Size=(%d+)")
        local c_free = string.match(result.output, "Caption=C:.-FreeSpace=(%d+)")
        if c_size and c_free then
            local used_percent = ((tonumber(c_size) - tonumber(c_free)) / tonumber(c_size)) * 100
            return math.floor(used_percent)
        end
    end
    return 0
end

-- 检查系统资源
local function check_system_resources()
    local cpu_usage = get_cpu_usage()
    local memory_usage = get_memory_usage()
    local disk_usage = get_disk_usage()
    
    local alerts = {}
    
    -- CPU检查
    if cpu_usage >= thresholds.cpu_usage_critical then
        table.insert(alerts, {type = "CRITICAL", category = "SYSTEM", message = string.format("CPU使用率过高: %d%%", cpu_usage), value = cpu_usage})
    elseif cpu_usage >= thresholds.cpu_usage_warning then
        table.insert(alerts, {type = "WARNING", category = "SYSTEM", message = string.format("CPU使用率警告: %d%%", cpu_usage), value = cpu_usage})
    end
    
    -- 内存检查
    if memory_usage >= thresholds.memory_usage_critical then
        table.insert(alerts, {type = "CRITICAL", category = "SYSTEM", message = string.format("内存使用率过高: %d%%", memory_usage), value = memory_usage})
    elseif memory_usage >= thresholds.memory_usage_warning then
        table.insert(alerts, {type = "WARNING", category = "SYSTEM", message = string.format("内存使用率警告: %d%%", memory_usage), value = memory_usage})
    end
    
    -- 磁盘检查
    if disk_usage >= thresholds.disk_usage_critical then
        table.insert(alerts, {type = "CRITICAL", category = "SYSTEM", message = string.format("磁盘使用率过高: %d%%", disk_usage), value = disk_usage})
    elseif disk_usage >= thresholds.disk_usage_warning then
        table.insert(alerts, {type = "WARNING", category = "SYSTEM", message = string.format("磁盘使用率警告: %d%%", disk_usage), value = disk_usage})
    end
    
    return alerts
end

-- ============================================================================
-- 设备状态监控模块
-- ============================================================================

-- 检查电源状态
local function check_power_supply()
    local alerts = {}
    
    -- 模拟获取电源状态，实际应调用设备DLL接口
    local voltage_result = execute_command_unified("get_power_voltage", 1000)
    local current_result = execute_command_unified("get_power_current", 1000)
    
    if voltage_result and voltage_result.success then
        local voltage = tonumber(voltage_result.output) or 0
        device_states.power_supply.voltage = voltage
        device_states.power_supply.last_update = os.time()
        
        if voltage < thresholds.voltage_min or voltage > thresholds.voltage_max then
            table.insert(alerts, {type = "CRITICAL", category = "DEVICE", message = string.format("电源电压异常: %.2fV", voltage), value = voltage})
        end
    else
        table.insert(alerts, {type = "CRITICAL", category = "DEVICE", message = "无法获取电源电压", value = 0})
    end
    
    if current_result and current_result.success then
        local current = tonumber(current_result.output) or 0
        device_states.power_supply.current = current
        
        if current > thresholds.current_max then
            table.insert(alerts, {type = "CRITICAL", category = "DEVICE", message = string.format("电源电流过高: %.2fA", current), value = current})
        end
    else
        table.insert(alerts, {type = "WARNING", category = "DEVICE", message = "无法获取电源电流", value = 0})
    end
    
    return alerts
end

-- 检查示波器状态
local function check_oscilloscope()
    local alerts = {}
    
    -- 模拟检查示波器连接状态
    local status_result = execute_command_unified("check_oscilloscope_status", 2000)
    
    if status_result and status_result.success then
        device_states.oscilloscope.status = "connected"
        device_states.oscilloscope.last_update = os.time()
        
        -- 检查温度
        local temp_result = execute_command_unified("get_oscilloscope_temperature", 1000)
        if temp_result and temp_result.success then
            local temperature = tonumber(temp_result.output) or 0
            device_states.oscilloscope.temperature = temperature
            
            if temperature > thresholds.temperature_max then
                table.insert(alerts, {type = "CRITICAL", category = "DEVICE", message = string.format("示波器温度过高: %d°C", temperature), value = temperature})
            end
        end
    else
        device_states.oscilloscope.status = "disconnected"
        table.insert(alerts, {type = "CRITICAL", category = "DEVICE", message = "示波器连接断开", value = 0})
    end
    
    return alerts
end

-- 检查负载箱状态
local function check_load_box()
    local alerts = {}
    
    -- 模拟检查负载箱状态
    local status_result = execute_command_unified("check_load_box_status", 2000)
    
    if status_result and status_result.success then
        device_states.load_box.status = "connected"
        device_states.load_box.last_update = os.time()
    else
        device_states.load_box.status = "disconnected"
        table.insert(alerts, {type = "WARNING", category = "DEVICE", message = "负载箱连接异常", value = 0})
    end
    
    return alerts
end

-- 检查通信状态
local function check_communication()
    local alerts = {}
    
    -- 检查CAN通信
    local can_result = execute_command_unified("check_can_status", 1500)
    if can_result and can_result.success then
        device_states.communication.can_status = "active"
    else
        device_states.communication.can_status = "inactive"
        table.insert(alerts, {type = "WARNING", category = "COMMUNICATION", message = "CAN通信异常", value = 0})
    end
    
    -- 检查以太网通信
    local eth_result = execute_command_unified("ping -n 1 192.168.1.100", 2000)
    if eth_result and eth_result.success and string.find(eth_result.output, "TTL") then
        device_states.communication.ethernet_status = "active"
    else
        device_states.communication.ethernet_status = "inactive"
        table.insert(alerts, {type = "WARNING", category = "COMMUNICATION", message = "以太网通信异常", value = 0})
    end
    
    device_states.communication.last_check = os.time()
    return alerts
end

-- ============================================================================
-- 逻辑规则检查模块
-- ============================================================================

-- 检查设备互斥规则
local function check_device_conflicts()
    local alerts = {}
    
    -- 检查电源和负载箱是否同时开启（示例规则）
    if device_states.power_supply.status == "on" and device_states.load_box.status == "connected" then
        local voltage = device_states.power_supply.voltage
        if voltage > 0 then
            -- 这里可以添加更复杂的冲突检测逻辑
            -- table.insert(alerts, {type = "WARNING", category = "LOGIC", message = "电源和负载箱同时激活，请检查配置", value = voltage})
        end
    end
    
    return alerts
end

-- 检查设备依赖关系
local function check_device_dependencies()
    local alerts = {}
    
    -- 检查示波器依赖（示例：测试时必须连接示波器）
    if device_states.power_supply.status == "on" and device_states.oscilloscope.status ~= "connected" then
        table.insert(alerts, {type = "WARNING", category = "LOGIC", message = "电源开启但示波器未连接", value = 0})
    end
    
    return alerts
end

-- 检查温度保护规则
local function check_temperature_protection()
    local alerts = {}
    
    -- 检查所有设备温度
    local max_temp = math.max(device_states.oscilloscope.temperature, device_states.load_box.temperature)
    
    if max_temp > thresholds.temperature_max then
        table.insert(alerts, {type = "CRITICAL", category = "LOGIC", message = string.format("系统温度过高: %d°C，启动保护", max_temp), value = max_temp})
    end
    
    return alerts
end

-- ============================================================================
-- 告警处理中心
-- ============================================================================

-- 记录告警
local function log_alert(alert)
    local timestamp = os.date("%Y-%m-%d %H:%M:%S")
    local log_entry = {
        timestamp = timestamp,
        type = alert.type,
        category = alert.category,
        message = alert.message,
        value = alert.value
    }
    
    table.insert(alert_history, log_entry)
    
    -- 保持告警历史记录在合理范围内
    if #alert_history > 1000 then
        table.remove(alert_history, 1)
    end
    
    -- 输出告警信息
    print(string.format("[%s] %s - %s: %s", timestamp, alert.type, alert.category, alert.message))
end

-- 处理告警
local function handle_alerts(alerts)
    for _, alert in ipairs(alerts) do
        log_alert(alert)
        monitor_state.alert_count = monitor_state.alert_count + 1
        
        -- 根据告警类型和类别执行相应动作
        if alert.type == "CRITICAL" then
            if alert.category == "SYSTEM" then
                handle_system_critical_alert(alert)
            elseif alert.category == "DEVICE" then
                handle_device_critical_alert(alert)
            elseif alert.category == "LOGIC" then
                handle_logic_critical_alert(alert)
            end
        elseif alert.type == "WARNING" then
            if alert.category == "SYSTEM" then
                handle_system_warning_alert(alert)
            elseif alert.category == "DEVICE" then
                handle_device_warning_alert(alert)
            elseif alert.category == "COMMUNICATION" then
                handle_communication_warning_alert(alert)
            end
        end
    end
end

-- ============================================================================
-- 响应动作实现
-- ============================================================================

-- 系统严重告警响应
function handle_system_critical_alert(alert)
    if string.find(alert.message, "CPU") then
        -- CPU过载处理
        print("执行CPU过载保护：清理临时文件，关闭非必要进程")
        cleanup_temp_files()
        -- execute_command_unified("taskkill /f /im notepad.exe", 1000)  -- 示例：关闭记事本
    elseif string.find(alert.message, "内存") then
        -- 内存不足处理
        print("执行内存保护：强制垃圾回收，清理缓存")
        collectgarbage("collect")
        cleanup_log_files()
    elseif string.find(alert.message, "磁盘") then
        -- 磁盘空间不足处理
        print("执行磁盘清理：删除临时文件和旧日志")
        cleanup_temp_files()
        cleanup_log_files()
    end
end

-- 设备严重告警响应
function handle_device_critical_alert(alert)
    if string.find(alert.message, "电源") then
        -- 电源异常处理
        print("检测到电源异常，执行紧急断电保护")
        emergency_power_shutdown()
    elseif string.find(alert.message, "示波器") then
        -- 示波器异常处理
        print("示波器异常，尝试重新连接")
        reconnect_oscilloscope()
    elseif string.find(alert.message, "温度") then
        -- 温度过高处理
        print("设备温度过高，启动散热保护")
        activate_cooling_protection()
    end
end

-- 逻辑严重告警响应
function handle_logic_critical_alert(alert)
    if string.find(alert.message, "温度") then
        -- 系统温度保护
        print("系统温度过高，执行紧急停机保护")
        emergency_system_shutdown()
    elseif string.find(alert.message, "冲突") then
        -- 设备冲突处理
        print("检测到设备冲突，强制断开冲突设备")
        resolve_device_conflicts()
    end
end

-- 系统警告响应
function handle_system_warning_alert(alert)
    print("系统警告：" .. alert.message .. "，继续监控")
end

-- 设备警告响应
function handle_device_warning_alert(alert)
    print("设备警告：" .. alert.message .. "，尝试自动恢复")
    if string.find(alert.message, "负载箱") then
        reconnect_load_box()
    end
end

-- 通信警告响应
function handle_communication_warning_alert(alert)
    print("通信警告：" .. alert.message .. "，尝试重新建立连接")
    if string.find(alert.message, "CAN") then
        reinitialize_can_communication()
    elseif string.find(alert.message, "以太网") then
        reset_ethernet_connection()
    end
end

-- ============================================================================
-- 具体响应动作函数
-- ============================================================================

-- 紧急断电
function emergency_power_shutdown()
    print("[紧急动作] 执行紧急断电")
    execute_command_unified("power_off_all_devices", 1000)
    monitor_state.emergency_stop = true
end

-- 解决设备冲突
function resolve_device_conflicts()
    print("[自动修复] 解决设备冲突")
    execute_command_unified("disconnect_conflicting_devices", 2000)
end

-- 启动散热保护
function activate_cooling_protection()
    print("[保护动作] 启动散热系统")
    execute_command_unified("activate_cooling_fan", 1000)
    -- 降低设备功率
    execute_command_unified("reduce_device_power", 1000)
end

-- 设备重连
function reconnect_oscilloscope()
    print("[自动修复] 重新连接示波器")
    execute_command_unified("reconnect_oscilloscope", 3000)
end

function reconnect_load_box()
    print("[自动修复] 重新连接负载箱")
    execute_command_unified("reconnect_load_box", 3000)
end

-- 通信重置
function reinitialize_can_communication()
    print("[自动修复] 重新初始化CAN通信")
    execute_command_unified("reset_can_interface", 2000)
end

function reset_ethernet_connection()
    print("[自动修复] 重置以太网连接")
    execute_command_unified("ipconfig /release && ipconfig /renew", 5000)
end

-- 清理函数
function cleanup_temp_files()
    print("[清理动作] 清理临时文件")
    execute_command_unified("del /q /s %temp%\\*.*", 3000)
end

function cleanup_log_files()
    print("[清理动作] 清理旧日志文件")
    execute_command_unified("forfiles /p .\\logs /s /m *.log /d -7 /c \"cmd /c del @path\"", 3000)
end

-- 紧急停机
function emergency_system_shutdown()
    print("[紧急停机] 系统紧急停机保护")
    emergency_power_shutdown()
    execute_command_unified("shutdown /s /t 30 /c \"ATE系统紧急停机保护\"", 1000)
end

-- ============================================================================
-- 主监控循环
-- ============================================================================

-- 执行单次监控检查
local function perform_monitoring_check()
    local all_alerts = {}
    
    -- 系统资源监控
    local system_alerts = check_system_resources()
    for _, alert in ipairs(system_alerts) do
        table.insert(all_alerts, alert)
    end
    
    -- 设备状态监控
    local power_alerts = check_power_supply()
    for _, alert in ipairs(power_alerts) do
        table.insert(all_alerts, alert)
    end
    
    local osc_alerts = check_oscilloscope()
    for _, alert in ipairs(osc_alerts) do
        table.insert(all_alerts, alert)
    end
    
    local load_alerts = check_load_box()
    for _, alert in ipairs(load_alerts) do
        table.insert(all_alerts, alert)
    end
    
    local comm_alerts = check_communication()
    for _, alert in ipairs(comm_alerts) do
        table.insert(all_alerts, alert)
    end
    
    -- 逻辑规则检查
    local conflict_alerts = check_device_conflicts()
    for _, alert in ipairs(conflict_alerts) do
        table.insert(all_alerts, alert)
    end
    
    local dependency_alerts = check_device_dependencies()
    for _, alert in ipairs(dependency_alerts) do
        table.insert(all_alerts, alert)
    end
    
    local temp_alerts = check_temperature_protection()
    for _, alert in ipairs(temp_alerts) do
        table.insert(all_alerts, alert)
    end
    
    -- 处理所有告警
    if #all_alerts > 0 then
        handle_alerts(all_alerts)
    end
    
    monitor_state.last_check_time = os.time()
end

-- 主监控循环函数
function start_ate_system_monitor()
    print("=== ATE系统监控启动 ===")
    print("监控间隔: " .. monitor_state.check_interval .. "毫秒")
    print("开始时间: " .. os.date("%Y-%m-%d %H:%M:%S"))
    
    monitor_state.is_running = true
    monitor_state.start_time = os.time()
    monitor_state.emergency_stop = false
    monitor_state.alert_count = 0
    
    -- 主监控循环
    while monitor_state.is_running and not monitor_state.emergency_stop do
        -- 执行监控检查
        perform_monitoring_check()
        
        -- 等待下一次检查
        local sleep_time = monitor_state.check_interval
        execute_command_unified("timeout /t " .. math.floor(sleep_time/1000), sleep_time + 500)
        
        -- 检查是否需要停止监控
        if monitor_state.alert_count > 100 then
            print("告警次数过多，停止监控")
            break
        end
    end
    
    print("=== ATE系统监控停止 ===")
    print("运行时长: " .. (os.time() - monitor_state.start_time) .. "秒")
    print("总告警次数: " .. monitor_state.alert_count)
end

-- 停止监控
function stop_ate_system_monitor()
    print("收到停止监控指令")
    monitor_state.is_running = false
end

-- 获取监控状态
function get_monitor_status()
    return {
        is_running = monitor_state.is_running,
        start_time = monitor_state.start_time,
        last_check_time = monitor_state.last_check_time,
        alert_count = monitor_state.alert_count,
        emergency_stop = monitor_state.emergency_stop,
        device_states = device_states,
        recent_alerts = {table.unpack(alert_history, math.max(1, #alert_history-10))}
    }
end

-- 更新监控配置
function update_monitor_config(new_config)
    if new_config.check_interval then
        monitor_state.check_interval = new_config.check_interval
    end
    
    if new_config.thresholds then
        for key, value in pairs(new_config.thresholds) do
            if thresholds[key] then
                thresholds[key] = value
            end
        end
    end
    
    print("监控配置已更新")
end

-- ============================================================================
-- 脚本入口点
-- ============================================================================

-- 如果脚本被直接执行，启动监控
if not package.loaded["ate_system_monitor"] then
    print("ATE系统监控脚本已加载")
    print("调用 start_ate_system_monitor() 开始监控")
    print("调用 stop_ate_system_monitor() 停止监控")
    print("调用 get_monitor_status() 获取状态")
    
    -- 可以在这里自动启动监控，或者等待外部调用
    -- start_ate_system_monitor()
end
