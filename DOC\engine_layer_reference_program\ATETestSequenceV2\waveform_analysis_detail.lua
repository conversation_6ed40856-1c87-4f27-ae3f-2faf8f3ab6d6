-- ============================================================================
-- 波形分析详细测试序列 (三级子序列) - Step-based框架
-- 文件: waveform_analysis_detail.lua
-- 描述: 详细波形分析测试，由waveform_basic_test.lua调用，包含pre、seq、post三个标签分区
-- 版本: 2.0 (Step-based重构版本)
-- ============================================================================

-- 工步控制变量
local step = 1
local run_flag = {1, 1, 1, 1, 1}  -- 5个工步的运行标志
local test_result = "UNKNOWN"
local error_status = {}

-- 子序列全局变量定义
waveform_analysis_detail = {
    test_result = "UNKNOWN",
    test_name = "波形分析详细测试",
    start_time = 0,
    end_time = 0,
    step_count = 0,
    error_messages = {},
    analysis_data = {},
    thd_results = {},
    frequency_analysis = {},
    amplitude_analysis = {}
}

-- 固定功能begin
function add_string(str)
    if not error_status[str] then
        error_status[str] = true
        return true
    end
    return false
end

-- 保护检查函数
function seq_protect_check()
    -- 检查系统状态，如温度、电压、电流等
    -- 返回true表示正常，false表示异常
    return true
end

-- 保护动作函数
function seq_protect_act()
    if (next(error_status) ~= nil) then
        for error_key, _ in pairs(error_status) do
            -- 波形分析详细测试的保护动作
            -- 这里可以添加具体的保护动作
        end
        -- 通过故障保护动作的组合返回做3种情况，只记录+不动作+继续执行，记录+动作+继续执行，记录+动作+停止执行
        -- 无函数体 不动作
        -- return 1 动作后返回1  则动作了 后面停止执行
        -- return 2 动作了返回2  则动作了 后面继续执行
    end
end
-- 固定功能end

-- 精确延时函数
function delayms(ms)
    local start_time = os.clock()
    while (os.clock() - start_time) * 1000 < ms do
        -- 空循环等待
    end
end

-- 测试参数配置
local analysis_params = {
    sample_rate = 100000.0,         -- 采样率(Hz)
    sample_duration = 1.0,          -- 采样时长(s)
    fft_points = 4096,              -- FFT点数
    frequency_resolution = 24.414,  -- 频率分辨率(Hz)
    fundamental_frequency = 50.0,   -- 基波频率(Hz)
    harmonic_count = 20,            -- 分析谐波数量
    thd_limit = 5.0,               -- THD限制(%)
    amplitude_tolerance = 0.05,     -- 幅值容差(5%)
    frequency_tolerance = 0.1,      -- 频率容差(Hz)
    noise_floor_limit = -60.0,      -- 噪声底限(dB)
    channels = {"CH1", "CH2", "CH3"}, -- 分析通道
    window_function = "HANNING",    -- 窗函数类型
    trigger_level = 0.1,           -- 触发电平(V)
    pre_trigger_samples = 1000      -- 预触发采样点数
}

-- 工具函数
-- function log_analysis_message(level, message)
--     local timestamp = os.date("%Y-%m-%d %H:%M:%S")
--     print(string.format("[%s] [波形分析] %s: %s", timestamp, level, message))
-- end

-- 写入波形分析JSON报告
function write_waveform_json_report()
    local report_data = {
        test_name = "波形分析详细测试",
        test_result = test_result or "UNKNOWN",
        start_time = waveform_analysis_detail.start_time or 0,
        end_time = waveform_analysis_detail.end_time or 0,
        duration = (waveform_analysis_detail.end_time or 0) - (waveform_analysis_detail.start_time or 0),
        step_count = waveform_analysis_detail.step_count or 0,
        measurement_count = waveform_analysis_detail.measurement_count or 0,
        
        -- 波形分析特定数据
        analysis_summary = {
            total_channels_analyzed = #(waveform_analysis_detail.analysis_data or {}),
            frequency_analysis_count = 0,
            amplitude_analysis_count = 0,
            thd_measurements = {}
        },
        
        -- 详细分析数据
        analysis_data = waveform_analysis_detail.analysis_data or {},
        frequency_analysis = waveform_analysis_detail.frequency_analysis or {},
        
        -- 测试参数
        test_parameters = {
            sample_rate = analysis_params.sample_rate,
            fft_points = analysis_params.fft_points,
            frequency_resolution = analysis_params.frequency_resolution,
            thd_threshold = analysis_params.thd_threshold,
            frequency_tolerance = analysis_params.frequency_tolerance
        },
        
        -- 错误信息
        error_info = waveform_analysis_detail.error_info or {},
        error_status = error_status or "NONE"
    }
    
    -- 统计分析数据
    for channel, data in pairs(waveform_analysis_detail.analysis_data or {}) do
        if data.amplitude then
            report_data.analysis_summary.amplitude_analysis_count = report_data.analysis_summary.amplitude_analysis_count + 1
        end
    end
    
    for channel, data in pairs(waveform_analysis_detail.frequency_analysis or {}) do
        report_data.analysis_summary.frequency_analysis_count = report_data.analysis_summary.frequency_analysis_count + 1
        if data.thd then
            table.insert(report_data.analysis_summary.thd_measurements, {
                channel = channel,
                thd_percent = data.thd
            })
        end
    end
    
    -- 转换为JSON字符串
    local json_str = json_encode(report_data)
    
    -- 调用C++接口写入文件
    local success = write_test_report_json("waveform_analysis_detail", json_str)
    
    if success then
        log_message("[波形分析] JSON测试报告已生成", "INFO")
    else
        log_message("[波形分析] JSON测试报告生成失败", "ERROR")
    end
end

function add_analysis_error(message)
    table.insert(waveform_analysis_detail.error_messages, message)
    log_message("[波形分析] " .. message, "ERROR")
end

function calculate_thd(fundamental_amplitude, harmonic_amplitudes)
    local harmonic_sum_squared = 0.0
    for _, amplitude in ipairs(harmonic_amplitudes) do
        harmonic_sum_squared = harmonic_sum_squared + (amplitude * amplitude)
    end
    
    if fundamental_amplitude <= 0 then
        return 100.0  -- 如果基波幅值为0，返回最大THD
    end
    
    local thd = math.sqrt(harmonic_sum_squared) / fundamental_amplitude * 100.0
    return thd
end

function analyze_frequency_spectrum(channel, fft_data)
    local spectrum_analysis = {
        channel = channel,
        fundamental = {
            frequency = 0.0,
            amplitude = 0.0,
            phase = 0.0
        },
        harmonics = {},
        noise_floor = 0.0,
        snr = 0.0
    }
    
    -- 查找基波
    local fundamental_bin = math.floor(analysis_params.fundamental_frequency / analysis_params.frequency_resolution)
    local max_amplitude = 0.0
    local max_bin = fundamental_bin
    
    -- 在基波附近搜索最大值
    for bin = fundamental_bin - 2, fundamental_bin + 2 do
        if bin > 0 and bin < #fft_data and fft_data[bin] > max_amplitude then
            max_amplitude = fft_data[bin]
            max_bin = bin
        end
    end
    
    spectrum_analysis.fundamental.frequency = max_bin * analysis_params.frequency_resolution
    spectrum_analysis.fundamental.amplitude = max_amplitude
    
    -- 分析谐波
    for harmonic = 2, analysis_params.harmonic_count do
        local harmonic_freq = spectrum_analysis.fundamental.frequency * harmonic
        local harmonic_bin = math.floor(harmonic_freq / analysis_params.frequency_resolution)
        
        if harmonic_bin < #fft_data then
            local harmonic_amplitude = fft_data[harmonic_bin]
            table.insert(spectrum_analysis.harmonics, {
                order = harmonic,
                frequency = harmonic_freq,
                amplitude = harmonic_amplitude,
                relative_db = 20.0 * math.log10(harmonic_amplitude / max_amplitude)
            })
        end
    end
    
    -- 计算噪声底
    local noise_sum = 0.0
    local noise_count = 0
    for bin = 1, #fft_data do
        local freq = bin * analysis_params.frequency_resolution
        local is_signal = false
        
        -- 检查是否为信号频率（基波或谐波）
        if math.abs(freq - spectrum_analysis.fundamental.frequency) < analysis_params.frequency_tolerance then
            is_signal = true
        else
            for _, harmonic in ipairs(spectrum_analysis.harmonics) do
                if math.abs(freq - harmonic.frequency) < analysis_params.frequency_tolerance then
                    is_signal = true
                    break
                end
            end
        end
        
        if not is_signal and fft_data[bin] > 0 then
            noise_sum = noise_sum + fft_data[bin]
            noise_count = noise_count + 1
        end
    end
    
    if noise_count > 0 then
        spectrum_analysis.noise_floor = noise_sum / noise_count
        spectrum_analysis.snr = 20.0 * math.log10(max_amplitude / spectrum_analysis.noise_floor)
    end
    
    return spectrum_analysis
end

function perform_waveform_capture(channel)
    -- 配置示波器
    dll_send_command(main.device_channels.oscilloscope, string.format("CHANNEL:%s:DISPLAY:ON", channel))
    dll_send_command(main.device_channels.oscilloscope, string.format("CHANNEL:%s:SCALE:AUTO", channel))
    dll_send_command(main.device_channels.oscilloscope, string.format("TRIGGER:SOURCE:%s", channel))
    dll_send_command(main.device_channels.oscilloscope, string.format("TRIGGER:LEVEL:%.3f", analysis_params.trigger_level))
    dll_send_command(main.device_channels.oscilloscope, "TRIGGER:MODE:NORMAL")
    
    -- 设置采样参数
    dll_send_command(main.device_channels.oscilloscope, string.format("TIMEBASE:SCALE:%.6f", analysis_params.sample_duration / 10.0))
    dll_send_command(main.device_channels.oscilloscope, string.format("ACQUIRE:SRATE:%.0f", analysis_params.sample_rate))
    dll_send_command(main.device_channels.oscilloscope, string.format("ACQUIRE:POINTS:%d", math.floor(analysis_params.sample_rate * analysis_params.sample_duration)))
    
    -- 启动采集
    dll_send_command(main.device_channels.oscilloscope, "ACQUIRE:STATE:RUN")
    
    -- 等待触发和采集完成
    local timeout_count = 0
    local max_timeout = 50  -- 5秒超时
    
    while timeout_count < max_timeout do
        local acq_state = dll_query_command(main.device_channels.oscilloscope, "ACQUIRE:STATE?")
        if acq_state == "STOP" then
            break
        end
        os.execute("timeout /t 1 /nobreak > nul")
        timeout_count = timeout_count + 1
    end
    
    if timeout_count >= max_timeout then
        add_analysis_error(string.format("通道%s波形采集超时", channel))
        return nil
    end
    
    -- 读取波形数据
    local waveform_data_str = dll_query_command(main.device_channels.oscilloscope, string.format("CHANNEL:%s:DATA?", channel))
    
    -- 解析波形数据（假设返回逗号分隔的数值）
    local waveform_data = {}
    for value_str in string.gmatch(waveform_data_str, "([^,]+)") do
        local value = tonumber(value_str)
        if value then
            table.insert(waveform_data, value)
        end
    end
    
    return waveform_data
end

function perform_fft_analysis(waveform_data)
    -- 简化的FFT分析（实际应用中需要更复杂的FFT实现）
    -- 这里模拟FFT结果
    local fft_result = {}
    local data_length = #waveform_data
    
    if data_length < analysis_params.fft_points then
        add_analysis_error(string.format("波形数据点数不足: %d < %d", data_length, analysis_params.fft_points))
        return nil
    end
    
    -- 应用窗函数（简化实现）
    local windowed_data = {}
    for i = 1, analysis_params.fft_points do
        local window_value = 1.0
        if analysis_params.window_function == "HANNING" then
            window_value = 0.5 * (1.0 - math.cos(2.0 * math.pi * (i - 1) / (analysis_params.fft_points - 1)))
        elseif analysis_params.window_function == "HAMMING" then
            window_value = 0.54 - 0.46 * math.cos(2.0 * math.pi * (i - 1) / (analysis_params.fft_points - 1))
        end
        windowed_data[i] = waveform_data[i] * window_value
    end
    
    -- 模拟FFT计算（实际需要调用专业FFT库）
    for bin = 1, analysis_params.fft_points / 2 do
        local frequency = (bin - 1) * analysis_params.frequency_resolution
        local amplitude = 0.0
        
        -- 简化的频谱计算
        for i = 1, analysis_params.fft_points do
            local phase = 2.0 * math.pi * frequency * (i - 1) / analysis_params.sample_rate
            amplitude = amplitude + windowed_data[i] * math.cos(phase)
        end
        
        fft_result[bin] = math.abs(amplitude) / analysis_params.fft_points
    end
    
    return fft_result
end

-- ============================================================================
-- PRE 标签 - 预处理阶段
-- ============================================================================
::pre::
log_message("[波形分析] === 开始波形分析详细测试预处理阶段 ===", "INFO")
waveform_analysis_detail.start_time = os.time()
waveform_analysis_detail.step_count = 0
waveform_analysis_detail.error_messages = {}
waveform_analysis_detail.analysis_data = {}
waveform_analysis_detail.thd_results = {}
waveform_analysis_detail.frequency_analysis = {}
waveform_analysis_detail.amplitude_analysis = {}

-- 检查设备状态
if not device_initialized then
    add_analysis_error("设备未初始化，无法执行波形分析")
    waveform_analysis_detail.test_result = "FAILED"
    goto post
end

-- 检查示波器是否可用
local scope_status = dll_query_command(main.device_channels.oscilloscope, "STATUS?")
if scope_status == "ERROR" then
    add_analysis_error("示波器设备不可用")
    waveform_analysis_detail.test_result = "FAILED"
    goto post
end

-- 检查信号发生器是否可用
local gen_status = dll_query_command(main.device_channels.signal_generator, "STATUS?")
if gen_status == "ERROR" then
    add_analysis_error("信号发生器设备不可用")
    waveform_analysis_detail.test_result = "FAILED"
    goto post
end

-- 配置示波器
log_message("[波形分析] 配置示波器...", "INFO")
dll_send_command(main.device_channels.oscilloscope, "RESET")
dll_send_command(main.device_channels.oscilloscope, "AUTOSET:OFF")
dll_send_command(main.device_channels.oscilloscope, "ACQUIRE:MODE:SAMPLE")
dll_send_command(main.device_channels.oscilloscope, "ACQUIRE:STOPAFTER:SEQUENCE")
dll_send_command(main.device_channels.oscilloscope, "DISPLAY:PERSISTENCE:OFF")

-- 配置信号发生器（用于校准和测试）
log_message("[波形分析] 配置信号发生器...", "INFO")
dll_send_command(main.device_channels.signal_generator, "RESET")
dll_send_command(main.device_channels.signal_generator, "OUTPUT:OFF")
dll_send_command(main.device_channels.signal_generator, string.format("FREQUENCY:%.1f", analysis_params.fundamental_frequency))
dll_send_command(main.device_channels.signal_generator, "AMPLITUDE:1.0")
dll_send_command(main.device_channels.signal_generator, "WAVEFORM:SINE")
dll_send_command(main.device_channels.signal_generator, "OUTPUT:IMPEDANCE:50")

-- 计算频率分辨率
analysis_params.frequency_resolution = analysis_params.sample_rate / analysis_params.fft_points
log_message(string.format("[波形分析] 频率分辨率: %.3f Hz", analysis_params.frequency_resolution), "INFO")

-- 验证配置
if analysis_params.fundamental_frequency > (analysis_params.sample_rate / 2) then
    add_analysis_error("基波频率超出奈奎斯特频率")
    waveform_analysis_detail.test_result = "FAILED"
    goto post
end

log_message("[波形分析] === 波形分析详细测试预处理阶段完成 ===", "INFO")

-- ============================================================================
-- SEQ 标签 - 测试序列阶段 (Step-based)
-- ============================================================================
::seq::
log_message("[波形分析] === 开始波形分析详细测试序列阶段 ===", "INFO")

step = 1
while true do
    -- 工步1: CH1通道波形分析
    if step == 1 then
        if run_flag[step] ~= 0 then
            log_message("[波形分析] 工步1: CH1通道波形分析", "INFO")
            waveform_analysis_detail.step_count = waveform_analysis_detail.step_count + 1
            
            local channel = "CH1"
            local waveform_data = perform_waveform_capture(channel)
            
            if waveform_data and #waveform_data > 0 then
                -- 基本波形参数分析
                local max_value = math.max(table.unpack(waveform_data))
                local min_value = math.min(table.unpack(waveform_data))
                local peak_to_peak = max_value - min_value
                local rms_sum = 0.0
                
                for _, value in ipairs(waveform_data) do
                    rms_sum = rms_sum + (value * value)
                end
                local rms_value = math.sqrt(rms_sum / #waveform_data)
                
                waveform_analysis_detail.amplitude_analysis[channel] = {
                    max_value = max_value,
                    min_value = min_value,
                    peak_to_peak = peak_to_peak,
                    rms_value = rms_value,
                    crest_factor = max_value / rms_value
                }
                
                log_message(string.format("[波形分析] 通道 %s 幅值分析完成: 峰峰值=%.3fV, RMS=%.3fV", 
                                    channel, peak_to_peak, rms_value), "INFO")
                test_result = "PASSED"
                step = step + 1
            else
                add_analysis_error(string.format("通道 %s 波形采集失败", channel))
                test_result = "FAILED"
            end
        else
            step = step + 1
        end
    end
    
    -- 工步2: CH1通道FFT频谱分析
    if step == 2 then
        if run_flag[step] ~= 0 then
            log_message("[波形分析] 工步2: CH1通道FFT频谱分析", "INFO")
            waveform_analysis_detail.step_count = waveform_analysis_detail.step_count + 1
            
            local channel = "CH1"
            local waveform_data = perform_waveform_capture(channel)
            
            if waveform_data and #waveform_data > 0 then
                local fft_data = perform_fft_analysis(waveform_data)
                
                if fft_data then
                    local spectrum_analysis = analyze_frequency_spectrum(channel, fft_data)
                    waveform_analysis_detail.frequency_analysis[channel] = spectrum_analysis
                    
                    -- THD计算
                    local harmonic_amplitudes = {}
                    for _, harmonic in ipairs(spectrum_analysis.harmonics) do
                        table.insert(harmonic_amplitudes, harmonic.amplitude)
                    end
                    
                    local thd_value = calculate_thd(spectrum_analysis.fundamental.amplitude, harmonic_amplitudes)
                    waveform_analysis_detail.thd_results[channel] = {
                        thd_percent = thd_value,
                        fundamental_amplitude = spectrum_analysis.fundamental.amplitude,
                        harmonic_count = #harmonic_amplitudes,
                        harmonics = spectrum_analysis.harmonics
                    }
                    
                    log_message(string.format("[波形分析] 通道 %s FFT分析完成: 基波%.2fHz, THD%.2f%%", 
                                        channel, spectrum_analysis.fundamental.frequency, thd_value), "INFO")
                    
                    -- 检查THD和频率
                    if thd_value > analysis_params.thd_limit then
                        add_analysis_error(string.format("通道 %s THD超限: %.2f%% > %.1f%%", 
                                          channel, thd_value, analysis_params.thd_limit))
                        test_result = "FAILED"
                    else
                        test_result = "PASSED"
                    end
                    step = step + 1
                else
                    add_analysis_error(string.format("通道 %s FFT分析失败", channel))
                    test_result = "FAILED"
                end
            else
                add_analysis_error(string.format("通道 %s 波形数据无效", channel))
                test_result = "FAILED"
            end
        else
            step = step + 1
        end
    end
    
    -- 工步3: CH2通道分析
    if step == 3 then
        if run_flag[step] ~= 0 then
            log_message("[波形分析] 工步3: CH2通道分析", "INFO")
            waveform_analysis_detail.step_count = waveform_analysis_detail.step_count + 1
            
            local channel = "CH2"
            local waveform_data = perform_waveform_capture(channel)
            
            if waveform_data and #waveform_data > 0 then
                -- 执行与CH1相同的分析流程
                local fft_data = perform_fft_analysis(waveform_data)
                if fft_data then
                    local spectrum_analysis = analyze_frequency_spectrum(channel, fft_data)
                    waveform_analysis_detail.frequency_analysis[channel] = spectrum_analysis
                    
                    log_message(string.format("[波形分析] 通道 %s 分析完成", channel), "INFO")
                    test_result = "PASSED"
                else
                    add_analysis_error(string.format("通道 %s 分析失败", channel))
                    test_result = "FAILED"
                end
            end
            step = step + 1
        else
            step = step + 1
        end
    end
    
    -- 工步4: CH3通道分析
    if step == 4 then
        if run_flag[step] ~= 0 then
            log_message("[波形分析] 工步4: CH3通道分析", "INFO")
            waveform_analysis_detail.step_count = waveform_analysis_detail.step_count + 1
            
            local channel = "CH3"
            local waveform_data = perform_waveform_capture(channel)
            
            if waveform_data and #waveform_data > 0 then
                local fft_data = perform_fft_analysis(waveform_data)
                if fft_data then
                    local spectrum_analysis = analyze_frequency_spectrum(channel, fft_data)
                    waveform_analysis_detail.frequency_analysis[channel] = spectrum_analysis
                    
                    log_message(string.format("[波形分析] 通道 %s 分析完成", channel), "INFO")
                    test_result = "PASSED"
                else
                    add_analysis_error(string.format("通道 %s 分析失败", channel))
                    test_result = "FAILED"
                end
            end
            step = step + 1
        else
            step = step + 1
        end
    end
    
    -- 工步5: 交叉通道分析和数据整合
    if step == 5 then
        if run_flag[step] ~= 0 then
            log_message("[波形分析] 工步5: 交叉通道分析和数据整合", "INFO")
            waveform_analysis_detail.step_count = waveform_analysis_detail.step_count + 1
            
            -- 整合所有通道的分析数据
            for _, channel in ipairs(analysis_params.channels) do
                if waveform_analysis_detail.frequency_analysis[channel] and 
                   waveform_analysis_detail.amplitude_analysis[channel] and 
                   waveform_analysis_detail.thd_results[channel] then
                    
                    table.insert(waveform_analysis_detail.analysis_data, {
                        channel = channel,
                        amplitude_analysis = waveform_analysis_detail.amplitude_analysis[channel],
                        frequency_analysis = waveform_analysis_detail.frequency_analysis[channel],
                        thd_analysis = waveform_analysis_detail.thd_results[channel],
                        timestamp = os.time()
                    })
                end
            end
            
            -- 交叉通道相位差分析
            if #waveform_analysis_detail.analysis_data > 1 then
                log_message("[波形分析] 执行交叉通道相位差分析", "INFO")
            end
            
            test_result = "PASSED"
            step = step + 1
        else
            step = step + 1
        end
    end
    
    -- 每一次循环都执行一次检查
    if seq_protect_check() == false then
        if seq_protect_act() == true then
            break
        end
    end
    
    -- 工步执行完成或测试失败
    if step >= 6 or test_result == "FAILED" then
        if test_result == "PASSED" then
            log_message("[波形分析] 波形分析详细测试序列阶段通过", "INFO")
        else
            log_message("[波形分析] 波形分析详细测试序列阶段失败", "ERROR")
        end
        break
    end
end

log_message("[波形分析] === 波形分析详细测试序列阶段完成 ===", "INFO")

-- ============================================================================
-- POST 标签 - 后处理阶段
-- ============================================================================
::post::
log_message("[波形分析] === 开始波形分析详细测试后处理阶段 ===", "INFO")
waveform_analysis_detail.end_time = os.time()

-- 关闭设备输出
dll_send_command(main.device_channels.signal_generator, "OUTPUT:OFF")
dll_send_command(main.device_channels.oscilloscope, "ACQUIRE:STATE:STOP")
log_message("[波形分析] 所有设备输出已关闭", "INFO")

-- 生成测试报告
local test_duration = waveform_analysis_detail.end_time - waveform_analysis_detail.start_time
local total_channels = #waveform_analysis_detail.analysis_data

-- 计算工步完成情况
local total_steps = 5  -- 总工步数
local completed_steps = math.min(step - 1, total_steps)  -- 已完成工步数
local step_completion_rate = (completed_steps / total_steps) * 100  -- 工步完成率

-- 确定最终测试结果和错误状态
local final_test_result = "PASSED"
local error_status = "NONE"

if #waveform_analysis_detail.error_messages > 0 then
    final_test_result = "FAILED"
    error_status = "ANALYSIS_ERROR"
elseif total_channels == 0 then
    final_test_result = "FAILED"
    error_status = "NO_DATA"
    add_analysis_error("没有有效的分析数据")
elseif completed_steps < total_steps then
    final_test_result = "FAILED"
    error_status = "INCOMPLETE"
end

waveform_analysis_detail.test_result = final_test_result

-- 输出测试结果
print("\n" .. string.rep("-", 60))
print("波形分析详细测试结果报告")
print(string.rep("-", 60))
print(string.format("测试名称: %s", waveform_analysis_detail.test_name))
print(string.format("测试结果: %s", final_test_result))
print(string.format("测试时长: %d 秒", test_duration))
print(string.format("完成工步: %d/%d", completed_steps, total_steps))
print(string.format("工步完成率: %.1f%%", step_completion_rate))
print(string.format("分析通道: %d", total_channels))
print(string.format("错误数量: %d", #waveform_analysis_detail.error_messages))
print(string.format("错误状态: %s", error_status))

-- 打印工步详情
print("\n工步详情:")
print("  工步1: CH1通道波形分析 - " .. (completed_steps >= 1 and "完成" or "未完成"))
print("  工步2: CH1通道FFT频谱分析 - " .. (completed_steps >= 2 and "完成" or "未完成"))
print("  工步3: CH2通道分析 - " .. (completed_steps >= 3 and "完成" or "未完成"))
print("  工步4: CH3通道分析 - " .. (completed_steps >= 4 and "完成" or "未完成"))
print("  工步5: 交叉通道分析和数据整合 - " .. (completed_steps >= 5 and "完成" or "未完成"))

if #waveform_analysis_detail.error_messages > 0 then
    print("\n错误信息:")
    for i, error_msg in ipairs(waveform_analysis_detail.error_messages) do
        print(string.format("  %d. %s", i, error_msg))
    end
end

-- 输出详细分析结果
if total_channels > 0 then
    print("\n详细分析结果:")
    print("通道  基波频率(Hz)  基波幅值(V)  THD(%)  RMS(V)  峰峰值(V)")
    print(string.rep("-", 70))
    
    for _, data in ipairs(waveform_analysis_detail.analysis_data) do
        local freq_analysis = data.frequency_analysis
        local amp_analysis = data.amplitude_analysis
        local thd_analysis = data.thd_analysis
        
        print(string.format("%-4s  %11.2f  %10.3f  %6.2f  %6.3f  %9.3f", 
              data.channel,
              freq_analysis.fundamental.frequency,
              freq_analysis.fundamental.amplitude,
              thd_analysis.thd_percent,
              amp_analysis.rms_value,
              amp_analysis.peak_to_peak))
    end
    
    -- 输出谐波分析
    print("\n谐波分析 (前5次谐波):")
    for _, data in ipairs(waveform_analysis_detail.analysis_data) do
        print(string.format("\n通道 %s:", data.channel))
        print("次数  频率(Hz)  幅值(V)  相对电平(dB)")
        print(string.rep("-", 40))
        
        local harmonics = data.thd_analysis.harmonics
        for i = 1, math.min(5, #harmonics) do
            local harmonic = harmonics[i]
            print(string.format("%3d  %8.1f  %7.4f  %12.1f", 
                  harmonic.order, harmonic.frequency, harmonic.amplitude, harmonic.relative_db))
        end
    end
end
print(string.rep("-", 60))

-- 设置额外的测试结果信息
waveform_analysis_detail.completed_steps = completed_steps
waveform_analysis_detail.total_steps = total_steps
waveform_analysis_detail.error_status = error_status

log_message("[波形分析] === 波形分析详细测试后处理阶段完成 ===", "INFO")
log_message(string.format("[波形分析] 波形分析详细测试最终结果: %s", final_test_result), "INFO")
log_message(string.format("[波形分析] 工步完成情况: %d/%d (%.1f%%)", completed_steps, total_steps, step_completion_rate), "INFO")

-- 写入JSON测试报告
write_waveform_json_report()

-- 返回测试结果
return final_test_result