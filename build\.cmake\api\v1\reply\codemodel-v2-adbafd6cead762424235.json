{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "EngineFrameworkInterface", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "EngineFrameworkInterface::@6890427a1f51a3e7e1df", "jsonFile": "target-EngineFrameworkInterface-7a4552d26b441558c055.json", "name": "EngineFrameworkInterface", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "E:/KWL-Software/EVT/EngineFrameworkInterface/build", "source": "E:/KWL-Software/EVT/EngineFrameworkInterface"}, "version": {"major": 2, "minor": 8}}