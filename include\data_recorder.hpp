#ifndef DATA_RECORDER_HPP
#define DATA_RECORDER_HPP

#include <vector>
#include <string>
#include <chrono>
#include <memory>
#include <unordered_map>
#include <fstream>
#include <thread>
#include <atomic>
#include <mutex>
#include <queue>
#include <condition_variable>
#include "common_types.hpp"
#include "global_variable_task.hpp"

/**
 * @brief 数据记录结构体
 */
struct DataRecord {
    std::chrono::system_clock::time_point timestamp;
    std::unordered_map<std::string, std::string> variable_values; ///< 变量名 -> 值的字符串表示
    
    DataRecord() = default;
    explicit DataRecord(const std::chrono::system_clock::time_point& time) : timestamp(time) {}
};

/**
 * @brief 数据记录器配置
 */
struct DataRecorderConfig {
    size_t buffer_size = 10000;                                    ///< 缓冲区大小
    std::chrono::milliseconds recording_interval{100};             ///< 记录间隔
    std::chrono::milliseconds auto_save_interval{5000};            ///< 自动保存间隔
    size_t max_records_per_file = 50000;                          ///< 每个文件最大记录数
    std::string base_filename = "ate_data";                       ///< 基础文件名
    bool enable_auto_save = true;                                 ///< 是否启用自动保存
    bool enable_csv_export = true;                                ///< 是否启用CSV导出
    bool enable_xml_export = false;                               ///< 是否启用XML导出
    
    bool is_valid() const {
        return buffer_size > 0 && recording_interval.count() > 0 && 
               auto_save_interval.count() > 0 && max_records_per_file > 0;
    }
};

/**
 * @brief 精简版数据记录器类
 * 负责记录GlobalVariableTask中的变量数据，支持CSV导出和文件分割
 */
class EXP_API DataRecorder {
public:
    /**
     * @brief 构造函数
     * @param variable_system 全局变量系统引用
     * @param config 记录器配置
     */
    explicit DataRecorder(GlobalVariableTask& variable_system, const DataRecorderConfig& config = DataRecorderConfig{});
    
    /**
     * @brief 析构函数
     */
    ~DataRecorder();
    
    // 禁用拷贝和移动
    DataRecorder(const DataRecorder&) = delete;
    DataRecorder& operator=(const DataRecorder&) = delete;
    DataRecorder(DataRecorder&&) = delete;
    DataRecorder& operator=(DataRecorder&&) = delete;
    
    /**
     * @brief 初始化记录器
     * @return ATE_EC 错误代码
     */
    ATE_EC initialize();
    
    /**
     * @brief 关闭记录器
     */
    void shutdown();
    
    /**
     * @brief 开始数据记录
     * @return ATE_EC 错误代码
     */
    ATE_EC start_recording();
    
    /**
     * @brief 停止数据记录
     * @return ATE_EC 错误代码
     */
    ATE_EC stop_recording();
    
    /**
     * @brief 检查是否正在记录
     * @return true 正在记录，false 未记录
     */
    bool is_recording() const;
    
    /**
     * @brief 添加要记录的变量
     * @param variable_name 变量名
     * @return ATE_EC 错误代码
     */
    ATE_EC add_variable_to_record(const std::string& variable_name);
    
    /**
     * @brief 移除要记录的变量
     * @param variable_name 变量名
     * @return ATE_EC 错误代码
     */
    ATE_EC remove_variable_from_record(const std::string& variable_name);
    
    /**
     * @brief 获取当前记录的变量列表
     * @return 变量名列表
     */
    std::vector<std::string> get_recorded_variables() const;
    
    /**
     * @brief 清空记录的变量列表
     */
    void clear_recorded_variables();
    
    /**
     * @brief 获取已记录的数据条数
     * @return 记录条数
     */
    size_t get_record_count() const;
    
    /**
     * @brief 清空所有记录
     */
    void clear_records();
    
    /**
     * @brief 手动保存数据到文件
     * @param format 文件格式 ("csv" 或 "xml")
     * @param filename 文件名（可选，为空则使用默认命名）
     * @return ATE_EC 错误代码
     */
    ATE_EC save_to_file(const std::string& format = "csv", const std::string& filename = "");
    
    /**
     * @brief 设置记录配置
     * @param config 新的配置
     * @return ATE_EC 错误代码
     */
    ATE_EC set_config(const DataRecorderConfig& config);
    
    /**
     * @brief 获取当前配置
     * @return 当前配置
     */
    const DataRecorderConfig& get_config() const;
    
    /**
     * @brief 获取缓冲区使用率
     * @return 使用率 (0.0 - 1.0)
     */
    double get_buffer_usage() const;

private:
    GlobalVariableTask& m_variable_system;                      ///< 变量系统引用
    DataRecorderConfig m_config;                                  ///< 记录器配置
    
    // 状态控制
    std::atomic<bool> m_initialized;                              ///< 初始化状态
    std::atomic<bool> m_recording;                                ///< 记录状态
    std::atomic<bool> m_shutdown_requested;                       ///< 关闭请求标志
    
    // 数据存储
    std::vector<DataRecord> m_records;                            ///< 数据记录缓冲区
    std::vector<std::string> m_recorded_variables;                ///< 要记录的变量列表
    mutable std::mutex m_records_mutex;                           ///< 记录访问互斥锁
    mutable std::mutex m_variables_mutex;                         ///< 变量列表互斥锁
    
    // 线程控制
    std::unique_ptr<std::thread> m_recording_thread;              ///< 数据记录线程
    std::unique_ptr<std::thread> m_auto_save_thread;              ///< 自动保存线程
    
    // 文件管理
    std::atomic<size_t> m_current_file_records;                   ///< 当前文件记录数
    std::atomic<int> m_file_sequence;                             ///< 文件序列号
    
    /**
     * @brief 数据记录线程函数
     */
    void recording_thread_function();
    
    /**
     * @brief 自动保存线程函数
     */
    void auto_save_thread_function();
    
    /**
     * @brief 收集当前时刻的变量数据
     * @return 数据记录
     */
    DataRecord collect_current_data();
    
    /**
     * @brief 检查是否需要触发保存
     * @return true 需要保存，false 不需要保存
     */
    bool should_trigger_save() const;
    
    /**
     * @brief 生成文件名
     * @param format 文件格式
     * @param use_sequence 是否使用序列号
     * @return 完整文件名
     */
    std::string generate_filename(const std::string& format, bool use_sequence = true) const;
    
    /**
     * @brief 格式化时间戳
     * @param time_point 时间点
     * @return 格式化的时间字符串
     */
    std::string format_timestamp(const std::chrono::system_clock::time_point& time_point) const;
    
    /**
     * @brief 保存记录到CSV文件
     * @param records 要保存的记录
     * @param filename 文件名
     * @return ATE_EC 错误代码
     */
    ATE_EC save_records_to_csv(const std::vector<DataRecord>& records, const std::string& filename) const;
    
    /**
     * @brief 保存记录到XML文件
     * @param records 要保存的记录
     * @param filename 文件名
     * @return ATE_EC 错误代码
     */
    ATE_EC save_records_to_xml(const std::vector<DataRecord>& records, const std::string& filename) const;
    
    /**
     * @brief 执行实际的保存操作
     * @param format 文件格式
     * @param filename 文件名
     * @return ATE_EC 错误代码
     */
    ATE_EC perform_save(const std::string& format, const std::string& filename);
};

#endif // DATA_RECORDER_HPP