# 项目规则
- 项目名称：EVT
- 项目目录：E:\KWL-Software\EVT\EngineFrameworkInterface
- 项目描述：EVT是一个基于C++的工程框架，这是一个输出文件为DLL的工程框架，目的是在工业ATE测试系统中，扮演核心测试工艺执行的角色。这个DLL模块，还可以调用其他外设的驱动模块（DLL），并且本工程框架，向下管理可以多个实例的EngineCore模块，每个Enginecore模块可以执行测试工艺脚本，完成测试动作，并且可以执行数据保存和报告输出功能；本工程框架，向下管理单例SystemMonitor模块，每个systemmonitor模块管理一个全局变量系统。

- 当我在提出问题的最后有三个字符“请记录”，那么本次交互完成，都需要将交互记录，添加到项目目录下AiLog文件夹下，命名以当前时间+交互内容主题命名，文件格式为md文件，如果没有“请记录”，则不需要添加。
- 当我在提出问题的最后有三个字符“请操作”，那么你才能执行实际的操作，比如打开文件、修改文件、关闭文件、创建文件等。如果没有“请操作”，则不需要执行操作。