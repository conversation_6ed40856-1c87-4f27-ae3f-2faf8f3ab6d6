#include "variable_system.hpp"
#include <stdexcept>
#include <algorithm>
#include <unordered_set>

VariableSystem::VariableSystem() {
    // 初始化Lua
    m_lua.open_libraries();
    
    // 预留容器空间以提高性能
    m_variables.reserve(1000);

    std::cout << "VariableSystem initialized" << std::endl;
}

VariableSystem::~VariableSystem() noexcept {
    try {
        std::cout << "VariableSystem destroyed" << std::endl;
    } catch (...) {
        // 析构函数中不应抛出异常
        // 记录错误但不重新抛出
    }
}

// 变量管理方法
bool VariableSystem::set_variable(const std::string& name, const VariableValue& value) {
    if (name.empty()) {
        throw std::invalid_argument("Variable name cannot be empty");
    }

    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        // 检查是否已存在同名变量
        if (m_variables.find(name) != m_variables.end()) {
            return false; // 存在同名变量，插入失败
        }
        m_variables[name] = value;
    }
    std::cout << "Set variable '" << name << "' = " << get_type_name(value) << std::endl;
    return true; // 成功插入
}

VariableValue VariableSystem::get_variable(const std::string& name) const {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    auto it = m_variables.find(name);
    if (it != m_variables.end()) {
        return it->second;
    }
    throw std::runtime_error("Variable '" + name + "' not found");
}

bool VariableSystem::has_variable(const std::string& name) const {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    return m_variables.find(name) != m_variables.end();
}

void VariableSystem::remove_variable(const std::string& name) {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    auto it = m_variables.find(name);
    if (it != m_variables.end()) {
        m_variables.erase(it);
        std::cout << "Removed variable '" << name << "'" << std::endl;
    }
}

void VariableSystem::clear_all_variables() {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    size_t count = m_variables.size();
    m_variables.clear();
    std::cout << "Cleared " << count << " variables" << std::endl;
}

// 高性能的类型特化设置方法（避免variant构造开销）
void VariableSystem::set_number(const std::string& name, double value) {
    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        m_variables[name] = VariableValue(value);
    }
}

void VariableSystem::set_string(const std::string& name, const std::string& value) {
    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        m_variables[name] = VariableValue(value);
    }
}

void VariableSystem::set_bool(const std::string& name, bool value) {
    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        m_variables[name] = VariableValue(value);
    }
}

void VariableSystem::set_int(const std::string& name, int value) {
    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        m_variables[name] = VariableValue(value);
    }
}

// 带单位的设置方法
void VariableSystem::set_number(const std::string& name, double value, const std::string& unit) {
    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        m_variables[name] = VariableValue(value, unit);
    }
}

void VariableSystem::set_string(const std::string& name, const std::string& value, const std::string& unit) {
    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        m_variables[name] = VariableValue(value, unit);
    }
}

void VariableSystem::set_bool(const std::string& name, bool value, const std::string& unit) {
    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        m_variables[name] = VariableValue(value, unit);
    }
}

void VariableSystem::set_int(const std::string& name, int value, const std::string& unit) {
    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        m_variables[name] = VariableValue(value, unit);
    }
}

// 单位管理方法
void VariableSystem::set_variable_unit(const std::string& name, const std::string& unit) {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    auto it = m_variables.find(name);
    if (it != m_variables.end()) {
        it->second.setUnit(unit);
    }
}

std::string VariableSystem::get_variable_unit(const std::string& name) const {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    auto it = m_variables.find(name);
    if (it != m_variables.end()) {
        return it->second.getUnit();
    }
    return "";
}

bool VariableSystem::has_variable_unit(const std::string& name) const {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    auto it = m_variables.find(name);
    if (it != m_variables.end()) {
        return !it->second.getUnit().empty();
    }
    return false;
}

// 设置变量保存状态
void VariableSystem::set_variable_save_status(const std::string& name, bool should_save) {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    auto it = m_variables.find(name);
    if (it != m_variables.end()) {
        it->second.setShouldSave(should_save);
    } else {
        throw std::runtime_error("Variable '" + name + "' not found");
    }
}

// 获取变量保存状态
bool VariableSystem::get_variable_save_status(const std::string& name) const {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    auto it = m_variables.find(name);
    if (it != m_variables.end()) {
        return it->second.getShouldSave();
    }
    return false;
}

// 获取所有需要保存的变量名列表
std::vector<std::string> VariableSystem::get_saveable_variable_names() const {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    std::vector<std::string> saveable_names;
    for (const auto& pair : m_variables) {
        if (pair.second.getShouldSave()) {
            saveable_names.push_back(pair.first);
        }
    }
    return saveable_names;
}

// DataRecorder专用接口 - 设置变量保存标志
bool VariableSystem::set_variable_save_flag(const std::string& name, bool should_save) {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    auto it = m_variables.find(name);
    if (it != m_variables.end()) {
        it->second.setShouldSave(should_save);
        return true;
    }
    return false;
}

// DataRecorder专用接口 - 获取变量数据指针
void* VariableSystem::get_variable_data_pointer(const std::string& name) {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    auto it = m_variables.find(name);
    if (it != m_variables.end()) {
        VariableValue& value = it->second;
        switch (value.type) {
            case VariableType::DOUBLE:
                return &(value.data.doubleValue);
            case VariableType::INT:
                return &(value.data.intValue);
            case VariableType::BOOL:
                return &(value.data.boolValue);
            case VariableType::STRING:
                return value.stringData.get();
            default:
                return nullptr;
        }
    }
    return nullptr;
}

// 获取所有变量信息列表
std::vector<VariableSystem::VariableInfo> VariableSystem::list_variables() const {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    std::vector<VariableInfo> variable_list;
    
    for (const auto& pair : m_variables) {
        const std::string& name = pair.first;
        const VariableValue& value = pair.second;
        
        variable_list.emplace_back(
            name,
            value.getTypeName(),
            value.getShouldSave(),
            value.getUnit()
        );
    }
    
    return variable_list;
}

// 类型特化的获取方法
double VariableSystem::get_number(const std::string& name, double defaultValue) const {
    if (!has_variable(name)) return defaultValue;
    
    try {
        auto value = get_variable(name);
        return value.asDouble();
    } catch (...) {
        return defaultValue;
    }
}

std::string VariableSystem::get_string(const std::string& name, const std::string& defaultValue) const {
    if (!has_variable(name)) return defaultValue;
    
    try {
        auto value = get_variable(name);
        return value.asString();
    } catch (...) {
        return defaultValue;
    }
}

bool VariableSystem::get_bool(const std::string& name, bool defaultValue) const {
    if (!has_variable(name)) return defaultValue;
    
    try {
        auto value = get_variable(name);
        return value.asBool();
    } catch (...) {
        return defaultValue;
    }
}

int VariableSystem::get_int(const std::string& name, int defaultValue) const {
    if (!has_variable(name)) return defaultValue;
    
    try {
        auto value = get_variable(name);
        return value.asInt();
    } catch (...) {
        return defaultValue;
    }
}

// 调试和信息方法
void VariableSystem::print_all_variables() const {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    std::cout << "=== Variable System Status ===" << std::endl;
    std::cout << "Total variables: " << m_variables.size() << std::endl;

    for (const auto& [name, value] : m_variables) {
        std::cout << "  " << name << " = " << value.asString();
        std::cout << " (" << value.getTypeName() << ")";
        if (!value.getUnit().empty()) {
            std::cout << " [" << value.getUnit() << "]";
        }
        std::cout << std::endl;
    }
    std::cout << "===============================" << std::endl;
}

size_t VariableSystem::get_variable_count() const noexcept {
    try {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        return m_variables.size();
    } catch (...) {
        return 0;
    }
}

std::vector<std::string> VariableSystem::get_variable_names() const {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    std::vector<std::string> names;
    names.reserve(m_variables.size());

    for (const auto& [name, value] : m_variables) {
        names.emplace_back(name);  // 使用emplace_back提高效率
    }

    std::sort(names.begin(), names.end());
    return names;
}

// 辅助方法
std::string VariableSystem::get_type_name(const VariableValue& value) const {
    return value.getTypeName();
}

sol::object VariableSystem::push_variable_to_sol(const VariableValue& value) const {
    switch (value.type) {
        case VariableType::DOUBLE:
            return sol::make_object(m_lua, value.data.doubleValue);
        case VariableType::STRING:
            return sol::make_object(m_lua, value.stringData ? *value.stringData : std::string(""));
        case VariableType::BOOL:
            return sol::make_object(m_lua, value.data.boolValue);
        case VariableType::INT:
            return sol::make_object(m_lua, value.data.intValue);
        default:
            return sol::make_object(m_lua, sol::nil);
    }
}

VariableValue VariableSystem::get_variable_from_sol(const sol::object& obj) const {
    switch (obj.get_type()) {
        case sol::type::number:
            if (obj.is<int>()) {
                return VariableValue(obj.as<int>());
            } else {
                return VariableValue(obj.as<double>());
            }
        case sol::type::string:
            return VariableValue(obj.as<std::string>());
        case sol::type::boolean:
            return VariableValue(obj.as<bool>());
        default:
            return VariableValue();  // 返回默认值
    }
}

// Lua绑定实现
void VariableSystem::setup_lua_binding() {
    // ATE工业自动化测试场景下的自动同步实现
    // 通过重写__index和__newindex元方法实现C++变量系统与Lua的自动同步
    
    // 注册基础函数
    m_lua.set_function("cpp_set_var", [this](const std::string& name, sol::object value) {
        try {
            VariableValue varValue = this->get_variable_from_sol(value);
            this->set_variable(name, varValue);
        } catch (const std::exception& e) {
            std::cout << "Error setting variable: " << e.what() << std::endl;
        }
    });
    
    m_lua.set_function("cpp_get_var", [this](const std::string& name) -> sol::object {
        try {
            if (this->has_variable(name)) {
                auto value = this->get_variable(name);
                return this->push_variable_to_sol(value);
            }
        } catch (const std::exception& e) {
            std::cout << "Error getting variable: " << e.what() << std::endl;
        }
        return sol::nil;
    });
    
    m_lua.set_function("cpp_has_var", [this](const std::string& name) -> bool {
        return this->has_variable(name);
    });
    
    // 变量合法性检测函数
    m_lua.set_function("check_variable_conflict", [this](const std::string& name) -> bool {
        return this->check_variable_conflict(name);
    });
    
    // Lua脚本全局变量检索和冲突检测函数
    m_lua.set_function("check_lua_script_conflicts", [this](const std::string& script_content) -> sol::table {
        return this->check_lua_script_conflicts(script_content);
    });
    
    // 保存原始的全局表
    sol::table original_G = m_lua.globals();
    
    // 创建新的元表来实现自动同步
    sol::table global_meta = m_lua.create_table();
    
    // 重写__index元方法 - 只同步用户脚本变量
    global_meta["__index"] = [this, &original_G](sol::table t, const std::string& key) -> sol::object {
        // 首先检查原始全局表
        sol::object value = original_G.raw_get<sol::object>(key);
        if (value.valid() && value.get_type() != sol::type::lua_nil) {
            return value;
        }
        
        // 只同步用户脚本变量，过滤系统级变量
        if (this->is_user_script_variable(key) && this->has_variable(key)) {
            auto var_value = this->get_variable(key);
            return this->push_variable_to_sol(var_value);
        }
        
        return sol::nil;
    };
    
    // 重写__newindex元方法 - 只同步用户脚本变量
    global_meta["__newindex"] = [this, &original_G](sol::table t, const std::string& key, sol::object value) {
        // 使用rawset避免递归调用元方法
        lua_State* L = m_lua.lua_state();
        
        // 获取全局表
        lua_pushglobaltable(L);
        
        // 推入key和value
        lua_pushstring(L, key.c_str());
        sol::stack::push(L, value);
        
        // 使用rawset设置，避免触发元方法
        lua_rawset(L, -3);
        
        // 弹出全局表
        lua_pop(L, 1);
        
        // 只同步用户脚本变量到C++变量系统
        if (this->is_user_script_variable(key)) {
            try {
                VariableValue var_value = this->get_variable_from_sol(value);
                // 直接设置，不检查重名（按用户要求）
                std::lock_guard<std::mutex> lock(m_variables_mutex);
                m_variables[key] = var_value;
                std::cout << "Synced user variable '" << key << "' = " << this->get_type_name(var_value) << std::endl;
            } catch (const std::exception& e) {
                std::cout << "Error auto-syncing variable '" << key << "': " << e.what() << std::endl;
            }
        }
    };
    
    // 设置全局表的元表
    m_lua.globals().set(sol::metatable_key, global_meta);
    
    std::cout << "ATE自动化测试变量系统绑定完成。已启用__index和__newindex自动同步。" << std::endl;
}

// 用户脚本变量过滤函数实现
bool VariableSystem::is_user_script_variable(const std::string& variable_name) const {
    // 过滤系统级变量，避免递归和性能问题
    static const std::unordered_set<std::string> system_variables = {
        "_G", "_VERSION", "_ENV", "package", "require", "dofile", "loadfile",
        "print", "type", "getmetatable", "setmetatable", "rawget", "rawset",
        "pairs", "ipairs", "next", "tostring", "tonumber", "error", "assert",
        "pcall", "xpcall", "select", "unpack", "table", "string", "math",
        "io", "os", "debug", "coroutine", "utf8", "load", "loadstring",
        "collectgarbage", "gcinfo", "newproxy", "module", "setfenv", "getfenv"
    };
    
    // 如果是系统变量，不同步
    if (system_variables.find(variable_name) != system_variables.end()) {
        return false;
    }
    
    // 过滤以下划线开头的系统变量
    if (!variable_name.empty() && variable_name[0] == '_') {
        return false;
    }
    
    // 前面已经过滤了系统变量和下划线开头的变量
    // 剩余的都是用户脚本变量，直接同步
    return true;
}

// ATE自动化测试专用函数实现
bool VariableSystem::check_variable_conflict(const std::string& name) const {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    return m_variables.find(name) != m_variables.end();
}

sol::table VariableSystem::check_lua_script_conflicts(const std::string& script_content) {
    sol::table result = m_lua.create_table();
    sol::table conflicts = m_lua.create_table();
    sol::table global_vars = m_lua.create_table();
    
    try {
        // 创建一个临时的Lua环境来分析脚本
        sol::state temp_lua;
        temp_lua.open_libraries();
        
        // 重写全局表的__newindex来捕获全局变量赋值
        std::vector<std::string> detected_globals;
        
        sol::table temp_globals = temp_lua.globals();
        sol::table original_meta = temp_globals.get<sol::optional<sol::table>>(sol::metatable_key).value_or(temp_lua.create_table());
        
        // 创建新的元表来捕获变量赋值
        sol::table capture_meta = temp_lua.create_table();
        
        // 保留原有的__index
        if (original_meta["__index"].valid()) {
            capture_meta["__index"] = original_meta["__index"];
        }
        
        // 重写__newindex来捕获全局变量
        capture_meta["__newindex"] = [&detected_globals, &temp_globals](sol::table t, const std::string& key, sol::object value) {
            // 记录检测到的全局变量
            detected_globals.push_back(key);
            // 实际设置变量
            temp_globals.raw_set(key, value);
        };
        
        // 设置元表
        temp_globals.set(sol::metatable_key, capture_meta);
        
        // 执行脚本来检测全局变量
        temp_lua.script(script_content);
        
        // 同时检查脚本中可能存在的模块变量（如other.X形式）
        std::vector<std::string> module_vars;
        
        // 遍历全局表查找模块表
        for (auto& [key, value] : temp_globals) {
            if (key.is<std::string>() && value.is<sol::table>()) {
                std::string module_name = key.as<std::string>();
                sol::table module_table = value.as<sol::table>();
                
                // 遍历模块表中的变量
                for (auto& [mod_key, mod_value] : module_table) {
                    if (mod_key.is<std::string>()) {
                        std::string var_name = module_name + "." + mod_key.as<std::string>();
                        module_vars.push_back(var_name);
                    }
                }
            }
        }
        
        // 将检测到的全局变量添加到结果中
        int index = 1;
        for (const auto& var_name : detected_globals) {
            global_vars[index] = var_name;
            
            // 检查是否与C++变量系统中的变量冲突
            if (check_variable_conflict(var_name)) {
                conflicts[var_name] = true;
            }
            index++;
        }
        
        // 将模块变量也添加到结果中
        for (const auto& var_name : module_vars) {
            global_vars[index] = var_name;
            
            // 检查是否与C++变量系统中的变量冲突
            if (check_variable_conflict(var_name)) {
                conflicts[var_name] = true;
            }
            index++;
        }
        
        result["global_variables"] = global_vars;
        result["conflicts"] = conflicts;
        result["success"] = true;
        
    } catch (const std::exception& e) {
        result["success"] = false;
        result["error"] = e.what();
        std::cout << "Error analyzing Lua script: " << e.what() << std::endl;
    }
    
    return result;
}
