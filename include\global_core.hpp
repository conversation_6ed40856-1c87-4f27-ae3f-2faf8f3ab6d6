#ifndef GLOBAL_CORE_HPP
#define GLOBAL_CORE_HPP

#include <memory>
#include <string>
#include <functional>
#include <thread>
#include <atomic>
#include <chrono>
#include <mutex>
#include <condition_variable>
#include <set>
#include "common_types.hpp"
#include "variable_types.hpp"
#include "sol/sol.hpp"

/**
 * @brief 全局核心配置结构体
 * 基于engine_manager_config.json中的global_core字段，保留CPU和内存使用率监控
 */
struct GlobalCoreConfig {
    bool enabled = true;
    std::string monitor_name = "GlobalCore_Global";
    std::string global_core_package_path = "";
    std::string global_variable_system_path = "";
    std::string redis_host = "localhost";
    int redis_port = 6379;
    int redis_database = 0;
    int update_frequency = 100;  // 更新频率（毫秒）
    
    // 保留的系统监控功能
    double cpu_threshold = 80.0;      // CPU使用率阈值（百分比）
    double memory_threshold = 85.0;   // 内存使用率阈值（百分比）
    
    // 配置字符串保存
    std::string config_json_string = "";
    
    /**
     * @brief 检查配置是否有效
     * @return true 如果配置有效，false 否则
     */
    bool is_valid() const {
        return !monitor_name.empty() && 
               !global_core_package_path.empty() &&
               !global_variable_system_path.empty() &&
               !redis_host.empty() &&
               redis_port > 0 &&
               redis_database >= 0 &&
               update_frequency > 0 &&
               cpu_threshold > 0 && cpu_threshold <= 100 &&
               memory_threshold > 0 && memory_threshold <= 100;
    }
};

/**
 * @brief 全局核心模块（单例模式）
 * 
 * 负责监控系统级别状态，如PC系统状态、特定信号状态等
 * 与engine_core同层级，全局持续执行，全系统唯一实例
 */
class GlobalCore {
public:
    /**
     * @brief 获取单例实例
     * @return GlobalCore& 单例引用
     */
    static GlobalCore& get_instance();
    
    /**
     * @brief 析构函数
     */
    ~GlobalCore();
    
    // 禁用拷贝和移动
    GlobalCore(const GlobalCore&) = delete;
    GlobalCore& operator=(const GlobalCore&) = delete;
    GlobalCore(GlobalCore&&) = delete;
    GlobalCore& operator=(GlobalCore&&) = delete;
    
    /**
     * @brief 初始化全局核心模块
     * @param config 全局核心配置
     * @return ATE_EC 错误代码
     */
    ATE_EC initialize(const GlobalCoreConfig& config);
    
    /**
     * @brief 启动全局核心
     * @return ATE_EC 错误代码
     */
    ATE_EC start();
    
    /**
     * @brief 停止全局核心
     * @return ATE_EC 错误代码
     */
    ATE_EC stop();
    
    /**
     * @brief 暂停全局核心
     * @return ATE_EC 错误代码
     */
    ATE_EC suspend();
    
    /**
     * @brief 恢复全局核心
     * @return ATE_EC 错误代码
     */
    ATE_EC resume();
    
    /**
     * @brief 调试脚本执行
     * @param script_content 脚本内容
     * @return ATE_EC 错误代码
     */
    ATE_EC debug_script(const std::string& script_content);
    
    /**
     * @brief 清理资源
     * @return ATE_EC 错误代码
     */
    ATE_EC cleanup();
    
    /**
     * @brief FSM状态机回调处理
     * @return ATE_EC 错误代码
     */
    ATE_EC FSM_callback();
    
    /**
     * @brief 获取当前状态
     * @return FSMState 当前状态
     */
    FSMState get_state() const;

    /**
     * @brief 获取CPU使用率
     * @return double CPU使用率（百分比）
     */
    double get_cpu_usage() const;
    
    /**
     * @brief 获取内存使用率
     * @return double 内存使用率（百分比）
     */
    double get_memory_usage() const;
    
    // ========================================================================
    // Lua脚本执行控制接口
    // ========================================================================
    
    /**
     * @brief 开始执行Lua脚本
     * @param script_content 脚本内容
     * @param script_name 脚本名称
     * @return ATE_EC 错误代码
     */
    ATE_EC start_script_execution(const std::string& script_content, 
                                  const std::string& script_name);
    
    /**
     * @brief 停止脚本执行
     * @return ATE_EC 错误代码
     */
    ATE_EC stop_script_execution();
    
    /**
     * @brief 挂起脚本执行
     * @return ATE_EC 错误代码
     */
    ATE_EC suspend_script_execution();
    
    /**
     * @brief 恢复脚本执行
     * @return ATE_EC 错误代码
     */
    ATE_EC resume_script_execution();
    
    /**
     * @brief 在指定行设置调试断点并执行到断点
     * @param line_number 断点行号
     * @return ATE_EC 错误代码
     */
    ATE_EC debug_script_to_line(int line_number);
    
    /**
     * @brief 添加调试断点
     * @param breakpoint 断点信息
     * @return ATE_EC 错误代码
     */
    ATE_EC add_debug_breakpoint(const DebugBreakpoint& breakpoint);
    
    /**
     * @brief 移除调试断点
     * @param line_number 断点行号
     * @return ATE_EC 错误代码
     */
    ATE_EC remove_debug_breakpoint(int line_number);
    
    /**
     * @brief 清除所有断点
     */
    void clear_all_breakpoints();
    
    /**
     * @brief 获取脚本执行状态
     * @return LuaExecutionState 执行状态
     */
    LuaExecutionState get_script_execution_state() const;
    
    /**
     * @brief 获取执行上下文
     * @return LuaExecutionContext 执行上下文
     */
    LuaExecutionContext get_execution_context() const;
    
    /**
     * @brief Lua钩子函数处理
     * @param L Lua状态机
     * @param ar 调试信息
     */
    static void handle_lua_hook(lua_State* L, lua_Debug* ar);

private:
    /**
     * @brief 私有构造函数（单例模式）
     */
    GlobalCore();
    
    /**
     * @brief 监控线程函数
     */
    void monitor_thread_func();
    
    /**
     * @brief 更新系统状态
     */
    void update_system_status();
    
    /**
     * @brief 检查系统健康状态
     */
    void check_system_health();

    /**
     * @brief 设置状态
     * @param new_state 新状态
     */
    void set_state(FSMState new_state);
    
    /**
     * @brief 设置Lua绑定，配置index和newindex元方法
     */
    void setup_lua_binding();
    
    // ========================================================================
    // Lua钩子函数相关私有方法
    // ========================================================================
    
    /**
     * @brief 设置Lua钩子函数
     */
    void setup_lua_hook();
    
    /**
     * @brief 脚本执行线程函数
     * @param script_content 脚本内容
     * @param script_name 脚本名称
     */
    void script_execution_thread(const std::string& script_content, 
                                const std::string& script_name);
    
    /**
     * @brief 检查是否应该在指定行断点
     * @param line_number 行号
     * @return bool 是否应该断点
     */
    bool should_break_at_line(int line_number) const;
    
    /**
     * @brief Lua钩子回调函数
     * @param lua_state Lua状态机
     * @param debug_info 调试信息
     */
    static void lua_hook_call(lua_State* lua_state, lua_Debug* debug_info);
    
    std::atomic<FSMState> m_state{FSMState::TERMINATED}; ///< FSM状态
    std::unique_ptr<std::thread> m_monitor_thread;          ///< 监控线程
    std::chrono::milliseconds m_monitor_interval;           ///< 监控间隔
    std::function<void(const std::string&)> m_status_callback; ///< 状态回调函数
    GlobalCoreConfig m_config;                               ///< 全局核心配置
    
    // 系统状态监控
    mutable std::atomic<double> m_cpu_usage;                ///< CPU使用率
    mutable std::atomic<double> m_memory_usage;             ///< 内存使用率
    
    // Lua状态机
    sol::state m_lua;                                        ///< Lua状态机，用于全局变量操作
    
    // Lua脚本执行相关
    std::unique_ptr<LuaExecutionContext> m_execution_context; ///< 脚本执行上下文
    std::atomic<bool> m_stop_requested{false};              ///< 脚本停止请求
    std::atomic<bool> m_suspend_requested{false};           ///< 脚本挂起请求
    mutable std::mutex m_execution_mutex;                   ///< 脚本执行互斥锁
    std::condition_variable m_execution_cv;                 ///< 脚本执行条件变量
    std::set<DebugBreakpoint> m_breakpoints;                ///< 断点集合
    mutable std::mutex m_breakpoints_mutex;                 ///< 断点互斥锁
    std::unique_ptr<std::thread> m_execution_thread;        ///< 脚本执行线程
};

#endif // GLOBAL_CORE_HPP