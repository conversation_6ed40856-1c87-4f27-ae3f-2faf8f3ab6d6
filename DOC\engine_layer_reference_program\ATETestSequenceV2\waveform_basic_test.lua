-- ============================================================================
-- 波形基础测试序列 (二级子序列) - Step-Based Framework
-- 文件: waveform_basic_test.lua
-- 描述: 波形基础测试，包含pre、seq、post三个标签分区
-- 版本: V2.1 (标准化step-based工步控制框架)
-- ============================================================================

local step=0
local run_flag = {1, 1, 1, 1, 1, 1, 0, 0, 0, 0} -- 工步执行标志的数量和实际执行的数量相符合
local test_result="UNKNOWN"
local error_status = {}

-- 测试数据记录
local test_data_points = {}
local measurement_count = 0

-- 固定功能begin
function add_string(str)
    if not error_status[str] then
        error_status[str] = true
        return true
    end
    return false
end

-- 工步保护状态检查
function seq_protect_check()
    -- 检查示波器状态
    local scope_status = dll_query_command(main.device_channels.oscilloscope, "STATUS?")
    if scope_status == "ERROR" then
        add_string("示波器故障")
    end
    
    -- 检查信号发生器状态
    local generator_status = dll_query_command(main.device_channels.signal_generator, "STATUS?")
    if generator_status == "ERROR" then
        add_string("信号发生器故障")
    end
    
    -- 检查电源设备状态
    local power_status = dll_query_command(main.device_channels.power_supply, "STATUS?")
    if power_status == "ERROR" then
        add_string("电源设备故障")
    end
end

-- 工步保护动作执行
function seq_protect_act()
    if (next(error_status) ~= nil) then
        -- 发生故障时的保护动作
        dll_send_command(main.device_channels.power_supply, "OUTPUT:OFF")
        dll_send_command(main.device_channels.signal_generator, "OUTPUT:OFF")
        dll_send_command(main.device_channels.oscilloscope, "STOP")
        for error_key, _ in pairs(error_status) do
            log_message("ERROR", "[波形基测] 执行保护动作: " .. error_key)
        end
        -- 通过故障保护动作的组合返回做3种情况，只记录+不动作+继续执行，记录+动作+继续执行，记录+动作+停止执行
        -- return 1 动作后返回1 则动作了 后面停止执行
        -- return 2 动作了返回2 则动作了 后面继续执行
        return 1
    end
end
-- 固定功能end

-- 子序列全局变量定义
waveform_basic_test = {
    test_result = "UNKNOWN",
    test_name = "波形基测",
    start_time = 0,
    end_time = 0,
    step_count = 0,
    error_messages = {},
    test_data = {}
}

-- 测试参数配置
local test_params = {
    startup_voltage_min = 10.0,     -- 启动最小电压(V)
    startup_voltage_max = 15.0,     -- 启动最大电压(V)
    shutdown_voltage_min = 8.0,     -- 关机最小电压(V)
    startup_time_max = 5.0,         -- 最大启动时间(s)
    shutdown_time_max = 3.0,        -- 最大关机时间(s)
    measurement_interval = 0.1      -- 测量间隔(s)
}

function add_error_message(message)
    table.insert(waveform_basic_test.error_messages, message)
    log_message("ERROR", "[波形基测] " .. message)
end

-- ============================================================================
-- PRE 标签 - 预处理阶段
-- ============================================================================
::pre::
log_message("INFO", "[波形基测] === 开始波形基测预处理阶段 ===")
waveform_basic_test.start_time = os.time()
waveform_basic_test.step_count = 0
waveform_basic_test.error_messages = {}
waveform_basic_test.test_data = {}

-- 检查设备状态
if not device_initialized then
    add_error_message("设备未初始化，无法执行波形基测")
    waveform_basic_test.test_result = "FAILED"
    goto post
end

if not power_supply_ready then
    add_error_message("电源设备未就绪，无法执行波形基测")
    waveform_basic_test.test_result = "FAILED"
    goto post
end

-- 设置电源初始状态
log_message("INFO", "[波形基测] 设置电源初始状态...")
local power_off_result = dll_send_command(main.device_channels.power_supply, "OUTPUT:OFF")
if power_off_result ~= "OK" then
    add_error_message("无法关闭电源输出")
    waveform_basic_test.test_result = "FAILED"
    goto post
end

-- 配置电源参数
log_message("INFO", "[波形基测] 配置电源参数...")
dll_send_command(main.device_channels.power_supply, "VOLTAGE:12.0")
dll_send_command(main.device_channels.power_supply, "CURRENT:5.0")
dll_send_command(main.device_channels.power_supply, "OVP:15.0")
dll_send_command(main.device_channels.power_supply, "OCP:6.0")

-- 配置示波器
log_message("INFO", "[波形基测] 配置示波器...")
dll_send_command(main.device_channels.oscilloscope, "TIMEBASE:SCALE:0.5")
dll_send_command(main.device_channels.oscilloscope, "CHANNEL1:SCALE:5.0")
dll_send_command(main.device_channels.oscilloscope, "CHANNEL2:SCALE:2.0")
dll_send_command(main.device_channels.oscilloscope, "TRIGGER:SOURCE:CHANNEL1")
dll_send_command(main.device_channels.oscilloscope, "TRIGGER:LEVEL:10.0")

-- 等待设备稳定
log_message("INFO", "[波形基测] 等待设备稳定...")
os.execute("timeout /t 2 /nobreak > nul")

log_message("INFO", "[波形基测] === 波形基测预处理阶段完成 ===")

-- ============================================================================
-- SEQ 标签 - 测试序列阶段
-- ============================================================================
::seq::
-- 需要执行中通过公式二次计算的量在这里，通过公式计算再次生效
-- 每个工步的合适位置，在这一层代码中的合适位置进行故障监测，HOOK只由于debug，生产环境极其不建议使用
-- 每个工步的内容规划：源、载的动作，示波器，万用表，功率计等设备的模式配置，条件判断，例如指定时间内
-- 判断某一个电压或者电流是否有波动，是否在指定范围内，运行一定时间进行某变量的计算等，这些内容都在下面
-- 这一层代码时间，这一层代码调用的就是DLL外设导入到lua的可执行函数和命令，这一段代码::seq::是脚本中最
-- 复杂的部分！！若工步就是延时功能或者工步具有时间的条件判断要将时间分隔，每个间隔都执行故障监测代码
-- seq_protect_check() if (seq_protect_act() == 1) then goto post end

if (run_flag[1] ~= 0) then -- 当前编号动作执行
    step=1
    log_message("INFO", "[波形基测] 工步1: 开机波形测试准备")
    waveform_basic_test.step_count = waveform_basic_test.step_count + 1
    
    -- 启动示波器采集
    local scope_start_result = dll_send_command(main.device_channels.oscilloscope, "RUN")
    dll_send_command(main.device_channels.oscilloscope, "TRIGGER:MODE:SINGLE")
    
    if scope_start_result == "OK" then
        log_message("INFO", "[波形基测] 示波器启动采集成功")
        test_result="PASSED"
    else
        add_error_message("示波器启动采集失败")
        test_result="FAILED"
    end
    
    -- 每个工步的合适位置都进行故障监测
    seq_protect_check()
    -- 监测到故障后跳转
    if (seq_protect_act() == 1) then goto post end
end

if (run_flag[2] ~= 0) then -- 当前编号动作执行
    step=2
    log_message("INFO", "[波形基测] 工步2: 开机波形测试执行")
    waveform_basic_test.step_count = waveform_basic_test.step_count + 1
    
    -- 开启电源输出
    local startup_begin_time = os.clock()
    local power_on_result = dll_send_command(main.device_channels.power_supply, "OUTPUT:ON")
    
    if power_on_result ~= "OK" then
        add_error_message("电源开启失败")
        test_result="FAILED"
    else
        -- 监测启动过程
        local startup_completed = false
        local startup_voltage = 0
        local measurement_count = 0
        
        while (os.clock() - startup_begin_time) < test_params.startup_time_max do
            -- 读取电压值
            local voltage_str = dll_query_command(main.device_channels.power_supply, "MEASURE:VOLTAGE?")
            startup_voltage = tonumber(voltage_str) or 0
            
            -- 读取电流值
            local current_str = dll_query_command(main.device_channels.power_supply, "MEASURE:CURRENT?")
            local startup_current = tonumber(current_str) or 0
            
            -- 检查启动完成条件
            if startup_voltage >= test_params.startup_voltage_min and startup_voltage <= test_params.startup_voltage_max then
                startup_completed = true
                log_message("INFO", string.format("[波形基测] 启动完成，电压: %.2fV，时间: %.2fs", startup_voltage, os.clock() - startup_begin_time))
                break
            end
            
            measurement_count = measurement_count + 1
            -- 延时begin
            local x = test_params.measurement_interval * 1000
            local sleepms = 10  -- 每次循环基础延时
            local loops = math.floor(x / sleepms)  -- 完整循环次数
            local remainder = x % sleepms  -- 剩余延时
            -- 执行完整循环延时
            for i = 1, loops do
                -- 每个时间段都执行一次要做的连续判断
                -- 休眠循环基础延时 sleep_ms 采用C++注册的方式，注册一个通用的接口给lua
                sleep_ms(sleepms)
                seq_protect_check() if (seq_protect_act() == 1) then goto post end
            end
            -- 执行剩余延时
            if remainder > 0 then
                -- 每个时间段都执行一次要做的连续判断
                -- 休眠毫秒剩余
                sleep_ms(remainder)
                seq_protect_check() if (seq_protect_act() == 1) then goto post end
            end
            -- 延时end
        end
        
        -- 检查启动结果
        if startup_completed then
            test_result="PASSED"
        else
            add_error_message(string.format("启动超时或电压异常，当前电压: %.2fV", startup_voltage))
            test_result="FAILED"
        end
    end
    
    -- 每个工步的合适位置都进行故障监测
    seq_protect_check()
    -- 监测到故障后跳转
    if (seq_protect_act() == 1) then goto post end
end

if (run_flag[3] ~= 0) then -- 当前编号动作执行
    step=3
    log_message("INFO", "[波形基测] 工步3: 关机波形测试")
    waveform_basic_test.step_count = waveform_basic_test.step_count + 1
    
    -- 延时begin
    local x = 2000
    local sleepms = 10  -- 每次循环基础延时
    local loops = math.floor(x / sleepms)  -- 完整循环次数
    local remainder = x % sleepms  -- 剩余延时
    -- 执行完整循环延时
    for i = 1, loops do
        -- 每个时间段都执行一次要做的连续判断
        -- 休眠循环基础延时 sleep_ms 采用C++注册的方式，注册一个通用的接口给lua
        sleep_ms(sleepms)
        seq_protect_check() if (seq_protect_act() == 1) then goto post end
    end
    -- 执行剩余延时
    if remainder > 0 then
        -- 每个时间段都执行一次要做的连续判断
        -- 休眠毫秒剩余
        sleep_ms(remainder)
        seq_protect_check() if (seq_protect_act() == 1) then goto post end
    end
    -- 延时end
    
    -- 启动示波器采集
    dll_send_command(main.device_channels.oscilloscope, "TRIGGER:MODE:SINGLE")
    
    -- 关闭电源输出
    local shutdown_begin_time = os.clock()
    local power_off_result = dll_send_command(main.device_channels.power_supply, "OUTPUT:OFF")
    
    if power_off_result ~= "OK" then
        add_error_message("电源关闭失败")
        test_result="FAILED"
    else
        -- 监测关机过程
        local shutdown_completed = false
        local shutdown_voltage = 0
        
        while (os.clock() - shutdown_begin_time) < test_params.shutdown_time_max do
            -- 读取电压值
            local voltage_str = dll_query_command(main.device_channels.power_supply, "MEASURE:VOLTAGE?")
            shutdown_voltage = tonumber(voltage_str) or 0
            
            -- 读取电流值
            local current_str = dll_query_command(main.device_channels.power_supply, "MEASURE:CURRENT?")
            local shutdown_current = tonumber(current_str) or 0
            
            -- 检查关机完成条件
            if shutdown_voltage <= test_params.shutdown_voltage_min then
                shutdown_completed = true
                log_message("INFO", string.format("[波形基测] 关机完成，电压: %.2fV，时间: %.2fs", shutdown_voltage, os.clock() - shutdown_begin_time))
                break
            end
            
            -- 延时begin
            local x = test_params.measurement_interval * 1000
            local sleepms = 10  -- 每次循环基础延时
            local loops = math.floor(x / sleepms)  -- 完整循环次数
            local remainder = x % sleepms  -- 剩余延时
            -- 执行完整循环延时
            for i = 1, loops do
                -- 每个时间段都执行一次要做的连续判断
                -- 休眠循环基础延时 sleep_ms 采用C++注册的方式，注册一个通用的接口给lua
                sleep_ms(sleepms)
                seq_protect_check() if (seq_protect_act() == 1) then goto post end
            end
            -- 执行剩余延时
            if remainder > 0 then
                -- 每个时间段都执行一次要做的连续判断
                -- 休眠毫秒剩余
                sleep_ms(remainder)
                seq_protect_check() if (seq_protect_act() == 1) then goto post end
            end
            -- 延时end
        end
        
        -- 检查关机结果
        if shutdown_completed then
            test_result="PASSED"
        else
            add_error_message(string.format("关机超时或电压异常，当前电压: %.2fV", shutdown_voltage))
            test_result="FAILED"
        end
    end
    
    -- 每个工步的合适位置都进行故障监测
    seq_protect_check()
    -- 监测到故障后跳转
    if (seq_protect_act() == 1) then goto post end
end

if (run_flag[4] ~= 0) then -- 当前编号动作执行
    step=4
    log_message("INFO", "[波形基测] 工步4: 波形数据分析")
    waveform_basic_test.step_count = waveform_basic_test.step_count + 1
    
    -- 停止示波器采集
    dll_send_command(main.device_channels.oscilloscope, "STOP")
    
    -- 获取波形数据
    local waveform_ch1 = dll_query_command(main.device_channels.oscilloscope, "WAVEFORM:DATA? CHANNEL1")
    local waveform_ch2 = dll_query_command(main.device_channels.oscilloscope, "WAVEFORM:DATA? CHANNEL2")
    
    if waveform_ch1 == "ERROR" or waveform_ch2 == "ERROR" then
        add_error_message("无法获取示波器波形数据")
        test_result="FAILED"
    else
        log_message("INFO", "[波形基测] 波形数据获取成功，开始详细分析...")
        
        -- 调用波形分析详细测试脚本
        dofile("ATETestSequenceV2/waveform_analysis_detail.lua")
        
        -- 检查波形分析结果
        if waveform_analysis_detail.test_result == "PASS" then
            log_message("INFO", "[波形基测] 波形分析通过")
            test_result="PASSED"
        else
            add_error_message("波形分析失败")
            -- 将波形分析的错误信息合并到基测错误列表中
            for _, error_msg in ipairs(waveform_analysis_detail.error_messages) do
                add_error_message("波形分析: " .. error_msg)
            end
            test_result="FAILED"
        end
    end
    
    -- 每个工步的合适位置都进行故障监测
    seq_protect_check()
    -- 监测到故障后跳转
    if (seq_protect_act() == 1) then goto post end
end

log_message("INFO", "[波形基测] === 波形基测序列阶段完成 ===")

-- ============================================================================
-- POST 标签 - 后处理阶段
-- ============================================================================
::post::
log_message("INFO", "[波形基测] === 开始波形基测后处理阶段 ===")
waveform_basic_test.end_time = os.time()

-- 确保电源输出关闭
dll_send_command(main.device_channels.power_supply, "OUTPUT:OFF")
log_message("INFO", "[波形基测] 电源输出已关闭")

-- 停止示波器
dll_send_command(main.device_channels.oscilloscope, "STOP")
log_message("INFO", "[波形基测] 示波器已停止")

-- 生成测试报告
local test_duration = waveform_basic_test.end_time - waveform_basic_test.start_time
local data_points = #waveform_basic_test.test_data
local completed_steps = step - 1
local total_steps = 4
local step_completion_rate = (completed_steps / total_steps) * 100

-- 确定最终测试结果
if #waveform_basic_test.error_messages == 0 and completed_steps >= total_steps then
    waveform_basic_test.test_result = "PASS"
else
    waveform_basic_test.test_result = "FAILED"
end

-- 写入测试报告到JSON文件
local function write_waveform_json_report()
    -- 构建错误状态列表字符串
    local error_list_str = ""
    if next(error_status) ~= nil then
        local error_list = {}
        for error_key, _ in pairs(error_status) do
            table.insert(error_list, error_key)
        end
        error_list_str = table.concat(error_list, ", ")
    end
    
    -- 构建子测试结果JSON字符串（波形基测作为单独测试）
    local sub_test_json = string.format('{"%s":"%s"}', "waveform_basic_test", waveform_basic_test.test_result)
    
    -- 调用C++接口写入JSON文件
    write_test_report_json(
        waveform_basic_test.test_result,
        test_duration,
        completed_steps,
        total_steps,
        #waveform_basic_test.error_messages,
        0, -- warning_count，波形基测中没有警告计数
        os.date("%Y-%m-%d %H:%M:%S"),
        error_list_str,
        sub_test_json
    )
    
    log_message("INFO", "[波形基测] 波形基测报告已写入JSON文件")
end

-- 执行JSON报告写入
write_waveform_json_report()

log_message("INFO", "[波形基测] === 波形基测后处理阶段完成 ===")
log_message("INFO", string.format("[波形基测] 波形基测最终结果: %s", waveform_basic_test.test_result))

-- 返回测试结果
return waveform_basic_test.test_result