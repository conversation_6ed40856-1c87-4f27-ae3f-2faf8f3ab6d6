// =============================================================================
// E5000 DLL Interface Implementation
// E5000电子负载设备DLL接口实现文件
// 提供设备连接、命令执行、状态查询等核心功能
// 符合C++编码规范和最佳实践的实现文件
// =============================================================================

// 定义EthClient_DLL_EXPORTS以确保正确的导出声明
#ifndef EthClient_DLL_EXPORTS
#define EthClient_DLL_EXPORTS
#include <cstdint>
#include <string>
#endif

#include "eth_client_interface.hpp"
#include "device_manager.hpp"
#include "config_manager.hpp"
#include <memory>
#include <unordered_map>
#include <mutex>
#include <cstring>
#include <fstream>
#include <thread>
#include <chrono>
#include <sstream>
#include <iomanip>
#include <nlohmann/json.hpp>

// =============================================================================
// 全局变量和状态管理
// =============================================================================

namespace {
    // 全局状态管理
    std::unique_ptr<EthClient::DeviceManager> g_device_manager;  // 设备管理器实例，负责所有设备的生命周期管理
    std::mutex g_dll_mutex;                                  // DLL级别的互斥锁，保护全局状态
    std::string g_last_error;                               // 最后一次错误信息，用于调试
    bool g_initialized = false;                             // DLL初始化状态标志
    
    // 设备实例映射：通道号 -> 设备实例ID
    std::unordered_map<int, std::string> g_device_index_to_instance_id;
}

// =============================================================================
// 辅助函数
// =============================================================================
namespace {
    /**
     * 设置最后错误信息
     * @param error 错误描述字符串
     */
    void m_set_last_error(const std::string& error) {
        g_last_error = error;
    }
    
    /**
     * 从已连接设备映射中获取实例ID
     * @param device_index 设备索引（通道号）
     * @return 设备实例ID，未找到返回空字符串
     */
    std::string m_get_instance_id_from_index(int device_index) {
        auto it = g_device_index_to_instance_id.find(device_index);
        if (it != g_device_index_to_instance_id.end()) {
            return it->second;
        }
        return "";
    }
    
    /**
     * 从配置文件中获取设备实例ID
     * @param device_index 设备索引（通道号）
     * @return 配置文件中对应的设备实例ID，未找到返回空字符串
     */
    std::string m_get_instance_id_from_configs(int device_index) {
        std::string finded_instance_id =  g_device_manager->get_config_manager()->get_instance_id_by_device_index(static_cast<uint32_t>(device_index));
        if(!finded_instance_id.empty()){
            return finded_instance_id;
        }
        return "";
    }

    /**
     * 设备参数写入函数
     * 通过设备管理器向指定设备写入参数值
     * @param instance_id 设备实例ID
     * @param address Modbus寄存器地址
     * @param value 要写入的参数值
     * @param error_code 输出参数，错误码
     * @param error_message 输出参数，错误描述
     * @param command_description 命令描述，用于错误信息
     * @return 成功返回true，失败返回false
     */
    bool m_write_device(const std::string& instance_id, int& error_code, std::string& error_message, const std::string& hex_string) {
        ATE_EC last_result = ATE_ERROR_UNKNOWN;
        std::string last_error_msg;
        
        
            try {
                // 获取设备管理器实例
                if (!g_device_manager) {
                    error_code = -1;
                    error_message = "Device manager not initialized";
                    return false;
                }
                
                // 通过设备管理器写入参数
                ATE_EC result = g_device_manager->write_command_data(instance_id,  hex_string);
                if (result == ATE_SUCCESS) {
                    return true;
                }
            } catch (const std::exception& e) {
                last_error_msg = "Exception in parameter write " + hex_string + std::string(e.what());
            }

        error_message = last_error_msg ;
        return false;
    }

    bool m_read_device(const std::string& instance_id, int& error_code, std::string& error_message, uint8_t* buffer, int32_t buffer_size, int32_t* response_size) {
        ATE_EC last_result = ATE_ERROR_UNKNOWN;
        std::string last_error_msg;
        
        
            try {
                // 获取设备管理器实例
                if (!g_device_manager) {
                    error_code = -1;
                    error_message = "Device manager not initialized";
                    return false;
                }
                
                // 通过设备管理器写入参数
                ATE_EC result = g_device_manager->read_received_buffer_data(instance_id, buffer, buffer_size, response_size);
                if (result == ATE_SUCCESS) {
                    return true;
                }
            } catch (const std::exception& e) {
                last_error_msg = "Exception in parameter write " + std::string(e.what());
            }

        error_message = last_error_msg ;
        return false;
    }    
    
    /**
     * 执行写入命令
     * 解析命令字符串并执行相应的设备控制操作
     * 支持的命令格式："CC=1.5;CV=12.0;ONOFF=ON"
     * @param instance_id 设备实例ID
     * @param command 命令字符串，支持CC/CV/CP/CR参数设置和ONOFF电源控制
     * @param error_code 输出参数，错误码
     * @param error_message 输出参数，错误描述
     * @return 成功返回true，失败返回false
     * @note 基于command_system.csv和Delphi m_IoCtl实现
     */
    bool m_execute_command(const std::string& instance_id, const std::string& command, int& error_code, std::string& error_message, char * response, int buffer_size, int * response_size) {
        try {
          
            // 将命令解析为键值对
            std::vector<std::pair<std::string, std::string>> cmd_pairs;
            std::stringstream ss(command);
            std::string item;
            
            while (std::getline(ss, item, ';')) {
                size_t eq_pos = item.find('=');
                if (eq_pos != std::string::npos) {
                    std::string key = item.substr(0, eq_pos);
                    std::string value = item.substr(eq_pos + 1);
                    cmd_pairs.push_back({key, value});
                }
            }
            
            // 处理每个命令对
            for (const auto& pair : cmd_pairs) {
                const std::string& cmd_name = pair.first;
                const std::string& cmd_value = pair.second;
                
                // 写入命令，调用tcp_udp_client,通过socket发送以太网数据
                if (cmd_name == "Write") {
                  double voltage_value = std::stod(cmd_value);
                  if (!m_write_device(instance_id, error_code, error_message, cmd_value)) {
                    return false;
                  }
                } else if (cmd_name == "Read") {
                  if (cmd_value == "ReadAll") {
                    if (!m_read_device(instance_id, error_code, error_message, reinterpret_cast<uint8_t*>(response), buffer_size, response_size)) {
                      return false;
                    }
                  }
                }
            }
            return true;
        } catch (const std::exception& e) {
            error_code = -1;
            error_message = "Exception in command execution: " + std::string(e.what());
            return false;
        }
    }
}

// =============================================================================
// DLL导出函数实现 - 按照头文件中的声明顺序
// =============================================================================

/**
 * 初始化DLL资源
 * 创建并初始化设备管理器，加载配置文件，准备设备连接环境
 * @return 成功返回ATE_SUCCESS(0)，失败返回对应的ATE_EC错误码
 * @note 必须在调用其他DLL函数之前先调用此函数
 * @note 重复调用此函数是安全的，会直接返回成功
 * @note 线程安全：使用全局互斥锁保护
 */
EXP_API ATE_EC initialize_dll() {
    std::lock_guard<std::mutex> lock(g_dll_mutex);
    
    if (g_initialized) {
        return ATE_SUCCESS;
    }
    
    try {
        // 创建设备管理器
        auto config_manager = std::make_unique<EthClient::ConfigManager>(); // 初始化配置文件路径信息
        g_device_manager = std::make_unique<EthClient::DeviceManager>(std::move(config_manager)); // 转移配置管理器所有权，读配置文件只初始化
        
        // 初始化设备管理器
        ATE_EC result = g_device_manager->initialize();
        if (result != ATE_SUCCESS) {
            g_device_manager.reset();
            m_set_last_error("Failed to initialize device manager");
            return result;
        }
        
        g_initialized = true;
        return ATE_SUCCESS;
        
    } catch (const std::exception& e) {
        g_device_manager.reset();
        m_set_last_error("Exception during initialization: " + std::string(e.what()));
        return ATE_ERROR_UNKNOWN;
    }
}

/**
 * 清理DLL资源
 * 断开所有设备连接，释放设备管理器资源，清理内部缓存
 * @return 成功返回ATE_SUCCESS(0)，失败返回对应的ATE_EC错误码
 * @note 程序退出前应调用此函数进行资源清理
 * @note 清理后需要重新调用initialize_dll()才能使用其他功能
 * @note 线程安全：使用全局互斥锁保护
 */
EXP_API ATE_EC cleanup_dll() {
    std::lock_guard<std::mutex> lock(g_dll_mutex);
    
    if (!g_initialized) {
        return ATE_SUCCESS;
    }
    
    try {
        // 清理设备管理器
        if (g_device_manager) {
            g_device_manager->shutdown();
            g_device_manager.reset();
        }
        
        // 清理缓存
        g_device_index_to_instance_id.clear();
        
        g_initialized = false;
        return ATE_SUCCESS;
        
    } catch (const std::exception& e) {
        m_set_last_error("Exception during cleanup: " + std::string(e.what()));
        return ATE_ERROR_UNKNOWN;
    }
}

/**
 * 连接设备
 * 根据通道号从配置文件中查找对应的设备实例，建立Modbus TCP连接
 * @param ChannelNo 通道号，对应配置文件中的device_index
 * @return 成功返回ATE_SUCCESS(0)，失败返回对应的ATE_EC错误码
 * @note 连接成功后会启动设备数据采集线程
 * @note 如果设备已连接，返回ATE_ERROR_DEVICE_ALREADY_EXISTS
 * @note 线程安全：使用全局互斥锁保护
 */
EXP_API ATE_EC connect_device(const int32_t ChannelNo) {
    std::lock_guard<std::mutex> lock(g_dll_mutex);
    
    if (!g_initialized || !g_device_manager) {
        m_set_last_error("DLL not initialized");
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    try {
        // 检查设备是否已经连接
        std::string existing_instance_id = m_get_instance_id_from_index(ChannelNo);
        if (!existing_instance_id.empty()) {
            m_set_last_error("Device already connected");
            return ATE_ERROR_DEVICE_ALREADY_EXISTS;
        }

        // 查找配置文件中的实例ID
        std::string instance_id = m_get_instance_id_from_configs(ChannelNo);
        if(instance_id.empty()){
            m_set_last_error("Device index not found in configuration");
            return ATE_ERROR_DEVICE_NOT_FOUND;
        }
        
        // 连接设备
        ATE_EC result = g_device_manager->connect_device(instance_id);
        if (result == ATE_SUCCESS) {
            // 保存设备索引到实例ID的映射
            g_device_index_to_instance_id[ChannelNo] = instance_id;
        } else {
            m_set_last_error("Failed to connect device");
        }
        
        return result;
        
    } catch (const std::exception& e) {
        m_set_last_error("Exception during device connection: " + std::string(e.what()));
        return ATE_ERROR_UNKNOWN;
    }
}

/**
 * 断开设备连接
 * 停止设备数据采集线程，关闭Modbus TCP连接，清理相关资源
 * @param ChannelNo 通道号，对应配置文件中的device_index
 * @return 成功返回ATE_SUCCESS(0)，失败返回对应的ATE_EC错误码
 * @note 断开连接后会清理该设备的响应缓存和索引映射
 * @note 线程安全：使用全局互斥锁保护
 */
EXP_API ATE_EC disconnect_device(const int32_t ChannelNo) {
    std::lock_guard<std::mutex> lock(g_dll_mutex);
    
    if (!g_initialized || !g_device_manager) {
        m_set_last_error("DLL not initialized");
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    try {
        // 获取设备实例ID
        std::string instance_id = m_get_instance_id_from_index(ChannelNo);
        if (instance_id.empty()) {
            m_set_last_error("Device not found");
            return ATE_ERROR_DEVICE_NOT_FOUND;
        }
        
        // 断开设备连接
        ATE_EC result = g_device_manager->disconnect_device(instance_id);
        if (result == ATE_SUCCESS) {
            // 移除设备索引映射
            g_device_index_to_instance_id.erase(ChannelNo);
        } else {
            m_set_last_error("Failed to disconnect device");
        }
        
        return result;
        
    } catch (const std::exception& e) {
        m_set_last_error("Exception during device disconnection: " + std::string(e.what()));
        return ATE_ERROR_UNKNOWN;
    }
}

/**
 * 获取设备描述符
 * 返回设备的配置信息JSON字符串，包含设备类型、通信参数等信息
 * @param ChannelNo 通道号，对应配置文件中的device_index
 * @param device_descriptor 输出缓冲区，用于存储设备描述符JSON字符串
 * @param buffer_size 输入缓冲区大小
 * @param device_descriptor_size 输出参数，返回实际描述符JSON字符串长度
 * @return 成功返回ATE_SUCCESS(0)，失败返回对应的ATE_EC错误码
 * @note 如果缓冲区太小，返回ATE_ERROR_BUFFER_TOO_SMALL并设置所需大小
 * @note 设备无需连接即可获取描述符信息
 * @note 线程安全：使用全局互斥锁保护
 */
EXP_API ATE_EC get_device_descriptor(const int32_t ChannelNo, char* device_descriptor, const int32_t buffer_size, int32_t* device_descriptor_size) {
    // 基本参数检查
    if (!device_descriptor || !device_descriptor_size || buffer_size <= 0) {
        m_set_last_error("Invalid parameters");
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    // 检查DLL是否已初始化
    if (!g_initialized || !g_device_manager) {
        m_set_last_error("DLL not initialized");
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    // 获取设备instance_id
    std::string instance_id = m_get_instance_id_from_index(ChannelNo);
    if (instance_id.empty()) {
        instance_id = m_get_instance_id_from_configs(ChannelNo);
    }
    
    if (instance_id.empty()) {
        m_set_last_error("Device not found for ChannelNo: " + std::to_string(ChannelNo));
        return ATE_ERROR_DEVICE_NOT_FOUND;
    }
    
    try {
        // 获取设备配置
        EthClient::DeviceConfig config;
        if (!g_device_manager->get_device_config(instance_id, config)) {
            m_set_last_error("Failed to get device config");
            return ATE_ERROR_DEVICE_NOT_FOUND;
        }
        
        // 检查缓冲区大小
        int32_t required_size = static_cast<int32_t>(config.config_json.length()) + 1;
        if (buffer_size < required_size) {
            *device_descriptor_size = required_size;
            m_set_last_error("Buffer too small");
            return ATE_ERROR_BUFFER_TOO_SMALL;
        }
        
        // 复制配置JSON到输出参数
        strcpy(device_descriptor, config.config_json.c_str());
        *device_descriptor_size = static_cast<int32_t>(config.config_json.length());
        return ATE_SUCCESS;
        
    } catch (const std::exception& e) {
        m_set_last_error("Exception: " + std::string(e.what()));
        return ATE_ERROR_UNKNOWN;
    }
}

/**
 * 统一命令执行接口
 * 支持写入命令和查询命令两种模式，提供统一的设备控制接口
 * @param channel_no 通道号，对应配置文件中的device_index
 * @param mode 命令模式：COMMAND_WRITE_ONLY(写入)或COMMAND_QUERY(查询)
 * @param command 命令字符串，写入模式支持"CC=1.5;CV=12.0;ONOFF=ON"格式
 * @param command_length 命令或二进制数据的长度
 * @param timeout_ms 超时时间(毫秒)
 * @param response 响应缓冲区，用于存储命令执行结果或查询数据
 * @param buffer_size 输入缓冲区大小
 * @param response_size 输出参数，返回实际响应数据长度
 * @return 成功返回ATE_SUCCESS(0)，失败返回对应的ATE_EC错误码
 * @note 写入模式：解析命令并执行设备参数设置
 * @note 查询模式：返回当前设备状态的JSON数据
 * @note 设备必须已连接才能执行命令
 * @note 线程安全：使用全局互斥锁保护
 */
EXP_API ATE_EC execute_command_unified(const int32_t channel_no, const E5000_CommandMode mode, const char* command, const int32_t command_length, const int32_t timeout_ms, char* response, const int32_t buffer_size, int32_t* response_size) {
    // 参数验证
    if (!command) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    if (buffer_size <= 0) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    if (command_length < 0) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    if (!g_device_manager) {
        return ATE_ERROR_DEVICE_NOT_FOUND;
    }
    std::string instance_id = m_get_instance_id_from_index(channel_no);
    if (instance_id.empty()) {
        return ATE_ERROR_DEVICE_NOT_FOUND;
    }
    
    // 执行命令的核心逻辑
    bool success = false;
    int error_code = 0;
    std::string error_message = "";
    
    try {
        switch (mode) {
            case COMMAND_WRITE_ONLY: {
                // 通过设备独立线程执行写入命令
                std::string cmd_str;
                if (command_length > 0) {
                    cmd_str = std::string(command, command_length);
                } else {
                    cmd_str = std::string(command);
                }

                auto bResult = m_execute_command(instance_id, cmd_str, error_code, error_message, response, buffer_size, response_size);
                if (bResult == true) {
                    return ATE_SUCCESS;
                } else {
                    return ATE_ERROR_COMMAND_FAILED;
                }
                break;
            }
            case COMMAND_READ_ONLY:{
                // 通过设备独立线程执行读命令
                std::string cmd_str;
                if (command_length > 0) {
                    cmd_str = std::string(command, command_length);
                } else {
                    cmd_str = std::string(command);
                }

                auto bResult = m_execute_command(instance_id, cmd_str, error_code, error_message, response, buffer_size, response_size);
                if (bResult == true) {
                    return ATE_SUCCESS;
                } else {
                    return ATE_ERROR_COMMAND_FAILED;
                }    
                break;
            }
            case COMMAND_QUERY: {
               
                break;
            }
            case COMMAND_DYNAIC_CONFIG:{
                // 动态配置命令
                break;
            }            
            default: {
                error_code = -1;
                error_message = "Unsupported command mode";
                success = false;
                break;
            }
        }
    } catch (const std::exception& e) {
        success = false;
        error_code = -1;
        error_message = "Exception: " + std::string(e.what());
    }
    
    return error_code == 0 ? ATE_SUCCESS : static_cast<ATE_EC>(error_code);
}

/**
 * 获取通道状态
 * 返回设备当前状态的完整JSON数据，包含E5000DataBlockV2结构体的所有字段
 * @param ChannelNo 通道号，对应配置文件中的device_index
 * @param channel_state 输出缓冲区，用于存储通道状态JSON字符串
 * @param buffer_size 输入缓冲区大小
 * @param channel_state_size 输出参数，返回实际JSON字符串长度
 * @return 成功返回ATE_SUCCESS(0)，失败返回对应的ATE_EC错误码
 * @note 返回的JSON数据包含E5000DataBlockV2结构体的所有字段
 * @note 如果缓冲区太小，返回ATE_ERROR_BUFFER_TOO_SMALL并设置所需大小
 * @note 设备必须已连接才能获取状态数据
 * @note 线程安全：使用设备级互斥锁保护数据读取
 */
EXP_API ATE_EC get_channel_state(const int32_t channel_no, char* channel_state, const int32_t buffer_size, int32_t* channel_state_size) {
    // 基本参数检查
    if (!channel_state || !channel_state_size || buffer_size <= 0) {
        m_set_last_error("Invalid parameters");
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    // 检查DLL是否已初始化
    if (!g_initialized || !g_device_manager) {
        m_set_last_error("DLL not initialized");
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    // 获取设备instance_id
    std::string instance_id = m_get_instance_id_from_index(channel_no);
    if (instance_id.empty()) {
        instance_id = m_get_instance_id_from_configs(channel_no);
    }
    
    if (instance_id.empty()) {
        m_set_last_error("Device not found for channel: " + std::to_string(channel_no));
        return ATE_ERROR_DEVICE_NOT_FOUND;
    }
    
    try {
        // 获取通道状态JSON数据
        std::string json_state = g_device_manager->read_channel_state(instance_id.c_str(), channel_no);
        
        // 检查缓冲区大小
        int32_t required_size = static_cast<int32_t>(json_state.length()) + 1;
        if (buffer_size < required_size) {
            *channel_state_size = required_size;
            m_set_last_error("Buffer too small");
            return ATE_ERROR_BUFFER_TOO_SMALL;
        }
        
        // 复制JSON数据到输出参数
        strcpy(channel_state, json_state.c_str());
        *channel_state_size = static_cast<int32_t>(json_state.length());
        return ATE_SUCCESS;
        
    } catch (const std::exception& e) {
        m_set_last_error("Exception: " + std::string(e.what()));
        return ATE_ERROR_UNKNOWN;
    }
}
