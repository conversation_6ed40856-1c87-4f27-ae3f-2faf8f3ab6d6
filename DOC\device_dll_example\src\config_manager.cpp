#include "config_manager.hpp"
#include "eth_client_interface.hpp"
#include <fstream>
#include <iostream>
#include <sstream>
#include <filesystem>
#include <chrono>
#include <iostream>
#include <windows.h>

namespace EthClient {

// 静态函数用于获取当前模块句柄
static std::string get_dll_based_config_path() {
    HMODULE hModule = nullptr;
    if (GetModuleHandleExW(GET_MODULE_HANDLE_EX_FLAG_FROM_ADDRESS | GET_MODULE_HANDLE_EX_FLAG_UNCHANGED_REFCOUNT,
                          reinterpret_cast<LPCWSTR>(&get_dll_based_config_path), &hModule)) {
        wchar_t dll_path[MAX_PATH];
        if (GetModuleFileNameW(hModule, dll_path, MAX_PATH)) {
            std::filesystem::path dll_file_path(dll_path);
            std::string dll_name = dll_file_path.stem().string();
            std::string config_file_name = dll_name + ".json";
            std::filesystem::path config_path = dll_file_path.parent_path() / config_file_name;
            return config_path.string();
        }
    }
    return "";
}

ConfigManager::ConfigManager(): m_config_loaded(false) {

  std::string dll_config_path = get_dll_based_config_path();
  if (!dll_config_path.empty()) {
    m_config_file_path = dll_config_path;
  }

}

ConfigManager::ConfigManager(ConfigManager&& other) noexcept
    : m_config_file_path(std::move(other.m_config_file_path))
    , m_driver_info(std::move(other.m_driver_info))
    , m_device_configs(std::move(other.m_device_configs))
    , m_performance_config(std::move(other.m_performance_config))
    , m_command_system_config(std::move(other.m_command_system_config))
    , m_config_loaded(other.m_config_loaded.load())
    , m_last_error(std::move(other.m_last_error))
    , m_validation_errors(std::move(other.m_validation_errors))
    , m_device_config_index(std::move(other.m_device_config_index))
    , m_command_index(std::move(other.m_command_index)) {
}

ConfigManager& ConfigManager::operator=(ConfigManager&& other) noexcept {
    if (this != &other) {
        std::lock_guard<std::mutex> lock(m_config_mutex);
        
        m_config_file_path = std::move(other.m_config_file_path);
        m_driver_info = std::move(other.m_driver_info);
        m_device_configs = std::move(other.m_device_configs);
        m_performance_config = std::move(other.m_performance_config);
        m_command_system_config = std::move(other.m_command_system_config);
        m_config_loaded = other.m_config_loaded.load();
        m_last_error = std::move(other.m_last_error);
        m_validation_errors = std::move(other.m_validation_errors);
        m_device_config_index = std::move(other.m_device_config_index);
        m_command_index = std::move(other.m_command_index);
    }
    return *this;
}

ATE_EC ConfigManager::load_config() {
    std::lock_guard<std::mutex> lock(m_config_mutex);
    
    if (!file_exists(m_config_file_path)) {
        m_set_last_error("Configuration file not found: " + m_config_file_path);
        return ATE_ERROR_COMMUNICATION_ERROR;
    }
    
    std::string content; // 配置文件内容
    ATE_EC result = read_file(m_config_file_path, content);
    if (result != ATE_SUCCESS) {
        return result;
    }
    
    try {
        nlohmann::json config_json = nlohmann::json::parse(content);
        
        // 解析不同的配置段
        if (config_json.contains("driver_info")) {
            result = parse_driver_info(config_json["driver_info"]);
            if (result != ATE_SUCCESS) return result;
        }
        
        if (config_json.contains("capabilities")) {
            result = parse_capabilities_config(config_json["capabilities"]);
            if (result != ATE_SUCCESS) return result;
        } 
        
        if (config_json.contains("error_handling")) {
            result = parse_error_handling_config(config_json["error_handling"]);
            if (result != ATE_SUCCESS) return result;
        }

        if (config_json.contains("instances")) {
            result = parse_instance_configs(config_json["instances"]);
            if (result != ATE_SUCCESS) return result;
        }   


        
        if (config_json.contains("command_system")) {
            result = parse_command_system(config_json["command_system"]);
            if (result != ATE_SUCCESS) return result;
        }
        
        m_config_loaded = true;
        
        return ATE_SUCCESS;
        
    } catch (const nlohmann::json::exception& e) {
        m_set_last_error("JSON parsing error: " + std::string(e.what()));
        return ATE_ERROR_COMMUNICATION_ERROR;
    }
}



std::vector<DeviceConfig> ConfigManager::get_device_configs() const {
    std::lock_guard<std::mutex> lock(m_config_mutex);
    return m_device_configs;
}



bool ConfigManager::get_device_config(const std::string& instance_id, DeviceConfig& config) const {
    std::lock_guard<std::mutex> lock(m_config_mutex);
    
    auto it = m_device_config_index.find(instance_id);
    if (it == m_device_config_index.end()) {
        return false;
    }
    
    config = m_device_configs[it->second];
    return true;
}

std::string ConfigManager::get_instance_id_by_device_index(uint32_t device_index) const {
    std::lock_guard<std::mutex> lock(m_config_mutex);
    
    for (const auto& config : m_device_configs) {
        if (config.device_index == device_index) {
            return config.instance_id;
        }
    }
    return "";
}

ConfigManager::DriverInfo ConfigManager::get_driver_info() const {
    std::lock_guard<std::mutex> lock(m_config_mutex);
    return m_driver_info;
}

ConfigManager::CapabilitiesConfig ConfigManager::get_capabilities_config() const {
    std::lock_guard<std::mutex> lock(m_config_mutex);
    return m_capabilities_config;
}

ConfigManager::ErrorHandlingConfig ConfigManager::get_error_handling_config() const {
    std::lock_guard<std::mutex> lock(m_config_mutex);
    return m_error_handling_config;
}



std::vector<ConfigManager::CommandInfo> ConfigManager::get_command_system() const {
    std::lock_guard<std::mutex> lock(m_config_mutex);
    return m_command_system_config.commands;
}

ConfigManager::CommandSystemConfig ConfigManager::get_command_system_config() const {
    std::lock_guard<std::mutex> lock(m_config_mutex);
    return m_command_system_config;
}

bool ConfigManager::is_command_implemented(const std::string& command_name) const {
    std::lock_guard<std::mutex> lock(m_config_mutex);
    
    auto it = m_command_index.find(command_name);
    if (it == m_command_index.end()) {
        return false;
    }
    
    return m_command_system_config.commands[it->second].is_implemented;
}



ATE_EC ConfigManager::validate_config() const {
    std::lock_guard<std::mutex> lock(m_config_mutex);
    
    m_validation_errors.clear();
    
    // Validate device configs
    for (const auto& device_config : m_device_configs) {
        std::vector<std::string> errors;
        if (!validate_device_config(device_config, errors)) {
            m_validation_errors.insert(m_validation_errors.end(), errors.begin(), errors.end());
        }
    }
    
    return m_validation_errors.empty() ? ATE_SUCCESS : ATE_ERROR_COMMUNICATION_ERROR;
}

std::vector<std::string> ConfigManager::get_validation_errors() const {
    std::lock_guard<std::mutex> lock(m_config_mutex);
    return m_validation_errors;
}

std::string ConfigManager::get_config_file_path() const {
    return m_config_file_path;
}

bool ConfigManager::is_config_loaded() const {
    return m_config_loaded.load();
}

ATE_EC ConfigManager::parse_driver_info(const nlohmann::json& json) {
    try {
        if (json.contains("name")) m_driver_info.name = json["name"];
        if (json.contains("version")) m_driver_info.version = json["version"];
        if (json.contains("vendor")) m_driver_info.vendor = json["vendor"];
        if (json.contains("description")) m_driver_info.description = json["description"];
        if (json.contains("dll_filename")) m_driver_info.dll_filename = json["dll_filename"];
        if (json.contains("device_type")) m_driver_info.device_type = json["device_type"];
        if (json.contains("supports_multi_instance")) m_driver_info.supports_multi_instance = json["supports_multi_instance"];
        
        return ATE_SUCCESS;
    } catch (const nlohmann::json::exception& e) {
        m_set_last_error("Error parsing driver_info: " + std::string(e.what()));
        return ATE_ERROR_COMMUNICATION_ERROR;
    }
}

ATE_EC ConfigManager::parse_capabilities_config(const nlohmann::json& json) {
    try {
        if (json.contains("max_instances")) m_capabilities_config.max_instances = json["max_instances"];
        if (json.contains("supports_hot_plug")) m_capabilities_config.supports_hot_plug = json["supports_hot_plug"];
        if (json.contains("supports_calibration")) m_capabilities_config.supports_calibration = json["supports_calibration"];
        if (json.contains("auto_discovery")) m_capabilities_config.auto_discovery = json["auto_discovery"];
        
        return ATE_SUCCESS;
    } catch (const nlohmann::json::exception& e) {
        m_set_last_error("Error parsing capabilities config: " + std::string(e.what()));
        return ATE_ERROR_COMMUNICATION_ERROR;
    }
}

ATE_EC ConfigManager::parse_error_handling_config(const nlohmann::json& json) {
    try {
        if (json.contains("auto_recovery")) m_error_handling_config.auto_recovery = json["auto_recovery"];
        if (json.contains("error_logging_enabled")) m_error_handling_config.error_logging_enabled = json["error_logging_enabled"];
        
        return ATE_SUCCESS;
    } catch (const nlohmann::json::exception& e) {
        m_set_last_error("Error parsing error_handling config: " + std::string(e.what()));
        return ATE_ERROR_COMMUNICATION_ERROR;
    }
}

ATE_EC ConfigManager::parse_instance_configs(const nlohmann::json& json) {
    try {
        m_device_configs.clear();
        m_device_config_index.clear();
        
        for (const auto& device_json : json) {
            DeviceConfig config;
            
            // 解析基本字段
            config.instance_id = device_json["instance_id"];
            config.device_index = device_json.value("device_index", 0);
            config.display_name = device_json.value("display_name", "");
            config.memory_base_offset = device_json.value("memory_base_offset", 0);
            config.enabled = device_json.value("enabled", true);
            
            // 解析communication子对象
            if (device_json.contains("communication")) {
                const auto& comm = device_json["communication"];
                
                // 通用通讯参数
                config.communication.interface_type = comm.value("interface_type", "TCP");
                config.communication.device_id = comm.value("device_id", 1);
                config.communication.timeout_ms = comm.value("timeout_ms", 1000);
                config.communication.retry_count = comm.value("retry_count", 3);
                
                // 根据接口类型解析特定参数
                if (config.communication.interface_type == "TCP") {
                    config.communication.ip_address = comm.value("ip_address", "");
                    config.communication.port = comm.value("port", 502);
                } else if (config.communication.interface_type == "SERIAL") {
                    config.communication.com_port = comm.value("com_port", "");
                    config.communication.baud_rate = comm.value("baud_rate", 9600);
                    config.communication.data_bits = comm.value("data_bits", 8);
                    config.communication.parity = comm.value("parity", "NONE");
                    config.communication.stop_bits = comm.value("stop_bits", 1);
                } else if (config.communication.interface_type == "USB") {
                    config.communication.vendor_id = comm.value("vendor_id", "");
                    config.communication.product_id = comm.value("product_id", "");
                } else if (config.communication.interface_type == "SDK") {
                    config.communication.sdk_name = comm.value("sdk_name", "");
                    config.communication.sdk_version = comm.value("sdk_version", "");
                    config.communication.connection_string = comm.value("connection_string", "");
                }
            } else {
                // 兼容旧格式，直接从根级别读取TCP参数
                config.communication.interface_type = "TCP";
                config.communication.ip_address = device_json.value("ip_address", "");
                config.communication.port = device_json.value("port", 502);
                config.communication.timeout_ms = device_json.value("timeout_ms", 3000);
                config.communication.device_id = device_json.value("device_id", 1);
                config.communication.retry_count = device_json.value("retry_count", 3);
            }
            
            // 解析custom_parameters子对象
            if (device_json.contains("custom_parameters")) {
                const auto& custom = device_json["custom_parameters"];
                config.custom_parameters.serial_number = custom.value("serial_number", "");
                config.custom_parameters.calibration_date = custom.value("calibration_date", "");
                config.custom_parameters.max_power = custom.value("max_power", 0.0);
                config.custom_parameters.max_current = custom.value("max_current", 0.0);
                config.custom_parameters.max_voltage = custom.value("max_voltage", 0.0);
            }
            
            m_device_config_index[config.instance_id] = m_device_configs.size();
            m_device_configs.push_back(config);
        }
        
        return ATE_SUCCESS;
    } catch (const nlohmann::json::exception& e) {
        m_set_last_error("Error parsing device configs: " + std::string(e.what()));
        return ATE_ERROR_COMMUNICATION_ERROR;
    }
}

ATE_EC ConfigManager::parse_command_system(const nlohmann::json& json) {
    try {
        m_command_system_config.commands.clear();
        m_command_index.clear();
        
        // 解析categories中的命令
        if (json.contains("categories")) {
            const auto& categories = json["categories"];
            
            for (const auto& [category_name, category_data] : categories.items()) {
                if (category_data.contains("commands")) {
                    const auto& commands = category_data["commands"];
                    
                    for (const auto& [cmd_name, cmd_data] : commands.items()) {
                        CommandInfo cmd;
                        cmd.command_name = cmd_name;
                        cmd.function_name = cmd_data.value("function_name", cmd_name);
                        cmd.parameter_type = cmd_data.value("parameter_type", "");
                        cmd.description = cmd_data.value("description", "");
                        cmd.unit = cmd_data.value("unit", "");
                        cmd.response_time_ms = cmd_data.value("response_time_ms", 100);
                        cmd.category = category_name;
                        cmd.is_implemented = true;
                        
                        // 解析valid_values数组
                        if (cmd_data.contains("valid_values") && cmd_data["valid_values"].is_array()) {
                            for (const auto& value : cmd_data["valid_values"]) {
                                cmd.valid_values.push_back(value.get<std::string>());
                            }
                        }
                        
                        // 解析validation字段
                        if (cmd_data.contains("validation")) {
                            const auto& validation = cmd_data["validation"];
                            cmd.validation.required = validation.value("required", false);
                            cmd.validation.precision = validation.value("precision", 0.0);
                            
                            if (validation.contains("range")) {
                                const auto& range = validation["range"];
                                if (range.is_object()) {
                                    cmd.validation.range.min = range.value("min", 0.0);
                                    cmd.validation.range.max = range.value("max", 0.0);
                                } else if (range.is_string()) {
                                    cmd.validation.range_string = range.get<std::string>();
                                }
                            }
                        }
                        
                        m_command_index[cmd.command_name] = m_command_system_config.commands.size();
                        m_command_system_config.commands.push_back(cmd);
                    }
                }
            }
        }
        
        // 解析command_execution配置
        if (json.contains("command_execution")) {
            const auto& execution = json["command_execution"];
            m_command_system_config.execution.timeout_ms = execution.value("timeout_ms", 5000);
            m_command_system_config.execution.retry_count = execution.value("retry_count", 3);
            m_command_system_config.execution.retry_delay_ms = execution.value("retry_delay_ms", 100);
            m_command_system_config.execution.queue_size = execution.value("queue_size", 100);
            m_command_system_config.execution.concurrent_commands = execution.value("concurrent_commands", 5);
            m_command_system_config.execution.priority_scheduling = execution.value("priority_scheduling", false);
        }
        
        // 解析validation配置
        if (json.contains("validation")) {
            const auto& validation = json["validation"];
            m_command_system_config.validation.parameter_checking = validation.value("parameter_checking", true);
            m_command_system_config.validation.range_validation = validation.value("range_validation", true);
            m_command_system_config.validation.type_validation = validation.value("type_validation", true);
            
            if (validation.contains("authorization_required") && validation["authorization_required"].is_array()) {
                for (const auto& auth : validation["authorization_required"]) {
                    m_command_system_config.validation.authorization_required.push_back(auth.get<std::string>());
                }
            }
        }
        
        return ATE_SUCCESS;
    } catch (const nlohmann::json::exception& e) {
        m_set_last_error("Error parsing command system: " + std::string(e.what()));
        return ATE_ERROR_COMMUNICATION_ERROR;
    }
}



bool ConfigManager::validate_device_config(const DeviceConfig& config, std::vector<std::string>& errors) const {
    bool valid = true;
    
    if (config.instance_id.empty()) {
        errors.push_back("Device instance_id cannot be empty");
        valid = false;
    }
    
    // 验证通讯配置
    if (config.communication.interface_type.empty()) {
        errors.push_back("Communication interface type cannot be empty");
        valid = false;
    } else {
        // 根据接口类型验证特定参数
        if (config.communication.interface_type == "TCP") {
            if (config.communication.ip_address.empty()) {
                errors.push_back("TCP IP address cannot be empty");
                valid = false;
            }
            if (config.communication.port == 0) {
                errors.push_back("TCP port must be greater than 0");
                valid = false;
            }
        } else if (config.communication.interface_type == "SERIAL") {
            if (config.communication.com_port.empty()) {
                errors.push_back("Serial COM port cannot be empty");
                valid = false;
            }
            if (config.communication.baud_rate == 0) {
                errors.push_back("Serial baud rate must be greater than 0");
                valid = false;
            }
        } else if (config.communication.interface_type == "USB") {
            if (config.communication.vendor_id.empty()) {
                errors.push_back("USB vendor ID cannot be empty");
                valid = false;
            }
            if (config.communication.product_id.empty()) {
                errors.push_back("USB product ID cannot be empty");
                valid = false;
            }
        } else if (config.communication.interface_type == "SDK") {
            if (config.communication.sdk_name.empty()) {
                errors.push_back("SDK name cannot be empty");
                valid = false;
            }
        } else {
            errors.push_back("Unsupported interface type: " + config.communication.interface_type);
            valid = false;
        }
    }
    
    if (config.communication.timeout_ms < 100 || config.communication.timeout_ms > 60000) {
        errors.push_back("Communication timeout must be between 100ms and 60000ms");
        valid = false;
    }
    
    return valid;
}



ATE_EC ConfigManager::read_file(const std::string& file_path, std::string& content) const {
    try {
        std::ifstream file(file_path);
        if (!file.is_open()) {
            m_set_last_error("Cannot open file: " + file_path);
            return ATE_ERROR_COMMUNICATION_ERROR;
        }
        
        std::stringstream buffer;
        buffer << file.rdbuf();
        content = buffer.str();
        
        return ATE_SUCCESS;
    } catch (const std::exception& e) {
        m_set_last_error("Error reading file: " + std::string(e.what()));
        return ATE_ERROR_COMMUNICATION_ERROR;
    }
}

bool ConfigManager::file_exists(const std::string& file_path) const {
    return std::filesystem::exists(file_path);
}

// get_file_modified_time 方法已被移除

void ConfigManager::m_set_last_error(const std::string& error) const {
    m_last_error = error;
}

} // namespace EthClient