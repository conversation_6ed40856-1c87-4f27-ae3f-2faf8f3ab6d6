/**
 * @file engine_framework_interface.cpp
 * @brief DLL接口导出模块实现
 * 
 * 本模块用于导出DLL接口，并做简单的调用关系映射。
 * 内部复杂调用和逻辑，通过engine_manager这个顶层模块完成管理。
 * 
 * 主要功能：
 * 1. 提供标准化的C接口，供外部应用程序调用
 * 2. 参数验证和错误处理的第一层封装
 * 3. 将外部调用映射到内部engine_manager模块的相应功能
 * 4. 管理DLL的生命周期（初始化、清理等）
 * 5. 提供设备管理、引擎控制、数据处理等核心功能的接口
 * 
 * 调用关系：
 * 外部应用 -> engine_framework_interface (本模块) -> engine_manager -> 具体功能模块
 * 
 * 设计原则：
 * - 本模块保持简洁，主要负责接口转换和基础验证
 * - 复杂的业务逻辑和状态管理由engine_manager负责
 * - 确保接口的稳定性和向后兼容性
 * 
 * <AUTHOR> Framework Team
 * @version 1.0
 * @date 2024
 */

#include "engine_framework_interface.hpp"
#include "device_manager.hpp"
#include "engine_manager.hpp"
#include <cstring>
#include <unordered_map>
#include <mutex>
#include <thread>
#include <functional>

// 存储引擎状态变化回调函数的容器
static std::unordered_map<int32_t, EngineCoreStateChangeCallback> g_state_change_callbacks;
static std::mutex g_callbacks_mutex;

// 存储全局核心状态变化回调函数的容器
static GlobalCoreStateChangeCallback g_global_core_callback = nullptr;
static std::mutex g_global_core_callback_mutex;

// 异步执行引擎状态变化回调的辅助函数
void async_invoke_state_change_callback(int32_t engine_id, FSMState from, FSMState to, const std::string& event_data) {
    std::thread([engine_id, from, to, event_data]() {
        std::lock_guard<std::mutex> lock(g_callbacks_mutex);
        auto it = g_state_change_callbacks.find(engine_id);
        if (it != g_state_change_callbacks.end() && it->second != nullptr) {
            // 创建event_data的C风格字符串副本
            it->second(engine_id, from, to, event_data.c_str());
        }
    }).detach();
}

// 异步执行全局核心状态变化回调的辅助函数
void async_invoke_global_core_state_change_callback(FSMState from, FSMState to, const std::string& event_data) {
    std::thread([from, to, event_data]() {
        std::lock_guard<std::mutex> lock(g_global_core_callback_mutex);
        if (g_global_core_callback != nullptr) {
            // 创建event_data的C风格字符串副本
            g_global_core_callback(from, to, event_data.c_str());
        }
    }).detach();
}

// 参数验证宏
#define VALIDATE_POINTER(ptr) \
    if ((ptr) == nullptr) { \
        return ATE_ERROR_NULL_POINTER; \
    }

#define VALIDATE_INSTANCE_NAME(name) \
    if ((name) == nullptr || strlen(name) == 0) { \
        return ATE_ERROR_INVALID_PARAMETER; \
    }

#define VALIDATE_BUFFER(buffer, size) \
    if ((buffer) == nullptr || (size) <= 0) { \
        return ATE_ERROR_INVALID_PARAMETER; \
    }

extern "C" {

ATE_EC initialize_dll(const char* dll_directory_path) {
    // 验证参数
    VALIDATE_POINTER(dll_directory_path);
    
    if (strlen(dll_directory_path) == 0) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    // 获取设备实例管理器
    DeviceInstanceManager& manager = DeviceInstanceManager::get_instance();
    
    // 调用管理器的初始化函数
    return manager.initialize(std::string(dll_directory_path));
}

ATE_EC cleanup_dll(const char* dll_directory_path) {
    // dll_directory_path可以为空，表示清理所有DLL
    std::string path_str;
    if (dll_directory_path != nullptr) {
        path_str = std::string(dll_directory_path);
    }
    
    // 获取设备实例管理器
    DeviceInstanceManager& manager = DeviceInstanceManager::get_instance();
    
    // 调用管理器的清理函数
    return manager.cleanup(path_str);
}

ATE_EC connect_device(const char* instance_name) {
    // 验证参数
    VALIDATE_INSTANCE_NAME(instance_name);
    
    // 获取设备实例管理器
    DeviceInstanceManager& manager = DeviceInstanceManager::get_instance();
    
    // 检查管理器是否已初始化
    if (!manager.is_initialized()) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    // 查找设备实例
    auto instance = manager.find_instance(std::string(instance_name));
    if (!instance) {
        return ATE_ERROR_DEVICE_NOT_FOUND;
    }
    
    // 检查设备是否已连接
    if (instance->is_connected) {
        return ATE_ERROR_DEVICE_ALREADY_EXISTS;
    }
    
    // 检查函数指针是否有效
    if (!instance->functions.connect_device) {
        return ATE_ERROR_LIBRARY_LOAD_FAILED;
    }
    
    // 调用底层DLL的connect_device函数
    ATE_EC result = instance->functions.connect_device(instance->device_index);
    
    // 更新连接状态
    if (result == ATE_SUCCESS) {
        instance->is_connected = true;
    }
    
    return result;
}

ATE_EC disconnect_device(const char* instance_name) {
    // 验证参数
    VALIDATE_INSTANCE_NAME(instance_name);
    
    // 获取设备实例管理器
    DeviceInstanceManager& manager = DeviceInstanceManager::get_instance();
    
    // 检查管理器是否已初始化
    if (!manager.is_initialized()) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    // 查找设备实例
    auto instance = manager.find_instance(std::string(instance_name));
    if (!instance) {
        return ATE_ERROR_DEVICE_NOT_FOUND;
    }
    
    // 检查函数指针是否有效
    if (!instance->functions.disconnect_device) {
        return ATE_ERROR_LIBRARY_LOAD_FAILED;
    }
    
    // 调用底层DLL的disconnect_device函数
    ATE_EC result = instance->functions.disconnect_device(instance->device_index);
    
    // 更新连接状态
    if (result == ATE_SUCCESS) {
        instance->is_connected = false;
    }
    
    return result;
}

ATE_EC get_device_descriptor(const char* instance_name, 
                            char* device_descriptor, 
                            const int32_t buffer_size, 
                            int32_t* device_descriptor_size) {
    // 验证参数
    VALIDATE_INSTANCE_NAME(instance_name);
    VALIDATE_BUFFER(device_descriptor, buffer_size);
    VALIDATE_POINTER(device_descriptor_size);
    
    // 获取设备实例管理器
    DeviceInstanceManager& manager = DeviceInstanceManager::get_instance();
    
    // 检查管理器是否已初始化
    if (!manager.is_initialized()) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    // 查找设备实例
    auto instance = manager.find_instance(std::string(instance_name));
    if (!instance) {
        return ATE_ERROR_DEVICE_NOT_FOUND;
    }
    
    // 检查函数指针是否有效
    if (!instance->functions.get_device_descriptor) {
        return ATE_ERROR_LIBRARY_LOAD_FAILED;
    }
    
    // 调用底层DLL的get_device_descriptor函数
    return instance->functions.get_device_descriptor(
        instance->device_index, 
        device_descriptor, 
        buffer_size, 
        device_descriptor_size
    );
}

ATE_EC execute_command_unified(const char* instance_name, 
                              const E5000_CommandMode mode, 
                              const char* command, 
                              const int32_t command_length, 
                              const int32_t timeout_ms, 
                              char* response, 
                              const int32_t buffer_size, 
                              int32_t* response_size) {
    // 验证参数
    VALIDATE_INSTANCE_NAME(instance_name);
    VALIDATE_POINTER(command);
    VALIDATE_POINTER(response_size);
    
    if (command_length <= 0) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    if (timeout_ms < 0) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    // response可以为空（对于只写命令）
    if (response != nullptr && buffer_size <= 0) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    // 获取设备实例管理器
    DeviceInstanceManager& manager = DeviceInstanceManager::get_instance();
    
    // 检查管理器是否已初始化
    if (!manager.is_initialized()) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    // 查找设备实例
    auto instance = manager.find_instance(std::string(instance_name));
    if (!instance) {
        return ATE_ERROR_DEVICE_NOT_FOUND;
    }
    
    // 检查函数指针是否有效
    if (!instance->functions.execute_command_unified) {
        return ATE_ERROR_LIBRARY_LOAD_FAILED;
    }
    
    // 调用底层DLL的execute_command_unified函数
    return instance->functions.execute_command_unified(
        instance->device_index, 
        mode, 
        command, 
        command_length, 
        timeout_ms, 
        response, 
        buffer_size, 
        response_size
    );
}

ATE_EC get_channel_state(const char* instance_name, 
                        char* channel_state, 
                        const int32_t buffer_size, 
                        int32_t* channel_state_size) {
    // 验证参数
    VALIDATE_INSTANCE_NAME(instance_name);
    VALIDATE_BUFFER(channel_state, buffer_size);
    VALIDATE_POINTER(channel_state_size);
    
    // 获取设备实例管理器
    DeviceInstanceManager& manager = DeviceInstanceManager::get_instance();
    
    // 检查管理器是否已初始化
    if (!manager.is_initialized()) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    // 查找设备实例
    auto instance = manager.find_instance(std::string(instance_name));
    if (!instance) {
        return ATE_ERROR_DEVICE_NOT_FOUND;
    }
    
    // 检查函数指针是否有效
    if (!instance->functions.get_channel_state) {
        return ATE_ERROR_LIBRARY_LOAD_FAILED;
    }
    
    // 调用底层DLL的get_channel_state函数
    return instance->functions.get_channel_state(
        instance->device_index, 
        channel_state, 
        buffer_size, 
        channel_state_size
    );
}

ATE_EC suspend_engine_core(int32_t engine_id) {
    // 获取引擎管理器实例
    EngineManager& manager = EngineManager::get_instance();
    
    // 调用引擎管理器的suspend_engine_core函数
    if (manager.suspend_engine_core(static_cast<int>(engine_id))) {
        return ATE_SUCCESS;
    } else {
        return ATE_ERROR_COMMAND_FAILED;
    }
}

ATE_EC resume_engine_core(int32_t engine_id) {
    // 获取引擎管理器实例
    EngineManager& manager = EngineManager::get_instance();
    
    // 调用引擎管理器的resume_engine_core函数
    if (manager.resume_engine_core(static_cast<int>(engine_id))) {
        return ATE_SUCCESS;
    } else {
        return ATE_ERROR_COMMAND_FAILED;
    }
}

ATE_EC suspend_global_core() {
    // 获取引擎管理器实例
    EngineManager& manager = EngineManager::get_instance();
    
    // 调用引擎管理器的suspend_global_core函数
    ATE_EC result = manager.suspend_global_core();
    return result;
}

ATE_EC resume_global_core() {
    // 获取引擎管理器实例
    EngineManager& manager = EngineManager::get_instance();
    
    // 调用引擎管理器的resume_global_core函数
    ATE_EC result = manager.resume_global_core();
    return result;
}

ATE_EC register_engine_state_change_callback(int32_t engine_id, EngineCoreStateChangeCallback callback) {
    if (engine_id < 0 || callback == nullptr) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    std::lock_guard<std::mutex> lock(g_callbacks_mutex);
    g_state_change_callbacks[engine_id] = callback;
    return ATE_SUCCESS;
}

ATE_EC unregister_engine_state_change_callback(int32_t engine_id) {
    if (engine_id < 0) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    std::lock_guard<std::mutex> lock(g_callbacks_mutex);
    g_state_change_callbacks.erase(engine_id);
    return ATE_SUCCESS;
}

ATE_EC register_global_state_change_callback(GlobalCoreStateChangeCallback callback) {
    if (callback == nullptr) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    std::lock_guard<std::mutex> lock(g_global_core_callback_mutex);
    g_global_core_callback = callback;
    return ATE_SUCCESS;
}

ATE_EC unregister_global_state_change_callback() {
    std::lock_guard<std::mutex> lock(g_global_core_callback_mutex);
    g_global_core_callback = nullptr;
    return ATE_SUCCESS;
}

} // extern "C"