#ifndef TCP_UDP_CLIENT_HPP
#define TCP_UDP_CLIENT_HPP

#include "common_types.hpp"
#include "data_structures.hpp"
#include "eth_client_interface.hpp"
#include <string>
#include <vector>
#include <memory>
#include <atomic>
#include <mutex>
#include <queue>
#include <condition_variable>
#include <chrono>

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
using socket_t = SOCKET;
constexpr socket_t INVALID_SOCKET_VALUE = INVALID_SOCKET;
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
using socket_t = int;
constexpr socket_t INVALID_SOCKET_VALUE = -1;
#endif

namespace EthClient {

// 写入命令结构体
struct WriteCommand {
    std::vector<uint8_t> command_data;  // 命令数据
    uint32_t retry_count;               // 剩余重试次数
    uint64_t timestamp;                 // 创建时间戳
    std::string description;            // 命令描述（用于调试）
    
    WriteCommand(const std::vector<uint8_t>& data, uint32_t retries, const std::string& desc = "")
        : command_data(data), retry_count(retries), description(desc) {
        auto now = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now().time_since_epoch()).count();
        timestamp = static_cast<uint64_t>(now);
    }
};

class TcpUdpClient {
public:
    explicit TcpUdpClient(const DeviceConfig& config);
    ~TcpUdpClient();
    
    // 禁用拷贝构造函数和赋值运算符
    TcpUdpClient(const TcpUdpClient&) = delete;
    TcpUdpClient& operator=(const TcpUdpClient&) = delete;
    
    // 移动构造函数和赋值运算符
    TcpUdpClient(TcpUdpClient&& other) noexcept;
    TcpUdpClient& operator=(TcpUdpClient&& other) noexcept;
    
    // 连接管理
    ATE_EC connect();
    void disconnect();
    bool is_connected() const;
    
    // 四个固定命令的轮询机制
    ATE_EC execute_fixed_command_a(E5000DataBlockV2& data);  // 电压电流功率状态
    ATE_EC execute_fixed_command_b(E5000DataBlockV2& data);  // 运行状态
    ATE_EC execute_fixed_command_c(E5000DataBlockV2& data);  // 上下限判断
    ATE_EC execute_mode_command(E5000DataBlockV2& data);     // 读模式寄存器
    
    // 轮询控制
    void start_polling();
    void stop_polling();
    bool is_polling() const;
    
    // 命令队列管理
    ATE_EC add_write_command(const std::vector<uint8_t>& command, const std::string& description = "");

    
    // 处理待发送的写入命令（在device_worker_loop中调用）
    void process_pending_commands();
    
    // 处理接收到的数据并放入内部缓冲区（在device_worker_loop中调用）
    void process_received_data();
    
    // 获取接收缓冲区数据（返回副本）
    std::vector<uint8_t> get_received_data() const;
    
    // 获取接收缓冲区大小
    size_t get_received_data_size() const;
    
    // 清空接收缓冲区
    void clear_received_data();
    
    // 检查接收缓冲区是否有数据
    bool has_received_data() const;
    
    // 操作模式控制
    ATE_EC write_setpoint(uint16_t register_address, double value, const std::string& command_description);
    
    // 透传通信接口 - 类似m_IoTran函数
    // 直接发送原始数据并等待响应，占用modbus连接
    ATE_EC execute_raw_command(const uint8_t* command_data, size_t command_length, 
                               uint32_t timeout_ms, uint8_t* response_data, 
                               size_t response_buffer_size, size_t& actual_response_length);
    
    // 配置
    void set_timeout(uint32_t timeout_ms);
    uint32_t get_timeout() const;
    
    // 状态
    ConnectionStatus get_connection_status() const;
    std::string get_last_error() const;
    uint64_t get_last_activity_timestamp() const;
    
    // 统计信息
    uint32_t get_command_count() const;
    uint32_t get_error_count() const;
    void reset_statistics();
    
private:
    // 套接字操作
    ATE_EC create_socket();
    void close_socket();
    ATE_EC send_data(const std::vector<uint8_t>& data);
    ATE_EC receive_data(std::vector<uint8_t>& data);
    
    // 数据解析
    ATE_EC parse_modbus_tcp_response(std::vector<uint8_t>& response);
    
    // 发送Modbus命令的核心方法
    ATE_EC send_receive_modbus_command(const std::vector<uint8_t>& command, std::vector<uint8_t>& response);
    
    // 数据解析函数
    void parse_command_a_response(const std::vector<uint8_t>& response, E5000DataBlockV2& data);
    void parse_command_b_response(const std::vector<uint8_t>& response, E5000DataBlockV2& data);
    void parse_command_c_response(const std::vector<uint8_t>& response, E5000DataBlockV2& data);
    void parse_mode_command_response(const std::vector<uint8_t>& response, E5000DataBlockV2& data);
    
    // 实用函数
    double parse_double_from_registers(const std::vector<uint8_t>& data, size_t offset);
    void update_last_activity();
    ATE_EC set_socket_timeout(socket_t socket, uint32_t timeout_ms);
    std::vector<uint8_t> build_modbus_tcp_frame(const std::vector<uint8_t>& modbus_pdu);
    
    // Error handling
    void m_set_last_error(const std::string& error); // 设置最后错误
    ATE_EC handle_socket_error(); // 处理套接字错误
    
private:
    DeviceConfig m_config;
    socket_t m_socket;
    std::atomic<bool> m_connected;
    std::atomic<ConnectionStatus> m_status;
    
    mutable std::mutex m_socket_mutex;
    mutable std::mutex m_error_mutex;
    mutable std::mutex m_query_mutex;  // 同步查询操作的互斥锁，类似SelfExeResMutex
    
    std::string m_last_error;
    std::atomic<uint64_t> m_last_activity_timestamp;
    std::atomic<uint32_t> m_command_count;
    std::atomic<uint32_t> m_error_count;
    std::atomic<uint16_t> m_transaction_id;
    
    std::atomic<uint32_t> m_timeout_ms;
    
    // 四个固定命令的字节数组
    static const std::vector<uint8_t> DCLOAD_RO_BYTES_A;  // 电压电流功率状态
    static const std::vector<uint8_t> DCLOAD_RO_BYTES_B;  // 运行状态
    static const std::vector<uint8_t> DCLOAD_RO_BYTES_C;  // 上下限判断
    static const std::vector<uint8_t> DCLOAD_MODE_BYTES;  // 读模式寄存器
    
    // 轮询控制
    std::atomic<bool> m_polling_active;
    std::atomic<int> m_polling_switch;  // 0, 1, 2 轮询切换
    std::atomic<uint64_t> m_last_command_c_time;  // 上次执行命令C的时间
    std::atomic<uint64_t> m_last_mode_command_time;  // 上次执行模式命令的时间
    
    // 命令队列相关成员变量
    std::queue<WriteCommand> m_write_command_queue;
    mutable std::mutex m_command_queue_mutex;
    std::condition_variable m_command_queue_cv;
    
    // 接收数据缓冲区相关
    std::vector<std::vector<uint8_t>> m_receive_buffer_container;  // 接收数据缓冲区容器，每个元素存储一次接收的数据
    mutable std::mutex m_receive_buffer_mutex;                     // 接收缓冲区互斥锁
    static constexpr size_t RECEIVE_BUFFER_CHUNK_SIZE = 2048;      // 每次接收数据的缓冲区大小（2K）
    static constexpr size_t MAX_RECEIVE_BUFFER_CHUNKS = 100;       // 最大缓冲区块数量
    
#ifdef _WIN32
    static std::atomic<bool> s_winsock_initialized;
    static std::mutex s_winsock_mutex;
    static ATE_EC initialize_winsock();
    static void cleanup_winsock();
#endif
};

} // namespace EthClient

#endif // TCP_UDP_CLIENT_HPP