#ifndef REPORT_PROCESS_HPP
#define REPORT_PROCESS_HPP

#include <memory>
#include <vector>
#include <string>
#include <map>
#include <functional>
#include <chrono>
#include <mutex>
#include <thread>
#include <atomic>
#include <queue>
#include <fstream>
#include <condition_variable>
#include "common_types.hpp"

// 前向声明
class DataProcess;
class GlobalVariableTask;
class DataRecorder;

namespace ReportProcessing {

// 报告类型枚举
enum class ReportType {
    SUMMARY,        // 摘要报告
    DETAILED,       // 详细报告
    STATISTICAL,    // 统计报告
    TREND,          // 趋势报告
    COMPARISON,     // 对比报告
    DIAGNOSTIC,     // 诊断报告
    PERFORMANCE,    // 性能报告
    CUSTOM          // 自定义报告
};

// 报告格式枚举
enum class ReportFormat {
    HTML,
    PDF,
    JSON,
    XML,
    CSV,
    EXCEL,
    TEXT,
    MARKDOWN
};

// 报告状态枚举
enum class ReportStatus {
    PENDING,
    GENERATING,
    COMPLETED,
    FAILED,
    CANCELLED
};

// 报告优先级枚举
enum class ReportPriority {
    LOW,
    NORMAL,
    HIGH,
    URGENT
};

// 数据源类型
enum class DataSourceType {
    VARIABLE_SYSTEM,
    DATA_RECORDER,
    DATA_PROCESSOR,
    EXTERNAL_FILE,
    DATABASE,
    REAL_TIME
};

// 报告配置结构
struct ReportConfig {
    std::string report_id;
    std::string title;
    std::string description;
    ReportType type = ReportType::SUMMARY;
    ReportFormat format = ReportFormat::HTML;
    ReportPriority priority = ReportPriority::NORMAL;
    
    // 时间范围
    std::chrono::system_clock::time_point start_time;
    std::chrono::system_clock::time_point end_time;
    bool use_time_range = false;
    
    // 数据源配置
    std::vector<DataSourceType> data_sources;
    std::vector<std::string> variable_names;
    std::vector<std::string> data_files;
    
    // 输出配置
    std::string output_path;
    std::string template_path;
    bool auto_open = false;
    bool email_notification = false;
    std::vector<std::string> email_recipients;
    
    // 自定义参数
    std::map<std::string, std::string> custom_parameters;
    
    // 调度配置
    bool is_scheduled = false;
    std::chrono::minutes schedule_interval{60};
    std::chrono::system_clock::time_point next_run_time;
};

// 报告数据项
struct ReportDataItem {
    std::string name;
    std::string value;
    std::string unit;
    std::string category;
    std::chrono::system_clock::time_point timestamp;
    std::map<std::string, std::string> attributes;
    
    ReportDataItem() = default;
    ReportDataItem(const std::string& n, const std::string& v, const std::string& u = "")
        : name(n), value(v), unit(u), timestamp(std::chrono::system_clock::now()) {}
};

// 报告节
struct ReportSection {
    std::string section_id;
    std::string title;
    std::string content;
    std::vector<ReportDataItem> data_items;
    std::vector<std::string> charts; // 图表文件路径
    std::vector<std::string> tables; // 表格数据
    std::map<std::string, std::string> metadata;
    int order = 0;
};

// 报告结果
struct ReportResult {
    std::string report_id;
    ReportConfig config;
    ReportStatus status = ReportStatus::PENDING;
    std::string file_path;
    std::string error_message;
    std::chrono::system_clock::time_point created_time;
    std::chrono::system_clock::time_point completed_time;
    std::vector<ReportSection> sections;
    size_t file_size = 0;
    std::map<std::string, std::string> metadata;
};

// 报告模板接口
class IReportTemplate {
public:
    virtual ~IReportTemplate() = default;
    virtual std::string get_template_name() const = 0;
    virtual ReportFormat get_supported_format() const = 0;
    virtual std::string generate_report(const ReportResult& report_data) = 0;
    virtual bool validate_config(const ReportConfig& config) = 0;
};

// 数据收集器接口
class IDataCollector {
public:
    virtual ~IDataCollector() = default;
    virtual std::string get_collector_name() const = 0;
    virtual std::vector<DataSourceType> get_supported_sources() const = 0;
    virtual std::vector<ReportDataItem> collect_data(const ReportConfig& config) = 0;
    virtual bool is_available() const = 0;
};

// 图表生成器接口
class IChartGenerator {
public:
    virtual ~IChartGenerator() = default;
    virtual std::string get_generator_name() const = 0;
    virtual std::vector<std::string> get_supported_chart_types() const = 0;
    virtual std::string generate_chart(const std::string& chart_type, 
                                     const std::vector<ReportDataItem>& data,
                                     const std::map<std::string, std::string>& options) = 0;
};

} // namespace ReportProcessing

// 报告处理核心类
class ReportProcess {
public:
    ReportProcess();
    ~ReportProcess();

    // 禁用拷贝构造和赋值
    ReportProcess(const ReportProcess&) = delete;
    ReportProcess& operator=(const ReportProcess&) = delete;

    // 初始化和清理
    ATE_EC initialize();
    void shutdown();
    bool is_initialized() const { return m_initialized; }

    // 模块依赖设置
    void set_data_process(DataProcess* data_process);
    void set_variable_system(GlobalVariableTask* var_system);
    void set_data_recorder(DataRecorder* data_recorder);

    // 报告生成
    std::string create_report(const ReportProcessing::ReportConfig& config);
    bool generate_report_async(const std::string& report_id);
    bool cancel_report(const std::string& report_id);
    
    // 报告状态查询
    ReportProcessing::ReportStatus get_report_status(const std::string& report_id) const;
    ReportProcessing::ReportResult get_report_result(const std::string& report_id) const;
    std::vector<std::string> get_active_reports() const;
    std::vector<std::string> get_completed_reports() const;
    
    // 报告管理
    bool delete_report(const std::string& report_id);
    void clear_completed_reports();
    void clear_all_reports();
    
    // 模板管理
    void register_template(std::unique_ptr<ReportProcessing::IReportTemplate> template_ptr);
    void unregister_template(const std::string& template_name);
    std::vector<std::string> get_available_templates() const;
    std::vector<ReportProcessing::ReportFormat> get_supported_formats() const;
    
    // 数据收集器管理
    void register_data_collector(std::unique_ptr<ReportProcessing::IDataCollector> collector);
    void unregister_data_collector(const std::string& collector_name);
    std::vector<std::string> get_available_collectors() const;
    
    // 图表生成器管理
    void register_chart_generator(std::unique_ptr<ReportProcessing::IChartGenerator> generator);
    void unregister_chart_generator(const std::string& generator_name);
    std::vector<std::string> get_available_chart_generators() const;
    
    // 调度报告
    bool schedule_report(const ReportProcessing::ReportConfig& config);
    bool unschedule_report(const std::string& report_id);
    std::vector<std::string> get_scheduled_reports() const;
    void update_schedule();
    
    // 批量操作
    std::vector<std::string> create_batch_reports(const std::vector<ReportProcessing::ReportConfig>& configs);
    bool export_reports(const std::vector<std::string>& report_ids, const std::string& export_path);
    
    // 统计信息
    size_t get_total_reports_count() const;
    size_t get_pending_reports_count() const;
    size_t get_completed_reports_count() const;
    size_t get_failed_reports_count() const;
    double get_average_generation_time() const;
    
    // 配置管理
    void set_default_output_path(const std::string& path);
    std::string get_default_output_path() const;
    void set_max_concurrent_reports(size_t max_count);
    size_t get_max_concurrent_reports() const;
    void set_report_retention_days(int days);
    int get_report_retention_days() const;
    
    // 回调函数设置
    using ReportCompletedCallback = std::function<void(const std::string&, const ReportProcessing::ReportResult&)>;
    using ReportFailedCallback = std::function<void(const std::string&, const std::string&)>;
    using ReportProgressCallback = std::function<void(const std::string&, int)>; // report_id, progress_percentage
    
    void set_report_completed_callback(ReportCompletedCallback callback);
    void set_report_failed_callback(ReportFailedCallback callback);
    void set_report_progress_callback(ReportProgressCallback callback);
    
    // 错误处理
    std::vector<std::string> get_error_messages() const;
    void clear_error_messages();

private:
    // 内部状态
    std::atomic<bool> m_initialized{false};
    std::atomic<bool> m_shutdown_requested{false};
    
    // 模块依赖
    DataProcess* m_data_process = nullptr;
    GlobalVariableTask* m_variable_system = nullptr;
    DataRecorder* m_data_recorder = nullptr;
    
    // 报告存储
    std::map<std::string, ReportProcessing::ReportResult> m_reports;
    mutable std::mutex m_reports_mutex;
    
    // 组件管理
    std::map<std::string, std::unique_ptr<ReportProcessing::IReportTemplate>> m_templates;
    std::map<std::string, std::unique_ptr<ReportProcessing::IDataCollector>> m_collectors;
    std::map<std::string, std::unique_ptr<ReportProcessing::IChartGenerator>> m_chart_generators;
    mutable std::mutex m_components_mutex;
    
    // 调度管理
    std::map<std::string, ReportProcessing::ReportConfig> m_scheduled_reports;
    std::thread m_scheduler_thread;
    mutable std::mutex m_scheduler_mutex;
    
    // 生成队列
    std::queue<std::string> m_generation_queue;
    std::vector<std::thread> m_worker_threads;
    std::mutex m_queue_mutex;
    std::condition_variable m_queue_cv;
    
    // 配置
    std::string m_default_output_path;
    size_t m_max_concurrent_reports = 3;
    int m_report_retention_days = 30;
    
    // 统计信息
    std::atomic<size_t> m_total_reports{0};
    std::atomic<size_t> m_completed_reports{0};
    std::atomic<size_t> m_failed_reports{0};
    std::chrono::steady_clock::time_point m_start_time;
    std::vector<std::chrono::milliseconds> m_generation_times;
    mutable std::mutex m_stats_mutex;
    
    // 错误处理
    std::vector<std::string> m_error_messages;
    mutable std::mutex m_error_mutex;
    
    // 回调函数
    ReportCompletedCallback m_completed_callback;
    ReportFailedCallback m_failed_callback;
    ReportProgressCallback m_progress_callback;
    mutable std::mutex m_callback_mutex;
    
    // 内部方法
    void worker_thread_function();
    void scheduler_thread_function();
    bool generate_report_internal(const std::string& report_id);
    std::vector<ReportProcessing::ReportDataItem> collect_report_data(const ReportProcessing::ReportConfig& config);
    std::string generate_report_content(const ReportProcessing::ReportResult& report_data);
    bool save_report_to_file(const ReportProcessing::ReportResult& report_data);
    void cleanup_old_reports();
    void add_error_message(const std::string& message);
    void notify_report_completed(const std::string& report_id, const ReportProcessing::ReportResult& result);
    void notify_report_failed(const std::string& report_id, const std::string& error);
    void notify_report_progress(const std::string& report_id, int progress);
    std::string generate_unique_report_id() const;
    bool validate_report_config(const ReportProcessing::ReportConfig& config) const;
};

#endif // REPORT_PROCESS_HPP