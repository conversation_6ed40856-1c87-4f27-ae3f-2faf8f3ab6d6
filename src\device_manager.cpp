#include "device_manager.hpp"
#include "../third_library/nlohmann/json.hpp"
#include <fstream>
#include <iostream>
#include <filesystem>
#include <vector>

#ifdef _WIN32
#include <windows.h>
#else
#include <dlfcn.h>
#endif

using json = nlohmann::json;
namespace fs = std::filesystem;

// 静态成员定义
DeviceInstanceManager& DeviceInstanceManager::get_instance() {
    static DeviceInstanceManager instance;
    return instance;
}

DeviceInstanceManager::~DeviceInstanceManager() {
    cleanup();
}

ATE_EC DeviceInstanceManager::initialize(const std::string& dll_directory_path) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // 验证路径参数
    if (dll_directory_path.empty()) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    // 检查路径是否存在
    if (!fs::exists(dll_directory_path) || !fs::is_directory(dll_directory_path)) {
        return ATE_ERROR_PATH_NOT_FOUND;
    }
    
    // 清空现有实例
    m_instances.clear();
    
    // 加载指定目录下的所有DLL
    ATE_EC result = load_dlls_from_directory(dll_directory_path);
    if (result != ATE_SUCCESS) {
        return result;
    }
    
    return ATE_SUCCESS;
}

ATE_EC DeviceInstanceManager::cleanup(const std::string& dll_directory_path) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // 如果指定了路径，只清理该路径下的DLL
    if (!dll_directory_path.empty()) {
        std::vector<std::string> instances_to_remove;
        
        for (const auto& pair : m_instances) {
            const auto& instance = pair.second;
            if (instance->dll_path.find(dll_directory_path) == 0) {
                // 调用DLL的cleanup函数
                if (instance->functions.cleanup_dll) {
                    instance->functions.cleanup_dll();
                }
                
                // 卸载DLL
                unload_dll(instance);
                instances_to_remove.push_back(pair.first);
            }
        }
        
        // 从容器中移除
        for (const auto& name : instances_to_remove) {
            m_instances.erase(name);
        }
    } else {
        // 清理所有实例
        for (auto& pair : m_instances) {
            const auto& instance = pair.second;
            
            // 调用DLL的cleanup函数
            if (instance->functions.cleanup_dll) {
                instance->functions.cleanup_dll();
            }
            
            // 卸载DLL
            unload_dll(instance);
        }
        
        m_instances.clear();
    }
    
    return ATE_SUCCESS;
}

std::shared_ptr<DeviceInstance> DeviceInstanceManager::find_instance(const std::string& instance_name) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_instances.find(instance_name);
    if (it != m_instances.end()) {
        return it->second;
    }
    
    return nullptr;
}

ATE_EC DeviceInstanceManager::add_instance(std::shared_ptr<DeviceInstance> instance) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!instance) {
        return ATE_ERROR_NULL_POINTER;
    }
    
    if (instance->instance_name.empty()) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    // 检查是否已存在
    if (m_instances.find(instance->instance_name) != m_instances.end()) {
        return ATE_ERROR_DEVICE_ALREADY_EXISTS;
    }
    
    m_instances[instance->instance_name] = instance;
    return ATE_SUCCESS;
}

ATE_EC DeviceInstanceManager::remove_instance(const std::string& instance_name) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_instances.find(instance_name);
    if (it == m_instances.end()) {
        return ATE_ERROR_DEVICE_NOT_FOUND;
    }
    
    // 卸载DLL
    unload_dll(it->second);
    
    // 从容器中移除
    m_instances.erase(it);
    
    return ATE_SUCCESS;
}

std::vector<std::string> DeviceInstanceManager::get_all_instance_names() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    std::vector<std::string> names;
    names.reserve(m_instances.size());
    
    for (const auto& pair : m_instances) {
        names.push_back(pair.first);
    }
    
    return names;
}

bool DeviceInstanceManager::is_initialized() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return !m_instances.empty();
}

size_t DeviceInstanceManager::get_instance_count() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_instances.size();
}

ATE_EC DeviceInstanceManager::load_dlls_from_directory(const std::string& dll_directory_path) {
    try {
        // 遍历目录中的所有文件
        for (const auto& entry : fs::directory_iterator(dll_directory_path)) {
            if (!entry.is_regular_file()) {
                continue;
            }
            
            const auto& file_path = entry.path();
            
            // 检查是否为DLL文件
#ifdef _WIN32
            if (file_path.extension() != ".dll") {
                continue;
            }
#else
            if (file_path.extension() != ".so") {
                continue;
            }
#endif
            
            // 查找对应的JSON配置文件
            auto config_path = file_path;
            config_path.replace_extension(".json");
            
            if (!fs::exists(config_path)) {
                // 如果没有对应的JSON文件，跳过这个DLL
                continue;
            }
            
            // 加载DLL和配置
            ATE_EC result = load_single_dll(file_path.string(), config_path.string());
            if (result != ATE_SUCCESS) {
                // 记录错误但继续加载其他DLL
                std::cerr << "Failed to load DLL: " << file_path.string() 
                         << ", error code: " << result << std::endl;
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "Error scanning directory: " << e.what() << std::endl;
        return ATE_ERROR_SYSTEM_ERROR;
    }
    
    return ATE_SUCCESS;
}

ATE_EC DeviceInstanceManager::load_single_dll(const std::string& dll_path, const std::string& config_path) {
    // 加载DLL
#ifdef _WIN32
    DLL_HANDLE dll_handle = LoadLibraryA(dll_path.c_str());
    if (!dll_handle) {
        return ATE_ERROR_LIBRARY_LOAD_FAILED;
    }
#else
    DLL_HANDLE dll_handle = dlopen(dll_path.c_str(), RTLD_LAZY);
    if (!dll_handle) {
        return ATE_ERROR_LIBRARY_LOAD_FAILED;
    }
#endif
    
    // 创建设备实例
    auto instance = std::make_shared<DeviceInstance>();
    instance->dll_handle = dll_handle;
    instance->dll_path = dll_path;
    instance->config_file_path = config_path;
    
    // 加载DLL函数
    ATE_EC result = load_dll_functions(dll_handle, instance->functions);
    if (result != ATE_SUCCESS) {
        unload_dll(instance);
        return result;
    }
    
    // 解析配置文件
    result = parse_config_file(config_path, dll_path);
    if (result != ATE_SUCCESS) {
        unload_dll(instance);
        return result;
    }
    
    // 调用DLL的初始化函数
    if (instance->functions.initialize_dll) {
        result = instance->functions.initialize_dll();
        if (result != ATE_SUCCESS) {
            unload_dll(instance);
            return result;
        }
        instance->is_initialized = true;
    }
    
    return ATE_SUCCESS;
}

ATE_EC DeviceInstanceManager::unload_dll(std::shared_ptr<DeviceInstance> instance) {
    if (!instance || !instance->dll_handle) {
        return ATE_SUCCESS;
    }
    
    // 调用cleanup函数
    if (instance->functions.cleanup_dll && instance->is_initialized) {
        instance->functions.cleanup_dll();
        instance->is_initialized = false;
    }
    
    // 卸载DLL
#ifdef _WIN32
    FreeLibrary(static_cast<HMODULE>(instance->dll_handle));
#else
    dlclose(instance->dll_handle);
#endif
    
    instance->dll_handle = nullptr;
    
    return ATE_SUCCESS;
}

ATE_EC DeviceInstanceManager::load_dll_functions(DLL_HANDLE dll_handle, DllFunctions& functions) {
    if (!dll_handle) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    // 加载必需的函数
    functions.initialize_dll = reinterpret_cast<InitializeDllFunc>(
        GET_PROC_ADDRESS(dll_handle, "initialize_dll"));
    
    functions.cleanup_dll = reinterpret_cast<CleanupDllFunc>(
        GET_PROC_ADDRESS(dll_handle, "cleanup_dll"));
    
    functions.connect_device = reinterpret_cast<ConnectDeviceFunc>(
        GET_PROC_ADDRESS(dll_handle, "connect_device"));
    
    functions.disconnect_device = reinterpret_cast<DisconnectDeviceFunc>(
        GET_PROC_ADDRESS(dll_handle, "disconnect_device"));
    
    functions.get_device_descriptor = reinterpret_cast<GetDeviceDescriptorFunc>(
        GET_PROC_ADDRESS(dll_handle, "get_device_descriptor"));
    
    functions.execute_command_unified = reinterpret_cast<ExecuteCommandUnifiedFunc>(
        GET_PROC_ADDRESS(dll_handle, "execute_command_unified"));
    
    functions.get_channel_state = reinterpret_cast<GetChannelStateFunc>(
        GET_PROC_ADDRESS(dll_handle, "get_channel_state"));
    
    // 检查必需的函数是否加载成功
    if (!functions.initialize_dll || !functions.cleanup_dll || 
        !functions.connect_device || !functions.disconnect_device ||
        !functions.execute_command_unified) {
        return ATE_ERROR_LIBRARY_LOAD_FAILED;
    }
    
#undef GET_PROC_ADDRESS
    
    return ATE_SUCCESS;
}

ATE_EC DeviceInstanceManager::parse_config_file(const std::string& config_path, const std::string& dll_path) {
    try {
        // 读取JSON文件
        std::ifstream config_file(config_path);
        if (!config_file.is_open()) {
            return ATE_ERROR_FILE_NOT_FOUND;
        }
        
        json config;
        config_file >> config;
        config_file.close();
        
        // 解析instances数组
        if (!config.contains("instances") || !config["instances"].is_array()) {
            return ATE_ERROR_CONFIG_LOAD_FAILED;
        }
        
        const auto& instances = config["instances"];
        
        for (const auto& instance_config : instances) {
            // 检查必需字段
            if (!instance_config.contains("instance_id") || 
                !instance_config.contains("device_index") ||
                !instance_config.contains("enabled")) {
                continue;
            }
            
            // 跳过未启用的实例
            if (!instance_config["enabled"].get<bool>()) {
                continue;
            }
            
            // 创建设备实例
            auto device_instance = std::make_shared<DeviceInstance>();
            device_instance->instance_name = instance_config["instance_id"].get<std::string>();
            device_instance->device_index = instance_config["device_index"].get<int32_t>();
            device_instance->dll_path = dll_path;
            device_instance->config_file_path = config_path;
            
            // 加载DLL
#ifdef _WIN32
            device_instance->dll_handle = LoadLibraryA(dll_path.c_str());
#else
            device_instance->dll_handle = dlopen(dll_path.c_str(), RTLD_LAZY);
#endif
            
            if (!device_instance->dll_handle) {
                continue;
            }
            
            // 加载函数
            ATE_EC result = load_dll_functions(device_instance->dll_handle, device_instance->functions);
            if (result != ATE_SUCCESS) {
                unload_dll(device_instance);
                continue;
            }
            
            // 调用初始化函数
            if (device_instance->functions.initialize_dll) {
                result = device_instance->functions.initialize_dll();
                if (result == ATE_SUCCESS) {
                    device_instance->is_initialized = true;
                }
            }
            
            // 添加到容器
            add_instance(device_instance);
        }
        
    } catch (const std::exception& e) {
        std::cerr << "Error parsing config file: " << config_path 
                 << ", error: " << e.what() << std::endl;
        return ATE_ERROR_CONFIG_LOAD_FAILED;
    }
    
    return ATE_SUCCESS;
}