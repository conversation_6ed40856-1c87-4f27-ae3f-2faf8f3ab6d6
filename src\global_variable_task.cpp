#include "global_variable_task.hpp"
#include <sstream>
#include <iostream>
#include <cstring>
#include <algorithm>

#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <unistd.h>
    #include <netdb.h>
#endif

// RedisConfig 实现
bool RedisConfig::is_valid() const {
    return !host.empty() && port > 0 && port < 65536;
}

// GlobalVariableTask 实现
GlobalVariableTask::GlobalVariableTask(const std::string& instance_id, const RedisConfig& redis_config)
    : m_instance_id(instance_id)
    , m_redis_config(redis_config)
    , m_initialized(false)
    , m_connected(false)
    , m_redis_socket(-1) {
    
#ifdef _WIN32
    // 初始化Winsock
    WSADATA wsaData;
    WSAStartup(MAKEWORD(2, 2), &wsaData);
#endif
}

GlobalVariableTask::~GlobalVariableTask() noexcept {
    shutdown();
    
#ifdef _WIN32
    WSACleanup();
#endif
}

ATE_EC GlobalVariableTask::initialize() {
    if (m_initialized.load()) {
        return ATE_ERROR_ALREADY_INITIALIZED;
    }
    
    if (!m_redis_config.is_valid()) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    ATE_EC result = connect_to_redis();
    if (result == ATE_SUCCESS) {
        m_initialized.store(true);
    }
    
    return result;
}

void GlobalVariableTask::shutdown() {
    if (m_initialized.load()) {
        disconnect_from_redis();
        
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        m_variables.clear();
        
        m_initialized.store(false);
    }
}

ATE_EC GlobalVariableTask::connect_to_redis() {
    std::lock_guard<std::mutex> lock(m_redis_mutex);
    
    // 创建socket
    m_redis_socket = socket(AF_INET, SOCK_STREAM, 0);
    if (m_redis_socket < 0) {
        return ATE_ERROR_CONNECTION_FAILED;
    }
    
    // 设置连接地址
    struct sockaddr_in server_addr;
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(m_redis_config.port);
    
    if (inet_pton(AF_INET, m_redis_config.host.c_str(), &server_addr.sin_addr) <= 0) {
        // 尝试域名解析
        struct hostent* host_entry = gethostbyname(m_redis_config.host.c_str());
        if (!host_entry) {
#ifdef _WIN32
            closesocket(m_redis_socket);
#else
            close(m_redis_socket);
#endif
            m_redis_socket = -1;
            return ATE_ERROR_CONNECTION_FAILED;
        }
        memcpy(&server_addr.sin_addr, host_entry->h_addr_list[0], host_entry->h_length);
    }
    
    // 连接到Redis服务器
    if (connect(m_redis_socket, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
#ifdef _WIN32
        closesocket(m_redis_socket);
#else
        close(m_redis_socket);
#endif
        m_redis_socket = -1;
        return ATE_ERROR_CONNECTION_FAILED;
    }
    
    // 如果有密码，进行认证
    if (!m_redis_config.password.empty()) {
        std::string auth_cmd = "AUTH " + m_redis_config.password + "\r\n";
        std::string response;
        ATE_EC result = send_redis_command(auth_cmd, response);
        if (result != ATE_SUCCESS || response.find("+OK") == std::string::npos) {
            disconnect_from_redis();
            return ATE_ERROR_AUTHENTICATION_FAILED;
        }
    }
    
    // 选择数据库
    if (m_redis_config.database != 0) {
        std::string select_cmd = "SELECT " + std::to_string(m_redis_config.database) + "\r\n";
        std::string response;
        ATE_EC result = send_redis_command(select_cmd, response);
        if (result != ATE_SUCCESS || response.find("+OK") == std::string::npos) {
            disconnect_from_redis();
            return ATE_ERROR_CONNECTION_FAILED;
        }
    }
    
    m_connected.store(true);
    return ATE_SUCCESS;
}

void GlobalVariableTask::disconnect_from_redis() {
    if (m_redis_socket >= 0) {
#ifdef _WIN32
        closesocket(m_redis_socket);
#else
        close(m_redis_socket);
#endif
        m_redis_socket = -1;
    }
    m_connected.store(false);
}

std::string GlobalVariableTask::generate_redis_key(const std::string& variable_name) const {
    std::string key = m_redis_config.instance_key_format;
    
    // 替换占位符
    size_t pos = key.find("{instance_id}");
    if (pos != std::string::npos) {
        key.replace(pos, 13, m_instance_id);
    }
    
    pos = key.find("{variable_name}");
    if (pos != std::string::npos) {
        key.replace(pos, 15, variable_name);
    }
    
    return m_redis_config.key_prefix + key;
}

ATE_EC GlobalVariableTask::send_redis_command(const std::string& command, std::string& response) const {
    if (m_redis_socket < 0 || !m_connected.load()) {
        return ATE_ERROR_CONNECTION_LOST;
    }
    
    // 发送命令
    ssize_t sent = send(m_redis_socket, command.c_str(), command.length(), 0);
    if (sent != static_cast<ssize_t>(command.length())) {
        return ATE_ERROR_COMMUNICATION_ERROR;
    }
    
    // 接收响应
    char buffer[4096];
    ssize_t received = recv(m_redis_socket, buffer, sizeof(buffer) - 1, 0);
    if (received <= 0) {
        return ATE_ERROR_COMMUNICATION_TIMEOUT;
    }
    
    buffer[received] = '\0';
    response = std::string(buffer);
    
    return ATE_SUCCESS;
}

std::string GlobalVariableTask::serialize_variable(const VariableValue& value) const {
    std::ostringstream oss;
    oss << static_cast<int>(value.type) << "|" << value.getUnit() << "|" << (value.getShouldSave() ? "1" : "0") << "|";
    
    switch (value.type) {
        case VariableType::DOUBLE:
            oss << value.asDouble();
            break;
        case VariableType::STRING:
            oss << value.asString();
            break;
        case VariableType::BOOL:
            oss << (value.asBool() ? "1" : "0");
            break;
        case VariableType::INT:
            oss << value.asInt();
            break;
    }
    
    return oss.str();
}

ATE_EC GlobalVariableTask::deserialize_variable(const std::string& data, VariableValue& value) const {
    std::istringstream iss(data);
    std::string type_str, unit_str, should_save_str, value_str;
    
    if (!std::getline(iss, type_str, '|') || 
        !std::getline(iss, unit_str, '|') || 
        !std::getline(iss, should_save_str, '|') ||
        !std::getline(iss, value_str)) {
        return ATE_ERROR_DATA_INVALID;
    }
    
    try {
        int type_int = std::stoi(type_str);
        VariableType type = static_cast<VariableType>(type_int);
        bool should_save = (should_save_str == "1");
        
        switch (type) {
            case VariableType::DOUBLE:
                value = VariableValue(std::stod(value_str), unit_str);
                break;
            case VariableType::STRING:
                value = VariableValue(value_str, unit_str);
                break;
            case VariableType::BOOL:
                value = VariableValue(value_str == "1", unit_str);
                break;
            case VariableType::INT:
                value = VariableValue(std::stoi(value_str), unit_str);
                break;
            default:
                return ATE_ERROR_DATA_INVALID;
        }
        value.setShouldSave(should_save);
    } catch (...) {
        return ATE_ERROR_CONVERSION_FAILED;
    }
    
    return ATE_SUCCESS;
}

ATE_EC GlobalVariableTask::set_variable(const std::string& name, const VariableValue& value) {
    if (!m_initialized.load()) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    // 更新本地缓存
    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        m_variables[name] = value;
    }
    
    // 同步到Redis
    if (m_connected.load()) {
        std::lock_guard<std::mutex> redis_lock(m_redis_mutex);
        std::string redis_key = generate_redis_key(name);
        std::string serialized_value = serialize_variable(value);
        
        std::string set_cmd = "SET " + redis_key + " " + serialized_value + "\r\n";
        std::string response;
        
        ATE_EC result = send_redis_command(set_cmd, response);
        if (result != ATE_SUCCESS || response.find("+OK") == std::string::npos) {
            return ATE_ERROR_COMMUNICATION_ERROR;
        }
    }
    
    return ATE_SUCCESS;
}

ATE_EC GlobalVariableTask::get_variable(const std::string& name, VariableValue& value) const {
    if (!m_initialized.load()) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    // 首先尝试从本地缓存获取
    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        auto it = m_variables.find(name);
        if (it != m_variables.end()) {
            value = it->second;
            return ATE_SUCCESS;
        }
    }
    
    // 如果本地缓存没有，尝试从Redis获取
    if (m_connected.load()) {
        std::lock_guard<std::mutex> redis_lock(m_redis_mutex);
        std::string redis_key = generate_redis_key(name);
        
        std::string get_cmd = "GET " + redis_key + "\r\n";
        std::string response;
        
        ATE_EC result = send_redis_command(get_cmd, response);
        if (result == ATE_SUCCESS && response.find("$-1") == std::string::npos) {
            // 解析响应
            size_t start = response.find('\n') + 1;
            size_t end = response.find("\r\n", start);
            if (start < response.length() && end != std::string::npos) {
                std::string data = response.substr(start, end - start);
                result = deserialize_variable(data, value);
                if (result == ATE_SUCCESS) {
                    // 更新本地缓存
                    std::lock_guard<std::mutex> lock(m_variables_mutex);
                    m_variables[name] = value;
                    return ATE_SUCCESS;
                }
            }
        }
    }
    
    return ATE_ERROR_DATA_INVALID;
}

bool GlobalVariableTask::has_variable(const std::string& name) const {
    if (!m_initialized.load()) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    return m_variables.find(name) != m_variables.end();
}

ATE_EC GlobalVariableTask::remove_variable(const std::string& name) {
    if (!m_initialized.load()) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    // 从本地缓存删除
    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        m_variables.erase(name);
    }
    
    // 从Redis删除
    if (m_connected.load()) {
        std::lock_guard<std::mutex> redis_lock(m_redis_mutex);
        std::string redis_key = generate_redis_key(name);
        
        std::string del_cmd = "DEL " + redis_key + "\r\n";
        std::string response;
        
        send_redis_command(del_cmd, response);
    }
    
    return ATE_SUCCESS;
}

ATE_EC GlobalVariableTask::clear_all_variables() {
    if (!m_initialized.load()) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    m_variables.clear();
    
    return ATE_SUCCESS;
}

std::vector<std::string> GlobalVariableTask::get_variable_names() const {
    std::vector<std::string> names;
    
    if (m_initialized.load()) {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        names.reserve(m_variables.size());
        
        for (const auto& pair : m_variables) {
            names.push_back(pair.first);
        }
    }
    
    return names;
}

size_t GlobalVariableTask::get_variable_count() const {
    if (!m_initialized.load()) {
        return 0;
    }
    
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    return m_variables.size();
}

ATE_EC GlobalVariableTask::sync_from_redis() {
    if (!m_initialized.load() || !m_connected.load()) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    // 这里可以实现从Redis批量同步的逻辑
    // 由于Redis原生协议比较复杂，这里简化实现
    return ATE_SUCCESS;
}

ATE_EC GlobalVariableTask::sync_to_redis() {
    if (!m_initialized.load() || !m_connected.load()) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    
    for (const auto& pair : m_variables) {
        std::string redis_key = generate_redis_key(pair.first);
        std::string serialized_value = serialize_variable(pair.second);
        
        std::string set_cmd = "SET " + redis_key + " " + serialized_value + "\r\n";
        std::string response;
        
        send_redis_command(set_cmd, response);
    }
    
    return ATE_SUCCESS;
}

const std::string& GlobalVariableTask::get_instance_id() const {
    return m_instance_id;
}

bool GlobalVariableTask::is_connected() const {
    return m_connected.load();
}

// 类型特化的便捷方法
ATE_EC GlobalVariableTask::set_double(const std::string& name, double value, const std::string& unit) {
    VariableValue var_value(value, unit);
    var_value.setShouldSave(true);
    return set_variable(name, var_value);
}

ATE_EC GlobalVariableTask::set_string(const std::string& name, const std::string& value, const std::string& unit) {
    VariableValue var_value(value, unit);
    var_value.setShouldSave(true);
    return set_variable(name, var_value);
}

ATE_EC GlobalVariableTask::set_bool(const std::string& name, bool value, const std::string& unit) {
    VariableValue var_value(value, unit);
    var_value.setShouldSave(true);
    return set_variable(name, var_value);
}

ATE_EC GlobalVariableTask::set_int(const std::string& name, int value, const std::string& unit) {
    VariableValue var_value(value, unit);
    var_value.setShouldSave(true);
    return set_variable(name, var_value);
}

ATE_EC GlobalVariableTask::get_double(const std::string& name, double& value) const {
    VariableValue var_value;
    ATE_EC result = get_variable(name, var_value);
    if (result == ATE_SUCCESS) {
        value = var_value.asDouble();
    }
    return result;
}

ATE_EC GlobalVariableTask::get_string(const std::string& name, std::string& value) const {
    VariableValue var_value;
    ATE_EC result = get_variable(name, var_value);
    if (result == ATE_SUCCESS) {
        value = var_value.asString();
    }
    return result;
}

ATE_EC GlobalVariableTask::get_bool(const std::string& name, bool& value) const {
    VariableValue var_value;
    ATE_EC result = get_variable(name, var_value);
    if (result == ATE_SUCCESS) {
        value = var_value.asBool();
    }
    return result;
}

ATE_EC GlobalVariableTask::get_int(const std::string& name, int& value) const {
    VariableValue var_value;
    ATE_EC result = get_variable(name, var_value);
    if (result == ATE_SUCCESS) {
        value = var_value.asInt();
    }
    return result;
}