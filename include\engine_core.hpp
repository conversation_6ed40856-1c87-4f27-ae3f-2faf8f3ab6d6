/**
 * @file engine_core.hpp
 * @brief 引擎核心类定义文件
 * <AUTHOR> Framework Team
 * @version 1.0
 * @date 2024
 */

#ifndef ENGINE_CORE_HPP
#define ENGINE_CORE_HPP

#include "common_types.hpp"
#include "engine_variable_system.hpp"
#include "global_variable_task.hpp"
#include "data_recorder.hpp"
#include "device_manager.hpp"
#include "report_process.hpp"
#include <algorithm>
#include <memory>
#include <string>
#include <vector>
#include <mutex>
#include <atomic>
#include <thread>
#include <chrono>

/**
 * @brief 引擎核心配置结构体
 * @details 基于engine_manager_config.json中的engine_cores字段定义的配置结构
 */
struct EngineCoreConfig {
    int engine_id = 0;                          ///< 引擎ID
    std::string engine_name = "";               ///< 引擎名称
    std::string test_project_package_path = ""; ///< 测试项目包路径
    std::string test_report_storage_path = "";  ///< 测试报告存储路径
    std::string test_data_storage_path = "";    ///< 测试数据存储路径
    std::string log_level = "INFO";             ///< 日志级别
    std::string config_json_string = "";        ///< 配置字符串保存
    
    /**
     * @brief 检查配置是否有效
     * @return true 配置有效
     * @return false 配置无效
     */
    bool is_valid() const {
        return engine_id > 0 && 
               !engine_name.empty() && 
               !test_project_package_path.empty() &&
               !test_report_storage_path.empty() &&
               !test_data_storage_path.empty();
    }
};

/**
 * @brief 引擎核心类
 * @details 作为整个引擎系统的核心控制器，负责协调各个子系统的工作，
 *          包括变量系统、数据记录器、设备管理器等
 */
class EngineCore {
public:
    /**
     * @brief 构造函数，初始化引擎核心
     * @param config 引擎配置参数，默认为空配置
     */
    explicit EngineCore(const EngineCoreConfig& config = EngineCoreConfig{});
    
    /**
     * @brief 析构函数，清理资源
     * @details 自动停止所有线程，释放子系统资源，确保安全关闭
     */
    ~EngineCore();
    
    /// 禁用拷贝和移动
    EngineCore(const EngineCore&) = delete;
    EngineCore& operator=(const EngineCore&) = delete;
    EngineCore(EngineCore&&) = delete;
    EngineCore& operator=(EngineCore&&) = delete;
    
    /**
     * @brief 初始化引擎核心
     * @param dll_directory_path DLL目录路径，默认为空使用当前目录
     * @return ATE_EC 错误代码，ATE_SUCCESS表示成功
     * @details 加载DLL模块并初始化所有子系统，包括变量系统、数据记录器等
     */
    ATE_EC initialize(const std::string& dll_directory_path = "");
    
    /**
     * @brief 启动引擎核心系统
     * @return ATE_EC 错误代码，ATE_SUCCESS表示成功
     * @details 启动所有子系统和监控线程，将引擎状态设置为运行中
     */
    ATE_EC start_engine();
    
    /**
     * @brief 执行Lua脚本
     * @param script_content 脚本内容
     * @param script_name 脚本名称（可选，默认为"main_script"）
     * @param script_type 脚本类型（可选，默认为"MA_SCRIPT"）
     * @return ATE_EC 错误代码，ATE_SUCCESS表示成功
     * @details 执行指定的Lua脚本，设置钩子函数进行执行控制
     */
    ATE_EC execute_script(const std::string& script_content, const std::string& script_name = "main_script", const std::string& script_type = "MA_SCRIPT");
    
    /**
     * @brief 停止引擎核心系统
     * @return ATE_EC 错误代码，ATE_SUCCESS表示成功
     * @details 停止所有子系统和线程，保存当前状态，安全关闭引擎
     */
    ATE_EC stop_engine();
    
    /**
     * @brief 终止当前正在执行的脚本
     * @return ATE_EC 错误代码，ATE_SUCCESS表示成功
     * @details 强制终止脚本执行，清理相关资源
     */
    ATE_EC terminate_script();
    
    /**
     * @brief 挂起引擎核心系统
     * @return ATE_EC 错误代码，ATE_SUCCESS表示成功
     * @details 暂停引擎运行，保持当前状态，可以通过resume_engine恢复
     */
    ATE_EC suspend_engine();
    
    /**
     * @brief 暂停当前正在执行的脚本
     * @return ATE_EC 错误代码，ATE_SUCCESS表示成功
     * @details 暂停脚本执行，保持当前执行状态
     */
    ATE_EC pause_script();
    
    /**
     * @brief 恢复引擎核心系统
     * @return ATE_EC 错误代码，ATE_SUCCESS表示成功
     * @details 从挂起状态恢复引擎运行
     */
    ATE_EC resume_engine();
    
    /**
     * @brief 恢复当前暂停的脚本
     * @return ATE_EC 错误代码，ATE_SUCCESS表示成功
     * @details 从暂停状态恢复脚本执行
     */
    ATE_EC resume_script();
    
    /**
     * @brief 清理引擎资源
     * @return ATE_EC 错误代码，ATE_SUCCESS表示成功
     * @details 清理所有资源，释放内存，关闭文件句柄等
     */
    ATE_EC cleanup();
    
    /**
     * @brief 在脚本中设置断点
     * @param line_number 断点行号
     * @param script_name 脚本名称（可选，默认为当前执行的脚本）
     * @return ATE_EC 错误代码，ATE_SUCCESS表示成功
     * @details 在指定行号设置断点，当脚本执行到该行时停止，并返回所有变量的JSON格式数据
     */
    ATE_EC set_script_breakpoint(int line_number, const std::string& script_name = "");
    
    /**
     * @brief 获取变量快照的JSON格式数据
     * @return std::string JSON格式的变量数据
     * @details 返回当前所有变量的JSON格式快照，用于调试和状态检查
     */
    std::string get_variables_snapshot_json() const;
    
    /**
     * @brief 获取脚本执行状态
     * @return LuaExecutionState 当前脚本执行状态
     */
    LuaExecutionState get_script_execution_state() const;
    
    /**
     * @brief 获取当前引擎状态
     * @return FSMState 当前引擎状态
     * @details 返回引擎状态机的当前状态，表示引擎的运行阶段
     */
    FSMState get_engine_state() const;
    
    /// ========================================================================
    /// 变量系统接口
    /// ========================================================================
    
    /// 获取变量系统引用
    EngineVariableSystem& get_variable_system();
    
    /**
     * @brief 设置变量值（模板方法）
     * @tparam T 变量值的类型
     * @param name 变量名称
     * @param value 变量值
     * @param unit 变量单位，默认为空
     * @param save_to_redis 是否保存到Redis，默认为false
     * @return ATE_EC 错误代码，ATE_SUCCESS表示成功
     * @details 通过模板方法设置任意类型的变量值到全局变量系统
     */
    template<typename T>
    ATE_EC set_variable(const std::string& name, const T& value, 
                       const std::string& unit = "", bool save_to_redis = false) {
        if (!m_variable_system) {
            return ATE_ERROR_NOT_INITIALIZED;
        }
        return m_variable_system->set_variable(name, value, unit, save_to_redis);
    }
    
    /**
     * @brief 获取变量值（模板方法）
     * @tparam T 变量值的类型
     * @param name 变量名称
     * @param value 输出参数，用于接收变量值
     * @return ATE_EC 错误代码，ATE_SUCCESS表示成功
     * @details 通过模板方法从全局变量系统获取指定类型的变量值
     */
    template<typename T>
    ATE_EC get_variable(const std::string& name, T& value) {
        if (!m_variable_system) {
            return ATE_ERROR_NOT_INITIALIZED;
        }
        return m_variable_system->get_variable(name, value);
    }
    
    /// ========================================================================
    /// 数据记录器接口
    /// ========================================================================
    
    /// 获取数据记录器引用
    DataRecorder& get_data_recorder();
    
    /**
     * @brief 开始数据记录
     * @param config_name 记录配置名称
     * @return ATE_EC 错误代码，ATE_SUCCESS表示成功
     * @details 启动数据记录功能，使用指定的配置
     */
    ATE_EC begin_data_recording(const std::string& config_name);
    
    /**
     * @brief 结束数据记录
     * @return ATE_EC 错误代码，ATE_SUCCESS表示成功
     * @details 结束当前的数据记录，保存记录的数据
     */
    ATE_EC end_data_recording();
    
    /**
     * @brief 添加变量到记录列表
     * @param variable_name 要记录的变量名称
     * @return ATE_EC 错误代码，ATE_SUCCESS表示成功
     * @details 将指定变量添加到数据记录器的监控列表中
     */
    ATE_EC add_variable_to_record(const std::string& variable_name);
    
    /// ========================================================================
    /// 配置管理
    /// ========================================================================
    
    /// 获取当前配置信息
    const EngineCoreConfig& get_config() const;
    
    /**
     * @brief 更新配置信息
     * @param config 新的引擎配置
     * @return ATE_EC 错误代码，ATE_SUCCESS表示成功
     * @details 更新引擎配置并重新初始化相关子系统
     */
    ATE_EC update_config(const EngineCoreConfig& config);
    
    /// ========================================================================
    /// 状态监控和诊断
    /// ========================================================================
    
    /**
     * @brief 获取引擎运行时统计信息
     * @return std::string JSON格式的统计信息
     * @details 返回引擎运行时的各种统计数据，包括运行时间、处理任务数等
     */
    std::string get_runtime_statistics() const;
    
    /**
     * @brief 获取性能指标
     * @return std::string JSON格式的性能指标
     * @details 返回引擎的性能指标，包括CPU使用率、内存使用量等
     */
    std::string get_performance_metrics() const;
    
    /**
     * @brief 获取错误日志列表
     * @param max_entries 最大条目数，默认为100
     * @return std::vector<std::string> 错误日志列表
     * @details 返回最近的错误日志记录，用于故障诊断
     */
    std::vector<std::string> get_error_logs(int max_entries = 100) const;
    
    /**
     * @brief 执行系统自检
     * @return ATE_EC 错误代码，ATE_SUCCESS表示自检通过
     * @details 对引擎的各个子系统进行健康检查
     */
    ATE_EC perform_self_test();
    
    /**
     * @brief 获取最后的错误信息
     * @return std::string 最后的错误信息
     * @details 返回引擎最后一次发生的错误信息
     */
    std::string get_last_error() const;
    
    /**
     * @brief 获取系统状态
     * @return std::string 系统状态信息
     * @details 返回引擎当前的系统状态信息
     */
    std::string get_system_status() const;
    
private:
    /// ========================================================================
    /// 私有成员变量
    /// ========================================================================
    
    EngineCoreConfig m_config;                              ///< 引擎配置
    std::atomic<FSMState> m_state{FSMState::TERMINATED}; ///< 引擎状态
    std::atomic<bool> m_shutdown_requested{false};          ///< 关闭请求标志
    std::atomic<bool> m_cleanup_requested{false};           ///< 清理请求标志
    
    /// 子系统
    std::unique_ptr<EngineVariableSystem> m_variable_system; ///< 变量系统
    std::unique_ptr<GlobalVariableTask> m_global_variable_system; ///< 全局变量系统（用于DataRecorder）
    std::unique_ptr<DataRecorder> m_data_recorder;          ///< 数据记录器
    std::unique_ptr<ReportProcess> m_report_process;        ///< 报告处理系统
    
    /// 线程和同步
    std::unique_ptr<std::thread> m_status_monitor_thread;   ///< 状态监控线程
    std::unique_ptr<std::thread> m_heartbeat_thread;        ///< 心跳线程
    mutable std::mutex m_mutex;                             ///< 互斥锁
    
    /// 状态信息
    std::string m_last_error;                               ///< 最后错误信息
    std::chrono::system_clock::time_point m_last_heartbeat; ///< 最后心跳时间
    
    /// ========================================================================
    /// 私有成员函数
    /// ========================================================================
    
    /**
     * @brief 状态监控线程函数
     * @details 定期检查引擎和子系统状态，处理异常情况
     */
    void status_monitor_thread_function();
    
    /**
     * @brief 心跳线程函数
     * @details 定期发送心跳信号，维护引擎活跃状态
     */
    void heartbeat_thread_function();
    
    /// 设置引擎状态
    void set_state(FSMState new_state);
    
    /// 设置最后错误信息
    void set_last_error(const std::string& error_message);
    
    /**
     * @brief 检查子系统状态
     * @return bool 所有子系统是否正常
     * @details 检查变量系统、数据记录器等子系统的健康状态
     */
    bool check_subsystems_health() const;
    
    /**
     * @brief 初始化子系统
     * @return ATE_EC 错误代码
     * @details 按顺序初始化所有子系统，确保依赖关系正确
     */
    ATE_EC initialize_subsystems();
    
    /**
     * @brief 关闭子系统
     * @details 安全关闭所有子系统，释放相关资源
     */
    void shutdown_subsystems();
};

#endif // ENGINE_CORE_HPP