#include "engine_variable_system.hpp"
#include "engine_framework_interface.hpp"
#include "global_variable_service.hpp"
#include "lua.h"
#include <stdexcept>
#include <algorithm>
#include <unordered_set>

// ========================================================================
// 优化的静态钩子函数 - 使用thread_local static避免多实例冲突
// ========================================================================

// 为MA脚本优化的静态包装函数，使用thread_local static确保线程安全
void EngineVariableSystem::lua_hook_call_MA(lua_State* L, lua_Debug* ar) {
    // 使用thread_local static变量，每个线程独立缓存
    thread_local static EngineVariableSystem* cached_instance = nullptr;
    
    // 第一次调用时获取实例指针，后续直接使用缓存
    if (cached_instance == nullptr) {
        lua_getfield(L, LUA_REGISTRYINDEX, "EngineVariableSystem_Instance");
        cached_instance = static_cast<EngineVariableSystem*>(lua_touserdata(L, -1));
        lua_pop(L, 1);
    }
    
    if (cached_instance && ar->event == LUA_HOOKLINE) {
        // 直接内联lua_hook_callback的内容，专门针对MA脚本优化
        int line_number = ar->currentline;
        
        // 检查停止请求
        if (cached_instance->m_MA_stop_requested.load()) {
            luaL_error(L, "Script execution stopped");
            return;
        }

        // 检查挂起请求
        if (cached_instance->m_MA_suspend_requested.load()) {
            cached_instance->m_MA_execution_context.state = LuaExecutionState::SUSPENDED;
            
            std::unique_lock<std::mutex> lock(cached_instance->m_MA_execution_mutex);
            cached_instance->m_MA_execution_cv.wait(lock, [&]() {
                return !cached_instance->m_MA_suspend_requested.load() || cached_instance->m_MA_stop_requested.load();
            });
            
            if (cached_instance->m_MA_stop_requested.load()) {
                luaL_error(L, "Script execution stopped");
                return;
            }
            
            cached_instance->m_MA_execution_context.state = LuaExecutionState::RUNNING;
        }

        // 检查断点
        bool should_break = false;
        {
            std::lock_guard<std::mutex> lock(cached_instance->m_breakpoints_mutex);
            for (const auto& breakpoint : cached_instance->m_MA_breakpoints) {
                if (breakpoint.line_number == line_number && breakpoint.enabled) {
                    should_break = true;
                    break;
                }
            }
        }
        
        if (should_break) {
            cached_instance->m_MA_execution_context.state = LuaExecutionState::DEBUG_BREAK;
            cached_instance->m_MA_execution_context.current_line = line_number;
            
            std::unique_lock<std::mutex> lock(cached_instance->m_MA_execution_mutex);
            cached_instance->m_MA_execution_cv.wait(lock, [&]() {
                return cached_instance->m_MA_execution_context.state != LuaExecutionState::DEBUG_BREAK;
            });
            
            if (cached_instance->m_MA_stop_requested.load()) {
                luaL_error(L, "Script execution stopped");
                return;
            }
        }

        // 更新当前行号
        cached_instance->m_MA_execution_context.current_line = line_number;
    }
}

// 为PR脚本优化的静态包装函数，使用thread_local static确保线程安全
void EngineVariableSystem::lua_hook_call_PR(lua_State* L, lua_Debug* ar) {
    // 使用thread_local static变量，每个线程独立缓存
    thread_local static EngineVariableSystem* cached_instance = nullptr;
    
    // 第一次调用时获取实例指针，后续直接使用缓存
    if (cached_instance == nullptr) {
        lua_getfield(L, LUA_REGISTRYINDEX, "EngineVariableSystem_Instance");
        cached_instance = static_cast<EngineVariableSystem*>(lua_touserdata(L, -1));
        lua_pop(L, 1);
    }
    
    if (cached_instance && ar->event == LUA_HOOKLINE) {
        // 直接内联lua_hook_callback的内容，专门针对PR脚本优化
        int line_number = ar->currentline;
        
        // 检查停止请求
        if (cached_instance->m_PR_stop_requested.load()) {
            luaL_error(L, "Script execution stopped");
            return;
        }

        // 检查挂起请求
        if (cached_instance->m_PR_suspend_requested.load()) {
            cached_instance->m_PR_execution_context.state = LuaExecutionState::SUSPENDED;
            
            std::unique_lock<std::mutex> lock(cached_instance->m_PR_execution_mutex);
            cached_instance->m_PR_execution_cv.wait(lock, [&]() {
                return !cached_instance->m_PR_suspend_requested.load() || cached_instance->m_PR_stop_requested.load();
            });
            
            if (cached_instance->m_PR_stop_requested.load()) {
                luaL_error(L, "Script execution stopped");
                return;
            }
            
            cached_instance->m_PR_execution_context.state = LuaExecutionState::RUNNING;
        }

        // 检查断点
        bool should_break = false;
        {
            std::lock_guard<std::mutex> lock(cached_instance->m_breakpoints_mutex);
            for (const auto& breakpoint : cached_instance->m_PR_breakpoints) {
                if (breakpoint.line_number == line_number && breakpoint.enabled) {
                    should_break = true;
                    break;
                }
            }
        }
        
        if (should_break) {
            cached_instance->m_PR_execution_context.state = LuaExecutionState::DEBUG_BREAK;
            cached_instance->m_PR_execution_context.current_line = line_number;
            
            std::unique_lock<std::mutex> lock(cached_instance->m_PR_execution_mutex);
            cached_instance->m_PR_execution_cv.wait(lock, [&]() {
                return cached_instance->m_PR_execution_context.state != LuaExecutionState::DEBUG_BREAK;
            });
            
            if (cached_instance->m_PR_stop_requested.load()) {
                luaL_error(L, "Script execution stopped");
                return;
            }
        }

        // 更新当前行号
        cached_instance->m_PR_execution_context.current_line = line_number;
    }
}

EngineVariableSystem::EngineVariableSystem() {
    // 初始化Lua
    m_MA_lua.open_libraries();
    m_PR_lua.open_libraries();
    
    // 预留容器空间以提高性能
    m_variables.reserve(1000);

    std::cout << "EngineVariableSystem initialized" << std::endl;
}

EngineVariableSystem::~EngineVariableSystem() noexcept {
    try {
        std::cout << "EngineVariableSystem destroyed" << std::endl;
    } catch (...) {
        // 析构函数中不应抛出异常
        // 记录错误但不重新抛出
    }
}

// 变量管理方法
bool EngineVariableSystem::set_variable(const std::string& name, const VariableValue& value) {
    if (name.empty()) {
        throw std::invalid_argument("Variable name cannot be empty");
    }

    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        // 检查是否已存在同名变量
        if (m_variables.find(name) != m_variables.end()) {
            return false; // 存在同名变量，插入失败
        }
        m_variables[name] = value;
    }
    std::cout << "Set variable '" << name << "' = " << get_type_name(value) << std::endl;
    return true; // 成功插入
}

VariableValue EngineVariableSystem::get_variable(const std::string& name) const {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    auto it = m_variables.find(name);
    if (it != m_variables.end()) {
        return it->second;
    }
    throw std::runtime_error("Variable '" + name + "' not found");
}

bool EngineVariableSystem::has_variable(const std::string& name) const {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    return m_variables.find(name) != m_variables.end();
}

void EngineVariableSystem::remove_variable(const std::string& name) {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    auto it = m_variables.find(name);
    if (it != m_variables.end()) {
        m_variables.erase(it);
        std::cout << "Removed variable '" << name << "'" << std::endl;
    }
}

void EngineVariableSystem::clear_all_variables() {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    size_t count = m_variables.size();
    m_variables.clear();
    std::cout << "Cleared " << count << " variables" << std::endl;
}

// 高性能的类型特化设置方法（避免variant构造开销）
void EngineVariableSystem::set_number(const std::string& name, double value) {
    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        m_variables[name] = VariableValue(value);
    }
}

void EngineVariableSystem::set_string(const std::string& name, const std::string& value) {
    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        m_variables[name] = VariableValue(value);
    }
}

void EngineVariableSystem::set_bool(const std::string& name, bool value) {
    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        m_variables[name] = VariableValue(value);
    }
}

void EngineVariableSystem::set_int(const std::string& name, int value) {
    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        m_variables[name] = VariableValue(value);
    }
}

// 带单位的设置方法
void EngineVariableSystem::set_number(const std::string& name, double value, const std::string& unit) {
    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        m_variables[name] = VariableValue(value, unit);
    }
}

void EngineVariableSystem::set_string(const std::string& name, const std::string& value, const std::string& unit) {
    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        m_variables[name] = VariableValue(value, unit);
    }
}

void EngineVariableSystem::set_bool(const std::string& name, bool value, const std::string& unit) {
    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        m_variables[name] = VariableValue(value, unit);
    }
}

void EngineVariableSystem::set_int(const std::string& name, int value, const std::string& unit) {
    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        m_variables[name] = VariableValue(value, unit);
    }
}

// 单位管理方法
void EngineVariableSystem::set_variable_unit(const std::string& name, const std::string& unit) {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    auto it = m_variables.find(name);
    if (it != m_variables.end()) {
        it->second.setUnit(unit);
    }
}

std::string EngineVariableSystem::get_variable_unit(const std::string& name) const {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    auto it = m_variables.find(name);
    if (it != m_variables.end()) {
        return it->second.getUnit();
    }
    return "";
}



// 获取变量保存状态
bool EngineVariableSystem::get_variable_save_status(const std::string& name) const {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    auto it = m_variables.find(name);
    if (it != m_variables.end()) {
        return it->second.getShouldSave();
    }
    return false;
}

// 获取所有需要保存的变量名列表
std::vector<std::string> EngineVariableSystem::get_saveable_variable_names() const {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    std::vector<std::string> saveable_names;
    for (const auto& pair : m_variables) {
        if (pair.second.getShouldSave()) {
            saveable_names.push_back(pair.first);
        }
    }
    return saveable_names;
}

// DataRecorder专用接口 - 设置变量保存标志
bool EngineVariableSystem::set_variable_save_status(const std::string& name, bool should_save) {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    auto it = m_variables.find(name);
    if (it != m_variables.end()) {
        it->second.setShouldSave(should_save);
        return true;
    }
    return false;
}

// DataRecorder专用接口 - 获取变量数据指针
void* EngineVariableSystem::get_variable_data_pointer(const std::string& name) {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    auto it = m_variables.find(name);
    if (it != m_variables.end()) {
        VariableValue& value = it->second;
        switch (value.type) {
            case VariableType::DOUBLE:
                return &(value.data.doubleValue);
            case VariableType::INT:
                return &(value.data.intValue);
            case VariableType::BOOL:
                return &(value.data.boolValue);
            case VariableType::STRING:
                return value.stringData.get();
            default:
                return nullptr;
        }
    }
    return nullptr;
}

// 获取所有变量信息列表
std::vector<EngineVariableSystem::VariableInfo> EngineVariableSystem::list_variables() const {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    std::vector<VariableInfo> variable_list;
    
    for (const auto& pair : m_variables) {
        const std::string& name = pair.first;
        const VariableValue& value = pair.second;
        
        variable_list.emplace_back(
            name,
            value.getTypeName(),
            value.getShouldSave(),
            value.getUnit()
        );
    }
    
    return variable_list;
}

// 类型特化的获取方法
double EngineVariableSystem::get_number(const std::string& name, double defaultValue) const {
    if (!has_variable(name)) return defaultValue;
    
    try {
        auto value = get_variable(name);
        return value.asDouble();
    } catch (...) {
        return defaultValue;
    }
}

std::string EngineVariableSystem::get_string(const std::string& name, const std::string& defaultValue) const {
    if (!has_variable(name)) return defaultValue;
    
    try {
        auto value = get_variable(name);
        return value.asString();
    } catch (...) {
        return defaultValue;
    }
}

bool EngineVariableSystem::get_bool(const std::string& name, bool defaultValue) const {
    if (!has_variable(name)) return defaultValue;
    
    try {
        auto value = get_variable(name);
        return value.asBool();
    } catch (...) {
        return defaultValue;
    }
}

int EngineVariableSystem::get_int(const std::string& name, int defaultValue) const {
    if (!has_variable(name)) return defaultValue;
    
    try {
        auto value = get_variable(name);
        return value.asInt();
    } catch (...) {
        return defaultValue;
    }
}

// 调试和信息方法
std::string EngineVariableSystem::get_all_variables() const {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    std::stringstream ss;
    ss << "=== Engine Variable System Status ===" << std::endl;
    ss << "Total variables: " << m_variables.size() << std::endl;

    for (const auto& [name, value] : m_variables) {
        ss << "  " << name << " = " << value.asString();
        ss << " (" << value.getTypeName() << ")";
        if (!value.getUnit().empty()) {
            ss << " [" << value.getUnit() << "]";
        }
        ss << std::endl;
    }
    ss << "===============================" << std::endl;
    return ss.str();
}

size_t EngineVariableSystem::get_variable_count() const noexcept {
    try {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        return m_variables.size();
    } catch (...) {
        return 0;
    }
}

// 辅助方法
std::string EngineVariableSystem::get_type_name(const VariableValue& value) const {
    return value.getTypeName();
}

sol::object EngineVariableSystem::push_variable_to_MA_sol(const VariableValue& value) const {
    switch (value.type) {
        case VariableType::DOUBLE:
            return sol::make_object(m_MA_lua, value.data.doubleValue);
        case VariableType::STRING:
            return sol::make_object(m_MA_lua, value.stringData ? *value.stringData : std::string(""));
        case VariableType::BOOL:
            return sol::make_object(m_MA_lua, value.data.boolValue);
        case VariableType::INT:
            return sol::make_object(m_MA_lua, value.data.intValue);
        default:
            return sol::make_object(m_MA_lua, sol::nil);
    }
}

sol::object EngineVariableSystem::push_variable_to_PR_sol(const VariableValue& value) const {
    switch (value.type) {
        case VariableType::DOUBLE:
            return sol::make_object(m_PR_lua, value.data.doubleValue);
        case VariableType::STRING:
            return sol::make_object(m_PR_lua, value.stringData ? *value.stringData : std::string(""));  
        case VariableType::BOOL:
            return sol::make_object(m_PR_lua, value.data.boolValue);
        case VariableType::INT:
            return sol::make_object(m_PR_lua, value.data.intValue);
        default:
            return sol::make_object(m_PR_lua, sol::nil);
    }
}

VariableValue EngineVariableSystem::get_variable_from_MA_sol(const sol::object& obj) const {
    switch (obj.get_type()) {
        case sol::type::number:
            if (obj.is<int>()) {
                return VariableValue(obj.as<int>());
            } else {
                return VariableValue(obj.as<double>());
            }
        case sol::type::string:
            return VariableValue(obj.as<std::string>());
        case sol::type::boolean:
            return VariableValue(obj.as<bool>());
        default:
            return VariableValue();  // 返回默认值
    }
}

VariableValue EngineVariableSystem::get_variable_from_PR_sol(const sol::object& obj) const {
    switch (obj.get_type()) {
        case sol::type::number:
            if (obj.is<int>()) {
                return VariableValue(obj.as<int>());
            } else {
                return VariableValue(obj.as<double>());
            }
        case sol::type::string:
            return VariableValue(obj.as<std::string>());
        case sol::type::boolean:
            return VariableValue(obj.as<bool>());
        default:
            return VariableValue();  // 返回默认值
    }
}

// Lua绑定实现
void EngineVariableSystem::setup_MA_lua_binding() {
    // ATE工业自动化测试场景下的自动同步实现
    // 通过重写__index和__newindex元方法实现C++变量系统与Lua的自动同步
    
    // 保存原始的全局表
    sol::table original_G = m_MA_lua.globals();
    
    // 创建新的元表来实现自动同步
    sol::table global_meta = m_MA_lua.create_table();
    
    // 重写__index元方法 - 只同步用户脚本变量
    global_meta["__index"] = [this, &original_G](sol::table t, const std::string& key) -> sol::object {
        // 首先检查原始全局表
        sol::object value = original_G.raw_get<sol::object>(key);
        if (value.valid() && value.get_type() != sol::type::lua_nil) {
            return value;
        }
        
        // 只同步用户脚本变量，过滤系统级变量
        if (this->is_user_script_variable(key)) {
            // 首先在引擎变量系统中查找
            if (this->has_variable(key)) {
                auto var_value = this->get_variable(key);
                return this->push_variable_to_MA_sol(var_value);
            }else{
                // 如果引擎变量系统中没有找到，从全局变量服务中查找
                auto& global_var_service = GlobalVariableService::get_instance();
                if (global_var_service.has_variable(key)) {
                    VariableValue var_value;
                    if (global_var_service.get_variable(key, var_value) == ATE_SUCCESS) {
                        return this->push_variable_to_MA_sol(var_value);
                    }
                }            
            }
        }
        
        return sol::nil;
    };
    
    // 重写__newindex元方法 - 只同步用户脚本变量
    global_meta["__newindex"] = [this, &original_G](sol::table t, const std::string& key, sol::object value) {
        // 使用rawset避免递归调用元方法
        lua_State* L = m_MA_lua.lua_state();
        
        // 获取全局表
        lua_pushglobaltable(L);
        
        // 推入key和value
        lua_pushstring(L, key.c_str());
        sol::stack::push(L, value);
        
        // 使用rawset设置，避免触发元方法
        lua_rawset(L, -3);
        
        // 弹出全局表
        lua_pop(L, 1);
        
        // 只同步用户脚本变量到C++变量系统
        if (this->is_user_script_variable(key)) {
            try {
                VariableValue var_value = this->get_variable_from_MA_sol(value);
                // 直接设置，不检查重名（按用户要求）
                std::lock_guard<std::mutex> lock(m_variables_mutex);
                m_variables[key] = var_value;
                std::cout << "Synced user variable '" << key << "' = " << this->get_type_name(var_value) << std::endl;
            } catch (const std::exception& e) {
                std::cout << "Error auto-syncing variable '" << key << "': " << e.what() << std::endl;
            }
        }
    };
    
    // 绑定engine_framework_interface函数到MA Lua状态机
    m_MA_lua.set_function("initialize_dll", initialize_dll);
    m_MA_lua.set_function("cleanup_dll", cleanup_dll);
    m_MA_lua.set_function("connect_device", connect_device);
    m_MA_lua.set_function("disconnect_device", disconnect_device);
    m_MA_lua.set_function("get_device_descriptor", get_device_descriptor);
    m_MA_lua.set_function("execute_command_unified", execute_command_unified);
    m_MA_lua.set_function("get_channel_state", get_channel_state);
    
    // 设置全局表的元表
    m_MA_lua.globals().set(sol::metatable_key, global_meta);
    
    std::cout << "ATE自动化测试变量系统绑定完成。已启用__index和__newindex自动同步。" << std::endl;
    std::cout << "已绑定engine_framework_interface的7个函数到MA Lua状态机。" << std::endl;
}

void EngineVariableSystem::setup_PR_lua_binding() {
    // ATE工业自动化测试场景下的自动同步实现
    // 通过重写__index和__newindex元方法实现C++变量系统与Lua的自动同步
    
    // 保存原始的全局表
    sol::table original_G = m_PR_lua.globals();
    
    // 创建新的元表来实现自动同步
    sol::table global_meta = m_PR_lua.create_table();
    
    // 重写__index元方法 - 只同步用户脚本变量
    global_meta["__index"] = [this, &original_G](sol::table t, const std::string& key) -> sol::object {
        // 首先检查原始全局表
        sol::object value = original_G.raw_get<sol::object>(key);
        if (value.valid() && value.get_type() != sol::type::lua_nil) {
            return value;
        }
        
        // 只同步用户脚本变量，过滤系统级变量
        if (this->is_user_script_variable(key)) {
            // 首先在引擎变量系统中查找
            if (this->has_variable(key)) {
                auto var_value = this->get_variable(key);
                return this->push_variable_to_PR_sol(var_value);
            }else{
                // 如果引擎变量系统中没有找到，从全局变量服务中查找
                auto& global_var_service = GlobalVariableService::get_instance();
                if (global_var_service.has_variable(key)) {
                    VariableValue var_value;
                    if (global_var_service.get_variable(key, var_value) == ATE_SUCCESS) {
                        return this->push_variable_to_PR_sol(var_value);
                    }
                }
            }          
        }
        return sol::nil;
    };
    
    // 重写__newindex元方法 - 只同步用户脚本变量
    global_meta["__newindex"] = [this, &original_G](sol::table t, const std::string& key, sol::object value) {
        // 使用rawset避免递归调用元方法
        lua_State* L = m_PR_lua.lua_state();
        
        // 获取全局表
        lua_pushglobaltable(L);
        
        // 推入key和value
        lua_pushstring(L, key.c_str());
        sol::stack::push(L, value);
        
        // 使用rawset设置，避免触发元方法
        lua_rawset(L, -3);
        
        // 弹出全局表
        lua_pop(L, 1);
        
        // 只同步用户脚本变量到C++变量系统
        if (this->is_user_script_variable(key)) {
            try {
                VariableValue var_value = this->get_variable_from_PR_sol(value);
                // 直接设置，不检查重名（按用户要求）
                std::lock_guard<std::mutex> lock(m_variables_mutex);
                m_variables[key] = var_value;
                std::cout << "Synced user variable '" << key << "' = " << this->get_type_name(var_value) << std::endl;
            } catch (const std::exception& e) {
                std::cout << "Error auto-syncing variable '" << key << "': " << e.what() << std::endl;
            }
        }
    };
    
    // 绑定engine_framework_interface函数到PR Lua状态机
    m_PR_lua.set_function("initialize_dll", initialize_dll);
    m_PR_lua.set_function("cleanup_dll", cleanup_dll);
    m_PR_lua.set_function("connect_device", connect_device);
    m_PR_lua.set_function("disconnect_device", disconnect_device);
    m_PR_lua.set_function("get_device_descriptor", get_device_descriptor);
    m_PR_lua.set_function("execute_command_unified", execute_command_unified);
    m_PR_lua.set_function("get_channel_state", get_channel_state);
    
    // 设置全局表的元表
    m_PR_lua.globals().set(sol::metatable_key, global_meta);
    
    std::cout << "ATE自动化测试变量系统绑定完成。已启用__index和__newindex自动同步。" << std::endl;
    std::cout << "已绑定engine_framework_interface的7个函数到PR Lua状态机。" << std::endl;
}

// 用户脚本变量过滤函数实现
bool EngineVariableSystem::is_user_script_variable(const std::string& variable_name) const {
    // 空变量名不是用户脚本变量
    if (variable_name.empty()) {
        return false;
    }
    
    // Lua系统级变量列表
    static const std::unordered_set<std::string> lua_system_variables = {
        "_G", "_VERSION", "_ENV", "package", "require", "dofile", "loadfile",
        "print", "type", "getmetatable", "setmetatable", "rawget", "rawset",
        "pairs", "ipairs", "next", "tostring", "tonumber", "error", "assert",
        "pcall", "xpcall", "select", "unpack", "table", "string", "math",
        "io", "os", "debug", "coroutine", "utf8", "load", "loadstring",
        "collectgarbage", "gcinfo", "newproxy", "module", "setfenv", "getfenv"
    };
    
    // 检查是否为Lua系统级变量
    if (lua_system_variables.find(variable_name) != lua_system_variables.end()) {
        return false;
    }
    
    // 其他变量都认为是用户脚本变量
    return true;
}

// ========================================================================
// Lua钩子函数实现
// ========================================================================

ATE_EC EngineVariableSystem::start_script_execution(const std::string& script_content, const std::string& script_name, LuaScriptType script_type) {
    std::lock_guard<std::mutex> lock(get_execution_mutex_ref(script_type));
    
    try {
        // 检查当前状态
        auto& execution_context = get_execution_context_ref(script_type);
        if (execution_context.state != LuaExecutionState::IDLE) {
            return ATE_ERROR_OPERATION_NOT_PERMITTED;
        }
        
        // 初始化执行上下文
        execution_context.state = LuaExecutionState::RUNNING;
        execution_context.current_script = script_content;
        execution_context.script_name = script_name;
        execution_context.current_line = 0;
        execution_context.last_error.clear();
        execution_context.start_time = std::chrono::system_clock::now();

        // 重置停止和挂起标志
        get_stop_requested_ref(script_type).store(false);
        get_suspend_requested_ref(script_type).store(false);

        // 选择对应的Lua状态机
        sol::state* lua_state = (script_type == LuaScriptType::MA_SCRIPT) ? &m_MA_lua : &m_PR_lua;

        // 根据脚本类型设置对应的专门钩子函数
        lua_State* L = lua_state->lua_state();
        
        // 将实例指针存储到Lua注册表中
        lua_pushlightuserdata(L, this);
        lua_setfield(L, LUA_REGISTRYINDEX, "EngineVariableSystem_Instance");
        
        if (script_type == LuaScriptType::MA_SCRIPT) {
            lua_sethook(L, lua_hook_call_MA, LUA_MASKLINE | LUA_MASKCOUNT, 1);
        } else {
            lua_sethook(L, lua_hook_call_PR, LUA_MASKLINE | LUA_MASKCOUNT, 1);
        }

        // 启动脚本执行线程
        std::unique_ptr<std::thread>& execution_thread = (script_type == LuaScriptType::MA_SCRIPT) ? m_MA_execution_thread : m_PR_execution_thread;
        
        execution_thread = std::make_unique<std::thread>([this, script_content, lua_state, script_type]() {
            try {
                lua_state->script(script_content);
                
                // 脚本正常执行完成
                std::lock_guard<std::mutex> lock(get_execution_mutex_ref(script_type));
                auto& exec_context = get_execution_context_ref(script_type);
                if (exec_context.state == LuaExecutionState::RUNNING) {
                    exec_context.state = LuaExecutionState::STOPPED;
                }
            } catch (const sol::error& e) {
                // 脚本执行错误
                std::lock_guard<std::mutex> lock(get_execution_mutex_ref(script_type));
                auto& exec_context = get_execution_context_ref(script_type);
                exec_context.state = LuaExecutionState::ERROR_STATE;
                exec_context.last_error = e.what();
                std::cerr << "Script execution error: " << e.what() << std::endl;
            }
        });
        
        return ATE_SUCCESS;
        
    } catch (const std::exception& e) {
        auto& execution_context = get_execution_context_ref(script_type);
        execution_context.state = LuaExecutionState::ERROR_STATE;
        execution_context.last_error = e.what();
        return ATE_ERROR_COMMAND_FAILED;
    }
    
    // 选择对应的Lua状态机
    sol::state* lua_state = (script_type == LuaScriptType::MA_SCRIPT) ? &m_MA_lua : &m_PR_lua;
    // 根据脚本类型设置对应的专门钩子函数
    lua_State* L = lua_state->lua_state();
    //执行完清理钩子
    lua_sethook(L, nullptr,0,0);
}



ATE_EC EngineVariableSystem::debug_script_to_line(int line_number, LuaScriptType script_type) {
    std::lock_guard<std::mutex> lock(m_breakpoints_mutex);
    
    // 创建临时断点
    DebugBreakpoint temp_breakpoint(line_number, "debug_to_line", true);
    get_breakpoints_ref(script_type).insert(temp_breakpoint);
    
    return ATE_SUCCESS;
}

ATE_EC EngineVariableSystem::add_debug_breakpoint(const DebugBreakpoint& breakpoint, LuaScriptType script_type) {
    std::lock_guard<std::mutex> lock(m_breakpoints_mutex);
    
    get_breakpoints_ref(script_type).insert(breakpoint);
    
    return ATE_SUCCESS;
}

ATE_EC EngineVariableSystem::remove_debug_breakpoint(int line_number, LuaScriptType script_type) {
    std::lock_guard<std::mutex> lock(m_breakpoints_mutex);
    
    auto& breakpoints = get_breakpoints_ref(script_type);
    auto it = std::find_if(breakpoints.begin(), breakpoints.end(),
        [line_number](const DebugBreakpoint& bp) {
            return bp.line_number == line_number;
        });
    
    if (it != breakpoints.end()) {
        breakpoints.erase(it);
        return ATE_SUCCESS;
    }
    
    return ATE_ERROR_NOT_FOUND;
}



// ========================================================================
// 辅助函数实现
// ========================================================================



std::mutex& EngineVariableSystem::get_execution_mutex_ref(LuaScriptType script_type) {
    return (script_type == LuaScriptType::MA_SCRIPT) ? m_MA_execution_mutex : m_PR_execution_mutex;
}

std::condition_variable& EngineVariableSystem::get_execution_cv_ref(LuaScriptType script_type) {
    return (script_type == LuaScriptType::MA_SCRIPT) ? m_MA_execution_cv : m_PR_execution_cv;
}

std::set<DebugBreakpoint>& EngineVariableSystem::get_breakpoints_ref(LuaScriptType script_type) {
    return (script_type == LuaScriptType::MA_SCRIPT) ? m_MA_breakpoints : m_PR_breakpoints;
}



// ========================================================================
// handle_lua_hook方法实现
// ========================================================================





ATE_EC EngineVariableSystem::stop_script_execution(LuaScriptType script_type) {
    std::lock_guard<std::mutex> lock(get_execution_mutex_ref(script_type));
    
    auto& execution_context = get_execution_context_ref(script_type);
    auto& stop_requested = get_stop_requested_ref(script_type);
    
    if (execution_context.state != LuaExecutionState::RUNNING && 
        execution_context.state != LuaExecutionState::SUSPENDED) {
        return ATE_ERROR_OPERATION_NOT_PERMITTED;
    }
    
    // 设置停止标志
    stop_requested.store(true);
    execution_context.state = LuaExecutionState::STOPPED;
    
    // 通知执行线程
    get_execution_cv_ref(script_type).notify_all();
    
    return ATE_SUCCESS;
}

ATE_EC EngineVariableSystem::suspend_script_execution(LuaScriptType script_type) {
    std::lock_guard<std::mutex> lock(get_execution_mutex_ref(script_type));
    
    auto& execution_context = get_execution_context_ref(script_type);
    auto& suspend_requested = get_suspend_requested_ref(script_type);
    
    if (execution_context.state != LuaExecutionState::RUNNING) {
        return ATE_ERROR_OPERATION_NOT_PERMITTED;
    }
    
    // 设置挂起标志
    suspend_requested.store(true);
    execution_context.state = LuaExecutionState::SUSPENDED;
    
    return ATE_SUCCESS;
}

ATE_EC EngineVariableSystem::resume_script_execution(LuaScriptType script_type) {
    std::lock_guard<std::mutex> lock(get_execution_mutex_ref(script_type));
    
    auto& execution_context = get_execution_context_ref(script_type);
    auto& suspend_requested = get_suspend_requested_ref(script_type);
    
    if (execution_context.state != LuaExecutionState::SUSPENDED) {
        return ATE_ERROR_OPERATION_NOT_PERMITTED;
    }
    
    // 清除挂起标志
    suspend_requested.store(false);
    execution_context.state = LuaExecutionState::RUNNING;
    
    // 通知执行线程
    get_execution_cv_ref(script_type).notify_all();
    
    return ATE_SUCCESS;
}



// ========================================================================
// 辅助函数实现
// ========================================================================

LuaExecutionContext& EngineVariableSystem::get_execution_context_ref(LuaScriptType script_type) {
    return (script_type == LuaScriptType::MA_SCRIPT) ? m_MA_execution_context : m_PR_execution_context;
}

sol::state& EngineVariableSystem::get_lua_state_ref(LuaScriptType script_type) {
    return (script_type == LuaScriptType::MA_SCRIPT) ? m_MA_lua : m_PR_lua;
}

std::atomic<bool>& EngineVariableSystem::get_stop_requested_ref(LuaScriptType script_type) {
    return (script_type == LuaScriptType::MA_SCRIPT) ? m_MA_stop_requested : m_PR_stop_requested;
}

std::atomic<bool>& EngineVariableSystem::get_suspend_requested_ref(LuaScriptType script_type) {
    return (script_type == LuaScriptType::MA_SCRIPT) ? m_MA_suspend_requested : m_PR_suspend_requested;
}

std::string EngineVariableSystem::get_all_variables_json() const {
    nlohmann::json json_data;
    
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    for (const auto& [name, value] : m_variables) {
        nlohmann::json var_json;
        var_json["type"] = static_cast<int>(value.type);
        var_json["unit"] = value.unit;
        var_json["should_save"] = value.should_save;
        var_json["scale_factor"] = value.scale_factor;
        
        switch (value.type) {
            case VariableType::DOUBLE:
                var_json["value"] = value.data.doubleValue;
                break;
            case VariableType::BOOL:
                var_json["value"] = value.data.boolValue;
                break;
            case VariableType::INT:
                var_json["value"] = value.data.intValue;
                break;
            case VariableType::STRING:
                var_json["value"] = value.stringData ? *value.stringData : "";
                break;
        }
        
        json_data[name] = var_json;
    }
    
    return json_data.dump(4);
}

LuaExecutionState EngineVariableSystem::get_script_execution_state(LuaScriptType script_type) const {
    auto& mutex_ref = const_cast<EngineVariableSystem*>(this)->get_execution_mutex_ref(script_type);
    std::lock_guard<std::mutex> lock(mutex_ref);
    const auto& context = const_cast<EngineVariableSystem*>(this)->get_execution_context_ref(script_type);
    return context.state;
}

bool EngineVariableSystem::should_break_at_line(int line_number, LuaScriptType script_type) const {
    std::lock_guard<std::mutex> lock(m_breakpoints_mutex);
    
    const auto& breakpoints = (script_type == LuaScriptType::MA_SCRIPT) ? m_MA_breakpoints : m_PR_breakpoints;
    
    // 查找指定行号的断点
    for (const auto& breakpoint : breakpoints) {
        if (breakpoint.line_number == line_number && breakpoint.enabled) {
            return true;
        }
    }
    
    return false;
}
