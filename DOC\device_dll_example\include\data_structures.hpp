#ifndef DATA_STRUCTURES_HPP
#define DATA_STRUCTURES_HPP

#include "common_types.hpp"
#include <string>
#include <vector>
#include <atomic>
#include <mutex>

namespace EthClient {

// 设备配置结构体
struct DeviceConfig {
    std::string instance_id;
    uint32_t device_index;
    std::string display_name;
    uint32_t memory_base_offset;
    bool enabled;
    std::string config_json;  // 保存配置信息的JSON字符串
    std::string state_json;   // 设备状态数据的JSON字符串
    
    // 通讯配置结构体
    struct CommunicationConfig {
        std::string interface_type;  // "TCP", "SERIAL", "USB", "SDK"
        uint32_t device_id;
        uint32_t timeout_ms;
        uint32_t retry_count;
        
        // TCP通讯参数
        std::string ip_address;
        uint16_t port;
        
        // 串口通讯参数
        std::string com_port;
        uint32_t baud_rate;
        uint32_t data_bits;
        std::string parity;  // "NONE", "ODD", "EVEN"
        uint32_t stop_bits;
        
        // USB通讯参数
        std::string vendor_id;
        std::string product_id;
        
        // SDK通讯参数
        std::string sdk_name;
        std::string sdk_version;
        std::string connection_string;
        
        // 命令队列配置
        uint32_t command_queue_size;
        
        CommunicationConfig()
            : interface_type("TCP")
            , device_id(1)
            , timeout_ms(1000)
            , retry_count(3)
            , port(502)
            , baud_rate(9600)
            , data_bits(8)
            , parity("NONE")
            , stop_bits(1)
            , command_queue_size(100) {}
    } communication;
    
    // 自定义参数
    struct CustomParameters {
        std::string serial_number;
        std::string calibration_date;
        double max_power;
        double max_current;
        double max_voltage;
        
        CustomParameters()
            : max_power(0.0)
            , max_current(0.0)
            , max_voltage(0.0) {}
    } custom_parameters;
    
    DeviceConfig() 
        : device_index(0)
        , memory_base_offset(0)
        , enabled(true) {}
};

struct E5000DataBlockV2 {
    // 只读数据 - Read-only measurement data
    double irms;        // 电流 - RMS Current
    double urms;        // 电压 - RMS Voltage  
    double prms;        // 功率 - RMS Power
    double ipeak;       // 电流 - Peak Current
    double upeak;       // 电压 - Peak Voltage
    double ppeak;       // 功率 - Peak Power
    double ivalley;     // 电流 - Valley Current
    double uvalley;     // 电压 - Valley Voltage
    double pvalley;     // 功率 - Valley Power
    
    // 运行状态 - Operating status
    uint64_t load_run_state_01;    // 负载运行状态01 - Load running state 01
    uint64_t load_run_state_02;    // 负载运行状态02 - Load running state 02
    uint64_t load_run_state_03;    // 负载运行状态03 - Load running state 03
    uint64_t load_run_state_04;    // 负载运行状态04 - Load running state 04
    uint64_t system_run_state_01;  // 系统运行状态01 - System running state 01
    uint64_t load_error_01;        // 负载故障01 - Load error 01
    uint64_t load_error_02;        // 负载故障02 - Load error 02
    uint64_t load_error_03;        // 负载故障03 - Load error 03
    uint64_t load_error_04;        // 负载故障04 - Load error 04
    uint64_t system_error_01;      // 系统故障01 - System error 01
    uint64_t run_stop_show;        // 设备运行状态 0 停机 1 运行 - Device running status 0=stop 1=run
    uint64_t short_show;           // 短路状态 0 未短路 1 短路状态 - Short circuit status 0=normal 1=short
    
    // CC模式下读写，只在指定模式下去读取 - CC mode read/write, only read in specified mode
    uint64_t cc_i_lv;              // CC电流档位 - CC current level
    uint64_t cc_v_lv;              // CC电流档位 - CC voltage level
    
    // CV模式下读写，只在指定模式下去读取 - CV mode read/write, only read in specified mode
    uint64_t cv_v_lv;              // CV电压档位 - CV voltage level
    uint64_t cv_i_lv;              // CV电流档位 - CV current level
    
    // CP模式下读写，只在指定模式下去读取 - CP mode read/write, only read in specified mode
    uint64_t cp_p_lv;              // CP功率档位 - CP power level
    uint64_t cp_v_lv;              // CP电压档位 - CP voltage level
    
    // CR模式下读写，只在指定模式下去读取 - CR mode read/write, only read in specified mode
    uint64_t cr_r_lv;              // CR电阻档位 - CR resistance level
    uint64_t cr_i_lv;              // CR电压档位 - CR current level
    
    // CCD模式下读写，只在指定模式下去读取 - CCD mode read/write, only read in specified mode
    uint64_t ccd_i_lv;             // CCD电流档位 - CCD current level
    uint64_t ccd_v_lv;             // CCD电流档位 - CCD voltage level
    
    // CRD模式下读写，只在指定模式下去读取 - CRD mode read/write, only read in specified mode
    uint64_t crd_r_lv;             // CRD电阻档位 - CRD resistance level
    uint64_t crd_i_lv;             // CRC电压档位 - CRD current level
    
    // SWD模式下读写，只在指定模式下去读取 - SWD mode read/write, only read in specified mode
    uint64_t swd_i_lv;             // SWD电流档位 - SWD current level
    uint64_t swd_v_lv;             // SWD电流档位 - SWD voltage level
    
    // SWP模式下读写，只在指定模式下去读取 - SWP mode read/write, only read in specified mode
    uint64_t swp_i_lv;             // SWP电流档位 - SWP current level
    uint64_t swp_v_lv;             // SWP电流档位 - SWP voltage level
    
    uint64_t run_mode;             // 负载运行模式 - Load operation mode
    int data_valid;                // 当前数据有效 0 无效 非0有效 - Current data valid 0=invalid non-zero=valid
    
    // Setpoint values for device control
    double voltage_setpoint;       // 电压设定值 - Voltage setpoint
    double current_setpoint;       // 电流设定值 - Current setpoint
    double power_setpoint;         // 功率设定值 - Power setpoint
    
    // Constructor
    E5000DataBlockV2() {
        reset();
    }
    
    // Reset all values to default
    void reset() {
        irms = urms = prms = 0.0;
        ipeak = upeak = ppeak = 0.0;
        ivalley = uvalley = pvalley = 0.0;
        
        load_run_state_01 = load_run_state_02 = load_run_state_03 = load_run_state_04 = 0;
        system_run_state_01 = 0;
        load_error_01 = load_error_02 = load_error_03 = load_error_04 = 0;
        system_error_01 = 0;
        run_stop_show = short_show = 0;
        
        cc_i_lv = cc_v_lv = 0;
        cv_v_lv = cv_i_lv = 0;
        cp_p_lv = cp_v_lv = 0;
        cr_r_lv = cr_i_lv = 0;
        ccd_i_lv = ccd_v_lv = 0;
        crd_r_lv = crd_i_lv = 0;
        swd_i_lv = swd_v_lv = 0;
        swp_i_lv = swp_v_lv = 0;
        
        run_mode = 0;
        data_valid = 0;
        
        // Initialize setpoint values
        voltage_setpoint = 0.0;
        current_setpoint = 0.0;
        power_setpoint = 0.0;
    }
};

} // namespace EthClient

#endif // DATA_STRUCTURES_HPP