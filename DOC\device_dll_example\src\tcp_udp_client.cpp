#include "tcp_udp_client.hpp"
#include "eth_client_interface.hpp"
#include "common_types.hpp"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <cstring>

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
#else
#include <sys/socket.h>
#include <sys/select.h>
#include <fcntl.h>
#include <unistd.h>
#endif

namespace EthClient {

// 静态成员初始化
std::atomic<bool> TcpUdpClient::s_winsock_initialized{false};
std::mutex TcpUdpClient::s_winsock_mutex;

// 四个固定命令的字节数组定义
const std::vector<uint8_t> TcpUdpClient::DCLOAD_RO_BYTES_A = {
    0x87, 0x01, 0x00, 0x00, 0x00, 0x06, 0x01, 0x03, 0x04, 0x4C, 0x00, 0x12
}; // 电压电流功率状态 1100开始18个

const std::vector<uint8_t> TcpUdpClient::DCLOAD_RO_BYTES_B = {
    0x87, 0x02, 0x00, 0x00, 0x00, 0x06, 0x01, 0x03, 0x04, 0x88, 0x00, 0x19
}; // 运行状态 1160 25个

const std::vector<uint8_t> TcpUdpClient::DCLOAD_RO_BYTES_C = {
    0x87, 0x03, 0x00, 0x00, 0x00, 0x06, 0x01, 0x03, 0x07, 0x6E, 0x00, 0x77
}; // 上下限判断 1900 119个

const std::vector<uint8_t> TcpUdpClient::DCLOAD_MODE_BYTES = {
    0x87, 0x11, 0x00, 0x00, 0x00, 0x06, 0x01, 0x03, 0x00, 0x00, 0x00, 0x02
}; // 读模式寄存器用的通用指令

TcpUdpClient::TcpUdpClient(const DeviceConfig& config)
     : m_config(config)
    , m_socket(INVALID_SOCKET_VALUE)
    , m_connected(false)
    , m_command_count(0)
    , m_error_count(0)
    , m_last_activity_timestamp(0)
    , m_transaction_id(1)
    , m_polling_active(false)
    , m_polling_switch(0)
    , m_last_command_c_time(0)
    , m_last_mode_command_time(0) {
    
    initialize_winsock();
}

TcpUdpClient::~TcpUdpClient() {
    disconnect();
    cleanup_winsock();
}

TcpUdpClient::TcpUdpClient(TcpUdpClient&& other) noexcept
     : m_config(std::move(other.m_config))
    , m_socket(other.m_socket)
    , m_connected(other.m_connected.load())
    , m_command_count(other.m_command_count.load())
    , m_error_count(other.m_error_count.load())
    , m_last_activity_timestamp(other.m_last_activity_timestamp.load())
    , m_transaction_id(other.m_transaction_id.load())
    , m_last_error(std::move(other.m_last_error)) {
    
    other.m_socket = INVALID_SOCKET_VALUE;
    other.m_connected = false;
}

TcpUdpClient& TcpUdpClient::operator=(TcpUdpClient&& other) noexcept {
    if (this != &other) {
        disconnect();
        
        // IP地址存储在m_config中，无需单独复制
        // 端口存储在m_config中，无需单独复制
        m_timeout_ms = other.m_timeout_ms.load();
        m_socket = other.m_socket;
        m_connected = other.m_connected.load();
        m_command_count = other.m_command_count.load();
        m_error_count = other.m_error_count.load();
        m_last_activity_timestamp = other.m_last_activity_timestamp.load();
        m_transaction_id = other.m_transaction_id.load();
        m_last_error = std::move(other.m_last_error);
        
        other.m_socket = INVALID_SOCKET_VALUE;
        other.m_connected = false;
    }
    return *this;
}

ATE_EC TcpUdpClient::connect() {
    std::lock_guard<std::mutex> lock(m_socket_mutex);
    
    if (m_connected) {
        return ATE_SUCCESS;
    }
    
    // 创建套接字
    m_socket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (m_socket == INVALID_SOCKET_VALUE) {
        m_set_last_error("Failed to create socket");
        m_error_count++;
        return ATE_ERROR_CONNECTION_FAILED;
    }
    
    // 设置套接字超时
    if (set_socket_timeout(m_socket, m_timeout_ms) != ATE_SUCCESS) {
        close_socket();
        return ATE_ERROR_CONNECTION_FAILED;
    }
    
    // 设置socket为非阻塞模式以便进行超时控制
#ifdef _WIN32
    u_long mode = 1;
    if (ioctlsocket(m_socket, FIONBIO, &mode) != 0) {
        m_set_last_error("Failed to set socket to non-blocking mode");
        close_socket();
        return ATE_ERROR_CONNECTION_FAILED;
    }
#else
    int flags = fcntl(m_socket, F_GETFL, 0);
    if (flags == -1 || fcntl(m_socket, F_SETFL, flags | O_NONBLOCK) == -1) {
        m_set_last_error("Failed to set socket to non-blocking mode");
        close_socket();
        return ATE_ERROR_CONNECTION_FAILED;
    }
#endif
    
    // 设置服务器地址
    sockaddr_in server_addr{};
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(m_config.communication.port);
    
#ifdef _WIN32
    if (inet_pton(AF_INET, m_config.communication.ip_address.c_str(), &server_addr.sin_addr) != 1) {
#else
    if (inet_aton(m_config.communication.ip_address.c_str(), &server_addr.sin_addr) == 0) {
#endif
        m_set_last_error("Invalid IP address: " + m_config.communication.ip_address);
        close_socket();
        m_error_count++;
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    // 连接到服务器
    if (::connect(m_socket, reinterpret_cast<sockaddr*>(&server_addr), sizeof(server_addr)) != 0) {
        m_set_last_error("Failed to connect to " + m_config.communication.ip_address + ":" + std::to_string(m_config.communication.port));
        close_socket();
        m_error_count++;
        return ATE_ERROR_CONNECTION_FAILED;
    }
    
    m_connected = true;
    update_last_activity();
    return ATE_SUCCESS;
}

void TcpUdpClient::disconnect() {
    std::lock_guard<std::mutex> lock(m_socket_mutex);
    close_socket();
    m_connected = false;
}

bool TcpUdpClient::is_connected() const {
    return m_connected.load();
}

ATE_EC TcpUdpClient::send_receive_modbus_command(const std::vector<uint8_t>& command, std::vector<uint8_t>& response) {
    if (!m_connected) {
        m_set_last_error("Not connected to device");
        m_error_count++;
        return ATE_ERROR_DEVICE_OFFLINE;
    }
    
    // 构建Modbus TCP帧
    std::vector<uint8_t> tcp_frame = build_modbus_tcp_frame(command);
    
    // 发送命令
    ATE_EC result = send_data(tcp_frame);
    if (result != ATE_SUCCESS) {
        m_error_count++;
        return result;
    }
    
    // 接收响应
    result = receive_data(response);
    if (result != ATE_SUCCESS) {
        m_error_count++;
        return result;
    }
    
    // 解析Modbus TCP响应
    result = parse_modbus_tcp_response(response);
    if (result != ATE_SUCCESS) {
        m_error_count++;
        return result;
    }
    
    m_command_count++;
    update_last_activity();
    return ATE_SUCCESS;
}

void TcpUdpClient::set_timeout(uint32_t timeout_ms) {
    m_timeout_ms = timeout_ms;
}

uint32_t TcpUdpClient::get_timeout() const {
    return m_timeout_ms.load();
}

std::string TcpUdpClient::get_last_error() const {
    std::lock_guard<std::mutex> lock(m_error_mutex);
    return m_last_error;
}

uint64_t TcpUdpClient::get_last_activity_timestamp() const {
    return m_last_activity_timestamp.load();
}

uint32_t TcpUdpClient::get_command_count() const {
    return m_command_count.load();
}

uint32_t TcpUdpClient::get_error_count() const {
    return m_error_count.load();
}

void TcpUdpClient::reset_statistics() {
    m_command_count = 0;
    m_error_count = 0;
}

// Private methods
ATE_EC TcpUdpClient::initialize_winsock() {
#ifdef _WIN32
    std::lock_guard<std::mutex> lock(s_winsock_mutex);
    if (!s_winsock_initialized) {
        WSADATA wsaData;
        if (WSAStartup(MAKEWORD(2, 2), &wsaData) == 0) {
            s_winsock_initialized = true;
            return ATE_SUCCESS;
        } else {
            return ATE_ERROR_CONNECTION_FAILED;
        }
    }
    return ATE_SUCCESS;
#else
    return ATE_SUCCESS;
#endif
}

void TcpUdpClient::cleanup_winsock() {
#ifdef _WIN32
    std::lock_guard<std::mutex> lock(s_winsock_mutex);
    if (s_winsock_initialized) {
        WSACleanup();
        s_winsock_initialized = false;
    }
#endif
}

void TcpUdpClient::close_socket() {
    if (m_socket != INVALID_SOCKET_VALUE) {
#ifdef _WIN32
        closesocket(m_socket);
#else
        close(m_socket);
#endif
        m_socket = INVALID_SOCKET_VALUE;
    }
}

ATE_EC TcpUdpClient::set_socket_timeout(socket_t socket, uint32_t timeout_ms) {
#ifdef _WIN32
    DWORD timeout = timeout_ms;
    if (setsockopt(socket, SOL_SOCKET, SO_RCVTIMEO, reinterpret_cast<const char*>(&timeout), sizeof(timeout)) != 0 ||
        setsockopt(socket, SOL_SOCKET, SO_SNDTIMEO, reinterpret_cast<const char*>(&timeout), sizeof(timeout)) != 0) {
        m_set_last_error("Failed to set socket timeout");
        return ATE_ERROR_CONNECTION_FAILED;
    }
#else
    struct timeval timeout;
    timeout.tv_sec = timeout_ms / 1000;
    timeout.tv_usec = (timeout_ms % 1000) * 1000;
    if (setsockopt(socket, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) != 0 ||
        setsockopt(socket, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout)) != 0) {
        m_set_last_error("Failed to set socket timeout");
        return ATE_ERROR_CONNECTION_FAILED;
    }
#endif
    return ATE_SUCCESS;
}

std::vector<uint8_t> TcpUdpClient::build_modbus_tcp_frame(const std::vector<uint8_t>& modbus_pdu) {
    std::vector<uint8_t> frame;
    frame.reserve(6 + modbus_pdu.size());
    
    // Transaction ID (2 bytes)
    uint16_t trans_id = m_transaction_id++;
    frame.push_back(static_cast<uint8_t>(trans_id >> 8));
    frame.push_back(static_cast<uint8_t>(trans_id & 0xFF));
    
    // Protocol ID (2 bytes) - always 0 for Modbus
    frame.push_back(0x00);
    frame.push_back(0x00);
    
    // Length (2 bytes) - PDU length
    uint16_t length = static_cast<uint16_t>(modbus_pdu.size());
    frame.push_back(static_cast<uint8_t>(length >> 8));
    frame.push_back(static_cast<uint8_t>(length & 0xFF));
    
    // Append PDU
    frame.insert(frame.end(), modbus_pdu.begin(), modbus_pdu.end());
    
    return frame;
}

ATE_EC TcpUdpClient::send_data(const std::vector<uint8_t>& data) {
    size_t total_sent = 0;
    while (total_sent < data.size()) {
        int sent = send(m_socket, reinterpret_cast<const char*>(data.data() + total_sent), 
                       static_cast<int>(data.size() - total_sent), 0);
        if (sent <= 0) {
            m_set_last_error("Failed to send data");
            return ATE_ERROR_COMMUNICATION_TIMEOUT;
        }
        total_sent += sent;
    }
    return ATE_SUCCESS;
}

ATE_EC TcpUdpClient::receive_data(std::vector<uint8_t>& data) {
    data.clear();
    
    // 获取超时时间，优先使用配置中的timeout_ms
    uint32_t timeout_ms = m_config.communication.timeout_ms;
    if (timeout_ms == 0) {
        timeout_ms = 3000; // 默认3秒超时
    }
    
    auto start_time = std::chrono::steady_clock::now();
    uint32_t elapsed = 0;
    
    while (elapsed < timeout_ms) {
        auto current_time = std::chrono::steady_clock::now();
        elapsed = static_cast<uint32_t>(std::chrono::duration_cast<std::chrono::milliseconds>(current_time - start_time).count());
        
        if (elapsed >= timeout_ms) {
            m_set_last_error("Timeout while receiving PDU");
            return ATE_ERROR_COMMUNICATION_TIMEOUT;
        }
        
        // 使用select检查数据是否可读
        fd_set read_fds;
        FD_ZERO(&read_fds);
        FD_SET(m_socket, &read_fds);
        
        struct timeval tv;
        tv.tv_sec = 0;
        tv.tv_usec = 10000; // 10ms超时
        
        int select_result = select(static_cast<int>(m_socket + 1), &read_fds, nullptr, nullptr, &tv);
        if (select_result < 0) {
            m_set_last_error("Select error while receiving PDU");
            return ATE_ERROR_COMMUNICATION_TIMEOUT;
        } else if (select_result == 0) {
            m_set_last_error("Timeout while receiving PDU");
            return ATE_ERROR_COMMUNICATION_TIMEOUT;
        }
        
        if (FD_ISSET(m_socket, &read_fds)) {
            int result = recv(m_socket, reinterpret_cast<char*>(data.data()), 0, 0);
            if (result > 0) {
                break;
            } else if (result == 0) {
                m_set_last_error("Connection closed while receiving PDU");
                return ATE_ERROR_COMMUNICATION_TIMEOUT;
            } else {
                m_set_last_error("Failed to receive PDU: " + std::to_string(result));
                return ATE_ERROR_COMMUNICATION_TIMEOUT;
            }
        }
    }
    
    return ATE_SUCCESS;
}

ATE_EC TcpUdpClient::parse_modbus_tcp_response(std::vector<uint8_t>& response) {
    if (response.size() < 2) {
        m_set_last_error("Response too short");
        return ATE_ERROR_PROTOCOL_ERROR;
    }
    
    // Check for Modbus exception
    if (response[1] & 0x80) {
        uint8_t exception_code = response.size() > 2 ? response[2] : 0;
        m_set_last_error("Modbus exception: " + std::to_string(exception_code));
        return ATE_ERROR_DEVICE_FAULT;
    }
    
    // Remove unit ID and function code, keep only data
    if (response.size() > 2) {
        response.erase(response.begin(), response.begin() + 2);
    }
    
    return ATE_SUCCESS;
}

double TcpUdpClient::parse_double_from_registers(const std::vector<uint8_t>& data, size_t offset) {
    if (offset + 4 > data.size()) {
        return 0.0;
    }
    
    uint32_t raw_value = (static_cast<uint32_t>(data[offset]) << 24) |
                        (static_cast<uint32_t>(data[offset + 1]) << 16) |
                        (static_cast<uint32_t>(data[offset + 2]) << 8) |
                        static_cast<uint32_t>(data[offset + 3]);
    
    return static_cast<double>(raw_value) / 1000.0; // Convert from milli-units
}

void TcpUdpClient::m_set_last_error(const std::string& error) {
    std::lock_guard<std::mutex> lock(m_error_mutex);
    m_last_error = error;
}

void TcpUdpClient::update_last_activity() {
    auto now = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
    m_last_activity_timestamp = static_cast<uint64_t>(now);
}

// 注意：send_receive_modbus_command函数已在前面定义，此处删除重复定义

// 四个固定命令的实现
// 这些函数通过send_receive_modbus_command调用，已经包含了查询锁机制
ATE_EC TcpUdpClient::execute_fixed_command_a(E5000DataBlockV2& data) {
    std::vector<uint8_t> response(MODBUS_RESPONSE_BUFFER_SIZE);
    ATE_EC result = send_receive_modbus_command(DCLOAD_RO_BYTES_A, response);
    if (result == ATE_SUCCESS) {
        parse_command_a_response(response, data);
    }
    return result;
}

ATE_EC TcpUdpClient::execute_fixed_command_b(E5000DataBlockV2& data) {
    std::vector<uint8_t> response(MODBUS_RESPONSE_BUFFER_SIZE);
    ATE_EC result = send_receive_modbus_command(DCLOAD_RO_BYTES_B, response);
    if (result == ATE_SUCCESS) {
        parse_command_b_response(response, data);
    }
    return result;
}

ATE_EC TcpUdpClient::execute_fixed_command_c(E5000DataBlockV2& data) {
    std::vector<uint8_t> response(MODBUS_RESPONSE_BUFFER_SIZE);
    ATE_EC result = send_receive_modbus_command(DCLOAD_RO_BYTES_C, response);
    if (result == ATE_SUCCESS) {
        parse_command_c_response(response, data);
    }
    return result;
}

ATE_EC TcpUdpClient::execute_mode_command(E5000DataBlockV2& data) {
    std::vector<uint8_t> response(MODBUS_RESPONSE_BUFFER_SIZE);
    ATE_EC result = send_receive_modbus_command(DCLOAD_MODE_BYTES, response);
    if (result == ATE_SUCCESS) {
        parse_mode_command_response(response, data);
    }
    return result;
}

// 轮询控制
void TcpUdpClient::start_polling() {
    m_polling_active = true;
    m_polling_switch = 0;
    auto now = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
    m_last_command_c_time = now;
    m_last_mode_command_time = now;
}

void TcpUdpClient::stop_polling() {
    m_polling_active = false;
}

bool TcpUdpClient::is_polling() const {
    return m_polling_active.load();
}

// 命令队列管理实现
ATE_EC TcpUdpClient::add_write_command(const std::vector<uint8_t>& command, const std::string& description) {
    std::lock_guard<std::mutex> lock(m_command_queue_mutex);
    
    // 使用配置中的重试次数
    uint32_t retry_count = m_config.communication.retry_count;
    
    if(m_write_command_queue.size() >= m_config.communication.command_queue_size)
    {
        return ATE_ERROR_DEVICE_BUSY;
    }
    m_write_command_queue.emplace(command, retry_count, description);
    m_command_queue_cv.notify_one();
    return ATE_SUCCESS;
}

void TcpUdpClient::process_pending_commands() {
    std::unique_lock<std::mutex> lock(m_command_queue_mutex);
    
    while (!m_write_command_queue.empty()) {
        WriteCommand cmd = m_write_command_queue.front();
        m_write_command_queue.pop();
        lock.unlock();
        
        // 发送命令
        std::vector<uint8_t> response(MODBUS_RESPONSE_BUFFER_SIZE);
        ATE_EC result = send_data(cmd.command_data);
        
        if (result != ATE_SUCCESS && cmd.retry_count > 0) {
            // 重试：减少重试次数并重新加入队列
            cmd.retry_count--;
            lock.lock();
            m_write_command_queue.push(cmd);
        } else if (result != ATE_SUCCESS) {
            // 重试次数用完，记录错误
            m_set_last_error("Write command failed after all retries: " + cmd.description);
            m_error_count++;
            lock.lock();
        } else {
            // 成功执行，重新获取锁继续循环
            lock.lock();
        }
    }
}

// 固定命令数据解析实现
void TcpUdpClient::parse_command_a_response(const std::vector<uint8_t>& response, E5000DataBlockV2& data) {
    if (response.size() < 42) return;  // 至少需要42字节数据
    
    // 验证响应头
    if (response.size() < 6 || response[0] != 0x87 || response[1] != 0x01) {
        return;
    }
    
    // 解析电压电流功率数据 (参考Pascal DataDeal_A)
    uint32_t temp_value;
    
    // Irms - 电流RMS值
    temp_value = (static_cast<uint32_t>(response[9]) << 24) |
                 (static_cast<uint32_t>(response[10]) << 16) |
                 (static_cast<uint32_t>(response[11]) << 8) |
                 static_cast<uint32_t>(response[12]);
    data.irms = static_cast<double>(temp_value) / 10000.0;
    
    // Urms - 电压RMS值
    temp_value = (static_cast<uint32_t>(response[13]) << 24) |
                 (static_cast<uint32_t>(response[14]) << 16) |
                 (static_cast<uint32_t>(response[15]) << 8) |
                 static_cast<uint32_t>(response[16]);
    data.urms = static_cast<double>(temp_value) / 10000.0;
    
    // Prms - 功率RMS值
    temp_value = (static_cast<uint32_t>(response[17]) << 24) |
                 (static_cast<uint32_t>(response[18]) << 16) |
                 (static_cast<uint32_t>(response[19]) << 8) |
                 static_cast<uint32_t>(response[20]);
    data.prms = static_cast<double>(temp_value) / 1000.0;
    
    // Ipeak - 电流峰值
    temp_value = (static_cast<uint32_t>(response[21]) << 24) |
                 (static_cast<uint32_t>(response[22]) << 16) |
                 (static_cast<uint32_t>(response[23]) << 8) |
                 static_cast<uint32_t>(response[24]);
    data.ipeak = static_cast<double>(temp_value) / 10000.0;
    
    // Upeak - 电压峰值
    temp_value = (static_cast<uint32_t>(response[25]) << 24) |
                 (static_cast<uint32_t>(response[26]) << 16) |
                 (static_cast<uint32_t>(response[27]) << 8) |
                 static_cast<uint32_t>(response[28]);
    data.upeak = static_cast<double>(temp_value) / 10000.0;
    
    // Ppeak - 功率峰值
    temp_value = (static_cast<uint32_t>(response[29]) << 24) |
                 (static_cast<uint32_t>(response[30]) << 16) |
                 (static_cast<uint32_t>(response[31]) << 8) |
                 static_cast<uint32_t>(response[32]);
    data.ppeak = static_cast<double>(temp_value) / 1000.0;
    
    // Ivalley - 电流谷值
    temp_value = (static_cast<uint32_t>(response[33]) << 24) |
                 (static_cast<uint32_t>(response[34]) << 16) |
                 (static_cast<uint32_t>(response[35]) << 8) |
                 static_cast<uint32_t>(response[36]);
    data.ivalley = static_cast<double>(temp_value) / 10000.0;
    
    // Uvalley - 电压谷值
    temp_value = (static_cast<uint32_t>(response[37]) << 24) |
                 (static_cast<uint32_t>(response[38]) << 16) |
                 (static_cast<uint32_t>(response[39]) << 8) |
                 static_cast<uint32_t>(response[40]);
    data.uvalley = static_cast<double>(temp_value) / 10000.0;
    
    // Pvalley - 功率谷值
    temp_value = (static_cast<uint32_t>(response[41]) << 24) |
                 (static_cast<uint32_t>(response[42]) << 16) |
                 (static_cast<uint32_t>(response[43]) << 8) |
                 static_cast<uint32_t>(response[44]);
    data.pvalley = static_cast<double>(temp_value) / 1000.0;
    
    // 标记数据有效性
    data.data_valid++;
}

void TcpUdpClient::parse_command_b_response(const std::vector<uint8_t>& response, E5000DataBlockV2& data) {
    if (response.size() < 59) return;  // 至少需要59字节数据
    
    // 验证响应头
    if (response.size() < 6 || response[0] != 0x87 || response[1] != 0x02) {
        return;
    }
    
    // 解析运行状态数据 (参考Pascal DataDeal_B)
    uint16_t temp_value;
    
    // 解析各种状态值
    temp_value = (static_cast<uint16_t>(response[10]) << 8) | response[9];
    data.load_run_state_01 = temp_value;
    
    temp_value = (static_cast<uint16_t>(response[12]) << 8) | response[11];
    data.load_run_state_02 = temp_value;
    
    temp_value = (static_cast<uint16_t>(response[14]) << 8) | response[13];
    data.load_run_state_03 = temp_value;
    
    temp_value = (static_cast<uint16_t>(response[16]) << 8) | response[15];
    data.load_run_state_04 = temp_value;
    
    temp_value = (static_cast<uint16_t>(response[18]) << 8) | response[17];
    data.system_run_state_01 = temp_value;
    
    // 解析运行停止状态和短路状态 (根据Pascal代码中的位置)
    temp_value = (static_cast<uint16_t>(response[20]) << 8) | response[19];
    data.run_stop_show = temp_value;
    
    temp_value = (static_cast<uint16_t>(response[22]) << 8) | response[21];
    data.short_show = temp_value;
    
    // 解析错误状态
    temp_value = (static_cast<uint16_t>(response[52]) << 8) | response[51];
    data.load_error_01 = temp_value;
    
    temp_value = (static_cast<uint16_t>(response[54]) << 8) | response[53];
    data.load_error_02 = temp_value;
    
    temp_value = (static_cast<uint16_t>(response[56]) << 8) | response[55];
    data.load_error_03 = temp_value;
    
    temp_value = (static_cast<uint16_t>(response[58]) << 8) | response[57];
    data.load_error_04 = temp_value;
    
    temp_value = (static_cast<uint16_t>(response[60]) << 8) | response[59];
    data.system_error_01 = temp_value;
    
    data.data_valid++;
}

void TcpUdpClient::parse_command_c_response(const std::vector<uint8_t>& response, E5000DataBlockV2& data) {
    if (response.size() < 247) return;  // 至少需要247字节数据
    
    // 验证响应头
    if (response.size() < 6 || response[0] != 0x87 || response[1] != 0x03) {
        return;
    }
    
    // 解析上下限判断数据和模式参数 (参考Pascal DataDeal_C)
    uint16_t temp_value;
    
    // 解析各种模式下的档位设置 (根据E5000DataBlockV2结构体定义)
    // CC模式参数 (假设在特定偏移位置)
    temp_value = (static_cast<uint16_t>(response[10]) << 8) | response[9];
    data.cc_i_lv = temp_value;
    temp_value = (static_cast<uint16_t>(response[12]) << 8) | response[11];
    data.cc_v_lv = temp_value;
    
    // CV模式参数
    temp_value = (static_cast<uint16_t>(response[14]) << 8) | response[13];
    data.cv_v_lv = temp_value;
    temp_value = (static_cast<uint16_t>(response[16]) << 8) | response[15];
    data.cv_i_lv = temp_value;
    
    // CP模式参数
    temp_value = (static_cast<uint16_t>(response[18]) << 8) | response[17];
    data.cp_p_lv = temp_value;
    temp_value = (static_cast<uint16_t>(response[20]) << 8) | response[19];
    data.cp_v_lv = temp_value;
    
    // CR模式参数
    temp_value = (static_cast<uint16_t>(response[22]) << 8) | response[21];
    data.cr_r_lv = temp_value;
    temp_value = (static_cast<uint16_t>(response[24]) << 8) | response[23];
    data.cr_i_lv = temp_value;
    
    // CCD模式参数
    temp_value = (static_cast<uint16_t>(response[26]) << 8) | response[25];
    data.ccd_i_lv = temp_value;
    temp_value = (static_cast<uint16_t>(response[28]) << 8) | response[27];
    data.ccd_v_lv = temp_value;
    
    // CRD模式参数
    temp_value = (static_cast<uint16_t>(response[30]) << 8) | response[29];
    data.crd_r_lv = temp_value;
    temp_value = (static_cast<uint16_t>(response[32]) << 8) | response[31];
    data.crd_i_lv = temp_value;
    
    // SWD模式参数
    temp_value = (static_cast<uint16_t>(response[34]) << 8) | response[33];
    data.swd_i_lv = temp_value;
    temp_value = (static_cast<uint16_t>(response[36]) << 8) | response[35];
    data.swd_v_lv = temp_value;
    
    // SWP模式参数
    temp_value = (static_cast<uint16_t>(response[38]) << 8) | response[37];
    data.swp_i_lv = temp_value;
    temp_value = (static_cast<uint16_t>(response[40]) << 8) | response[39];
    data.swp_v_lv = temp_value;
    
    // 运行模式 (最后一个参数)
    temp_value = (static_cast<uint16_t>(response[246]) << 8) | response[245];
    data.run_mode = temp_value;
    
    data.data_valid++;
}

void TcpUdpClient::parse_mode_command_response(const std::vector<uint8_t>& response, E5000DataBlockV2& data) {
    if (response.size() < 11) return;  // 至少需要11字节数据
    
    // 验证响应头
    if (response.size() < 6 || response[0] != 0x87 || response[1] != 0x11) {
        return;
    }
    
    // 解析模式寄存器数据
    uint16_t temp_value = (static_cast<uint16_t>(response[10]) << 8) | response[9];
    data.run_mode = temp_value;
    
    data.data_valid++;
}

// 透传通信接口实现 - 类似m_IoTran函数
ATE_EC TcpUdpClient::execute_raw_command(const uint8_t* command_data, size_t command_length, 
                                         uint32_t timeout_ms, uint8_t* response_data, 
                                         size_t response_buffer_size, size_t& actual_response_length) {
    // 参数验证
    if (!command_data || command_length == 0 || !response_data || response_buffer_size == 0) {
        m_set_last_error("Invalid parameters for raw command execution");
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    // 检查连接状态
    if (!m_connected || m_socket == INVALID_SOCKET_VALUE) {
        m_set_last_error("Device not connected");
        return ATE_ERROR_DEVICE_NOT_FOUND;
    }
    
    // 获取查询互斥锁，类似SelfExeResMutex
    std::lock_guard<std::mutex> lock(m_query_mutex);
    
    actual_response_length = 0;
    
    try {
        // 记录开始时间
        auto start_time = std::chrono::steady_clock::now();
        
        // 发送原始命令数据
        int total_sent = 0;
        while (total_sent < static_cast<int>(command_length)) {
            auto current_time = std::chrono::steady_clock::now();
            auto elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - start_time).count();
            
            if (elapsed_ms >= timeout_ms) {
                m_set_last_error("Timeout during command transmission");
                return ATE_ERROR_COMMUNICATION_TIMEOUT;
            }
            
            int sent = send(m_socket, reinterpret_cast<const char*>(command_data + total_sent), 
                           static_cast<int>(command_length - total_sent), 0);
            if (sent <= 0) {
                m_set_last_error("Failed to send raw command data");
                return ATE_ERROR_COMMUNICATION_TIMEOUT;
            }
            total_sent += sent;
        }
        
        // 清空响应缓冲区
        std::memset(response_data, 0, response_buffer_size);
        
        // 等待并接收响应数据
        while (true) {
            auto current_time = std::chrono::steady_clock::now();
            auto elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - start_time).count();
            
            if (elapsed_ms >= timeout_ms) {
                m_set_last_error("Timeout waiting for response");
                return ATE_ERROR_COMMUNICATION_TIMEOUT;
            }
            
            // 使用select检查数据是否可读
            fd_set read_fds;
            FD_ZERO(&read_fds);
            FD_SET(m_socket, &read_fds);
            
            struct timeval tv;
            tv.tv_sec = 0;
            tv.tv_usec = 10000; // 10ms
            
            int select_result = select(static_cast<int>(m_socket + 1), &read_fds, nullptr, nullptr, &tv);
            if (select_result < 0) {
                m_set_last_error("Select error while waiting for response");
                return ATE_ERROR_COMMUNICATION_TIMEOUT;
            } else if (select_result > 0 && FD_ISSET(m_socket, &read_fds)) {
                // 有数据可读
                int received = recv(m_socket, reinterpret_cast<char*>(response_data), static_cast<int>(response_buffer_size), 0);
                if (received > 0) {
                    actual_response_length = static_cast<size_t>(received);
                    break; // 成功接收到数据
                } else if (received == 0) {
                    m_set_last_error("Connection closed while receiving response");
                    return ATE_ERROR_COMMUNICATION_TIMEOUT;
                } else {
                    m_set_last_error("Failed to receive response data");
                    return ATE_ERROR_COMMUNICATION_TIMEOUT;
                }
            }
            // 如果select_result == 0，继续循环等待
        }
        
        // 更新统计信息
        m_command_count++;
        update_last_activity();
        
        return ATE_SUCCESS;
        
    } catch (const std::exception& e) {
        m_set_last_error("Exception in raw command execution: " + std::string(e.what()));
        return ATE_ERROR_UNKNOWN;
    }
}

ATE_EC TcpUdpClient::write_setpoint(uint16_t register_address, double value, const std::string& command_description) {
    // 避免未使用参数警告
    (void)register_address;
    (void)value;
    
    // 检查连接状态
    if (!m_connected || m_socket == INVALID_SOCKET_VALUE) {
        m_set_last_error("Device not connected");
        return ATE_ERROR_DEVICE_NOT_FOUND;
    }
    
    try {
        // 构建Modbus写入命令
        std::vector<uint8_t> command_data;
        
        // 这里应该根据实际的Modbus协议构建写入命令
        // 暂时返回成功，具体实现需要根据设备协议文档
        
        // 添加到写入命令队列
        ATE_EC result = add_write_command(command_data, command_description);
        
        if (result == ATE_SUCCESS) {
            m_command_count++;
            update_last_activity();
        } else {
            m_error_count++;
        }
        
        return result;
        
    } catch (const std::exception& e) {
        m_set_last_error("Exception in write_setpoint: " + std::string(e.what()));
        m_error_count++;
        return ATE_ERROR_COMMUNICATION_TIMEOUT;
    }
}

void TcpUdpClient::process_received_data() {
    if (!m_connected || m_socket == INVALID_SOCKET_VALUE) {
        return;
    }

    // 临时接收缓冲区，大小为2K
    std::vector<uint8_t> temp_buffer(RECEIVE_BUFFER_CHUNK_SIZE);
    
    // 使用select检查是否有数据可读
    fd_set read_fds;
    FD_ZERO(&read_fds);
    FD_SET(m_socket, &read_fds);
    
    struct timeval timeout;
    timeout.tv_sec = 0;
    timeout.tv_usec = 0;  // 非阻塞检查
    
    int select_result = select(static_cast<int>(m_socket + 1), &read_fds, nullptr, nullptr, &timeout);
    
    if (select_result > 0 && FD_ISSET(m_socket, &read_fds)) {
        // 有数据可读，接收数据
        int bytes_received = recv(m_socket, reinterpret_cast<char*>(temp_buffer.data()), static_cast<int>(temp_buffer.size()), 0);
        
        if (bytes_received > 0) {
            // 将接收到的数据添加到容器中
            std::lock_guard<std::mutex> lock(m_receive_buffer_mutex);
            
            // 检查容器大小限制
            if (m_receive_buffer_container.size() >= MAX_RECEIVE_BUFFER_CHUNKS) {
                // 如果超过最大块数，移除最旧的数据块
                m_receive_buffer_container.erase(m_receive_buffer_container.begin());
            }
            
            // 创建新的数据块，只包含实际接收到的数据
            std::vector<uint8_t> new_chunk(temp_buffer.begin(), temp_buffer.begin() + bytes_received);
            m_receive_buffer_container.push_back(std::move(new_chunk));
            
            // 更新活动时间戳
            m_last_activity_timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now().time_since_epoch()).count();
                
        } else if (bytes_received == 0) {
            // 连接已关闭
            m_connected = false;
            m_set_last_error("Connection closed by peer");
        } else {
            // 接收错误
#ifdef _WIN32
            int error_code = WSAGetLastError();
            if (error_code != WSAEWOULDBLOCK) {
                m_set_last_error("Receive error: " + std::to_string(error_code));
                m_error_count++;
            }
#else
            if (errno != EAGAIN && errno != EWOULDBLOCK) {
                m_set_last_error("Receive error: " + std::string(strerror(errno)));
                m_error_count++;
            }
#endif
        }
    } else if (select_result < 0) {
        // select错误
#ifdef _WIN32
        int error_code = WSAGetLastError();
        m_set_last_error("Select error: " + std::to_string(error_code));
#else
        m_set_last_error("Select error: " + std::string(strerror(errno)));
#endif
        m_error_count++;
    }
    // select_result == 0 表示没有数据可读，这是正常情况
}

std::vector<uint8_t> TcpUdpClient::get_received_data() const {
    std::lock_guard<std::mutex> lock(m_receive_buffer_mutex);
    
    // 合并容器内所有数据块
    std::vector<uint8_t> combined_data;
    for (const auto& chunk : m_receive_buffer_container) {
        combined_data.insert(combined_data.end(), chunk.begin(), chunk.end());
    }
    
    return combined_data;  // 返回合并后的数据副本
}

size_t TcpUdpClient::get_received_data_size() const {
    std::lock_guard<std::mutex> lock(m_receive_buffer_mutex);
    
    // 计算所有数据块的总大小
    size_t total_size = 0;
    for (const auto& chunk : m_receive_buffer_container) {
        total_size += chunk.size();
    }
    
    return total_size;
}

void TcpUdpClient::clear_received_data() {
    std::lock_guard<std::mutex> lock(m_receive_buffer_mutex);
    m_receive_buffer_container.clear();
}

bool TcpUdpClient::has_received_data() const {
    std::lock_guard<std::mutex> lock(m_receive_buffer_mutex);
    return !m_receive_buffer_container.empty();
}

} // namespace EthClient