#ifndef COMMON_TYPES_HPP
#define COMMON_TYPES_HPP

#include <cstdint>

// Windows DLL导出/导入宏定义
#ifdef _WIN32
    #ifdef DRIVER_MANAGER_DLL_EXPORTS
        #define EXP_API __declspec(dllexport)
    #else
        #define EXP_API __declspec(dllimport)
    #endif
#else
    #define EXP_API __attribute__((visibility("default")))
#endif

// ATE通用错误代码枚举 - 适用于所有外设DLL
typedef enum {
    // 成功状态
    ATE_SUCCESS = 0,                      // 操作成功
    
    // 参数相关错误 (1-19)
    ATE_ERROR_INVALID_PARAMETER = 1,      // 无效参数
    ATE_ERROR_NULL_POINTER = 2,           // 空指针
    ATE_ERROR_BUFFER_TOO_SMALL = 3,       // 缓冲区太小
    ATE_ERROR_INVALID_RANGE = 4,          // 参数超出有效范围
    ATE_ERROR_INVALID_FORMAT = 5,         // 格式错误
    
    // 初始化相关错误 (20-39)
    ATE_ERROR_NOT_INITIALIZED = 20,       // 未初始化
    ATE_ERROR_ALREADY_INITIALIZED = 21,   // 已经初始化
    ATE_ERROR_INITIALIZATION_FAILED = 22, // 初始化失败
    ATE_ERROR_CONFIG_LOAD_FAILED = 23,    // 配置加载失败
    ATE_ERROR_LIBRARY_LOAD_FAILED = 24,   // 库加载失败
    
    // 设备相关错误 (40-79)
    ATE_ERROR_DEVICE_NOT_FOUND = 40,      // 设备未找到
    ATE_ERROR_DEVICE_ALREADY_EXISTS = 41, // 设备已存在
    ATE_ERROR_DEVICE_BUSY = 42,           // 设备忙
    ATE_ERROR_DEVICE_NOT_READY = 43,      // 设备未就绪
    ATE_ERROR_DEVICE_OFFLINE = 44,        // 设备离线
    ATE_ERROR_DEVICE_FAULT = 45,          // 设备故障
    ATE_ERROR_DEVICE_OVERLOAD = 46,       // 设备过载
    ATE_ERROR_DEVICE_PROTECTION = 47,     // 设备保护
    ATE_ERROR_DEVICE_CALIBRATION = 48,    // 设备校准错误
    ATE_ERROR_DEVICE_FIRMWARE = 49,       // 固件错误
    ATE_ERROR_ALREADY_EXISTS = 50,        // 已存在
    ATE_ERROR_CREATION_FAILED = 51,       // 创建失败
    
    // 通信相关错误 (80-119)
    ATE_ERROR_CONNECTION_FAILED = 80,     // 连接失败
    ATE_ERROR_CONNECTION_LOST = 81,       // 连接丢失
    ATE_ERROR_CONNECTION_TIMEOUT = 82,    // 连接超时
    ATE_ERROR_COMMUNICATION_TIMEOUT = 83, // 通信超时
    ATE_ERROR_COMMUNICATION_ERROR = 84,   // 通信错误
    ATE_ERROR_PROTOCOL_ERROR = 85,        // 协议错误
    ATE_ERROR_CHECKSUM_ERROR = 86,        // 校验和错误
    ATE_ERROR_NETWORK_ERROR = 87,         // 网络错误
    ATE_ERROR_PORT_IN_USE = 88,           // 端口被占用
    ATE_ERROR_AUTHENTICATION_FAILED = 89, // 认证失败
    
    // 命令相关错误 (120-139)
    ATE_ERROR_INVALID_COMMAND = 120,      // 无效命令
    ATE_ERROR_COMMAND_NOT_SUPPORTED = 121,// 命令不支持
    ATE_ERROR_COMMAND_FAILED = 122,       // 命令执行失败
    ATE_ERROR_COMMAND_TIMEOUT = 123,      // 命令超时
    ATE_ERROR_COMMAND_ABORTED = 124,      // 命令被中止
    ATE_ERROR_FUNCTION_NOT_FOUND = 125,   // 函数未找到
    
    // 资源相关错误 (140-159)
    ATE_ERROR_INSUFFICIENT_MEMORY = 140,  // 内存不足
    ATE_ERROR_RESOURCE_BUSY = 141,        // 资源忙
    ATE_ERROR_RESOURCE_UNAVAILABLE = 142, // 资源不可用
    ATE_ERROR_QUOTA_EXCEEDED = 143,       // 配额超限
    ATE_ERROR_HANDLE_INVALID = 144,       // 句柄无效
    ATE_ERROR_NOT_FOUND = 145,            // 未找到
    
    // 文件系统相关错误 (160-179)
    ATE_ERROR_FILE_NOT_FOUND = 160,       // 文件未找到
    ATE_ERROR_FILE_ACCESS_DENIED = 161,   // 文件访问拒绝
    ATE_ERROR_FILE_ALREADY_EXISTS = 162,  // 文件已存在
    ATE_ERROR_FILE_CORRUPTED = 163,       // 文件损坏
    ATE_ERROR_DISK_FULL = 164,            // 磁盘空间不足
    ATE_ERROR_PATH_NOT_FOUND = 165,       // 路径未找到
    
    // 权限相关错误 (180-199)
    ATE_ERROR_PERMISSION_DENIED = 180,    // 权限拒绝
    ATE_ERROR_ACCESS_DENIED = 181,        // 访问拒绝
    ATE_ERROR_OPERATION_NOT_PERMITTED = 182, // 操作不被允许
    ATE_ERROR_INSUFFICIENT_PRIVILEGES = 183, // 权限不足
    
    // 数据相关错误 (200-219)
    ATE_ERROR_DATA_INVALID = 200,         // 数据无效
    ATE_ERROR_DATA_CORRUPTED = 201,       // 数据损坏
    ATE_ERROR_DATA_OVERFLOW = 202,        // 数据溢出
    ATE_ERROR_DATA_UNDERFLOW = 203,       // 数据下溢
    ATE_ERROR_DATA_OUT_OF_RANGE = 204,    // 数据超出范围
    ATE_ERROR_CONVERSION_FAILED = 205,    // 转换失败
    
    // 线程/同步相关错误 (220-239)
    ATE_ERROR_THREAD_CREATE_FAILED = 220, // 线程创建失败
    ATE_ERROR_THREAD_JOIN_FAILED = 221,   // 线程等待失败
    ATE_ERROR_MUTEX_LOCK_FAILED = 222,    // 互斥锁失败
    ATE_ERROR_SEMAPHORE_FAILED = 223,     // 信号量失败
    ATE_ERROR_DEADLOCK_DETECTED = 224,    // 检测到死锁
    
    // 系统相关错误 (240-259)
    ATE_ERROR_SYSTEM_ERROR = 240,         // 系统错误
    ATE_ERROR_OUT_OF_MEMORY = 241,        // 系统内存不足
    ATE_ERROR_SYSTEM_BUSY = 242,          // 系统忙
    ATE_ERROR_OPERATION_CANCELLED = 243,  // 操作被取消
    ATE_ERROR_OPERATION_ABORTED = 244,    // 操作被中止
    ATE_ERROR_SERVICE_UNAVAILABLE = 245,  // 服务不可用
    
    // 测量/仪器相关错误 (260-299)
    ATE_ERROR_MEASUREMENT_FAILED = 260,   // 测量失败
    ATE_ERROR_CALIBRATION_REQUIRED = 261, // 需要校准
    ATE_ERROR_SELF_TEST_FAILED = 262,     // 自检失败
    ATE_ERROR_TEMPERATURE_OUT_OF_RANGE = 263, // 温度超出范围
    ATE_ERROR_VOLTAGE_OUT_OF_RANGE = 264, // 电压超出范围
    ATE_ERROR_CURRENT_OUT_OF_RANGE = 265, // 电流超出范围
    ATE_ERROR_FREQUENCY_OUT_OF_RANGE = 266, // 频率超出范围
    ATE_ERROR_POWER_SUPPLY_FAULT = 267,   // 电源故障
    ATE_ERROR_SAFETY_INTERLOCK = 268,     // 安全联锁
    ATE_ERROR_OVERTEMPERATURE = 269,      // 过温保护
    
    // 警告相关代码 (280-299)
    ATE_WARNING_THRESHOLD_EXCEEDED = 280, // 阈值超限警告
    ATE_WARNING_PERFORMANCE_DEGRADED = 281, // 性能下降警告
    ATE_WARNING_RESOURCE_LOW = 282,       // 资源不足警告
    
    // 厂商自定义错误范围 (300-899)
    ATE_ERROR_VENDOR_SPECIFIC_BASE = 300, // 厂商自定义错误基础值
    
    // 通用错误
    ATE_ERROR_UNKNOWN = 999               // 未知错误
} ATE_EC;

// 命令模式枚举
typedef enum {
    COMMAND_WRITE_ONLY = 0,    // 只写入，不等待响应
    COMMAND_READ_ONLY = 1,     // 只读取，不写入
    COMMAND_QUERY = 2,         // 查询模式，等待文本响应
    COMMAND_DYNAIC_CONFIG = 3, // 动态配置，根据命令动态调整
} E5000_CommandMode;

/**
 * @brief 有限状态机状态枚举
 * 用于engine_core和global_core的线程状态管理
 * 
 * 状态转换规则：
 * - READY → RUNNING、DEBUGGING、TERMINATED
 * - RUNNING → PAUSED、DEBUGGING、TERMINATED、ERROR_STATE
 * - PAUSED → RUNNING、DEBUGGING、TERMINATED
 * - DEBUGGING → RUNNING、PAUSED、TERMINATED
 * - ERROR_STATE → TERMINATED（通过cleanup恢复）
 * - TERMINATED → 生命周期结束
 */
enum class FSMState {
    READY,          // 就绪 - initialize()完成后的初始状态
    RUNNING,        // 运行中 - 正常执行状态
    PAUSED,         // 暂停 - 可恢复的暂停状态
    DEBUGGING,      // 调试中 - 调试模式状态
    TERMINATED,     // 终止 - cleanup()完成后的最终状态
    ERROR_STATE     // 错误状态 - 异常处理状态
};

#endif // COMMON_TYPES_HPP