#ifndef GLOBAL_VARIABLE_TASK_HPP
#define GLOBAL_VARIABLE_TASK_HPP

#include <unordered_map>
#include <string>
#include <memory>
#include <mutex>
#include <vector>
#include <atomic>
#include "common_types.hpp"
#include "variable_types.hpp"

/**
 * @brief Redis配置结构体
 */
struct RedisConfig {
    std::string host = "127.0.0.1";
    int port = 6379;
    std::string password = "";
    int database = 0;
    std::string key_prefix = "ate_";
    std::string instance_key_format = "{instance_id}.{variable_name}";
    int timeout_ms = 5000;
    
    bool is_valid() const;
};

/**
 * @brief 全局变量系统类
 * 负责管理ATE系统中的全局变量，支持Redis存储和多实例访问
 */
class EXP_API GlobalVariableTask {
public:
    /**
     * @brief 构造函数
     * @param instance_id 设备实例ID
     * @param redis_config Redis配置
     */
    explicit GlobalVariableTask(const std::string& instance_id, const RedisConfig& redis_config = RedisConfig{});
    
    /**
     * @brief 析构函数
     */
    ~GlobalVariableTask() noexcept;
    
    // 禁用拷贝和移动
    GlobalVariableTask(const GlobalVariableTask&) = delete;
    GlobalVariableTask& operator=(const GlobalVariableTask&) = delete;
    GlobalVariableTask(GlobalVariableTask&&) = delete;
    GlobalVariableTask& operator=(GlobalVariableTask&&) = delete;
    
    /**
     * @brief 初始化Redis连接
     * @return ATE_EC 错误代码
     */
    ATE_EC initialize();
    
    /**
     * @brief 关闭连接并清理资源
     */
    void shutdown();
    
    /**
     * @brief 设置变量值
     * @param name 变量名
     * @param value 变量值
     * @return ATE_EC 错误代码
     */
    ATE_EC set_variable(const std::string& name, const VariableValue& value);
    
    /**
     * @brief 获取变量值
     * @param name 变量名
     * @param value 输出变量值
     * @return ATE_EC 错误代码
     */
    ATE_EC get_variable(const std::string& name, VariableValue& value) const;
    
    /**
     * @brief 检查变量是否存在
     * @param name 变量名
     * @return true 存在，false 不存在
     */
    bool has_variable(const std::string& name) const;
    
    /**
     * @brief 删除变量
     * @param name 变量名
     * @return ATE_EC 错误代码
     */
    ATE_EC remove_variable(const std::string& name);
    
    /**
     * @brief 清空所有变量
     * @return ATE_EC 错误代码
     */
    ATE_EC clear_all_variables();
    
    /**
     * @brief 获取所有变量名列表
     * @return 变量名列表
     */
    std::vector<std::string> get_variable_names() const;
    
    /**
     * @brief 获取变量数量
     * @return 变量数量
     */
    size_t get_variable_count() const;
    
    /**
     * @brief 从Redis同步变量到本地缓存
     * @return ATE_EC 错误代码
     */
    ATE_EC sync_from_redis();
    
    /**
     * @brief 将本地缓存同步到Redis
     * @return ATE_EC 错误代码
     */
    ATE_EC sync_to_redis();
    
    /**
     * @brief 获取实例ID
     * @return 实例ID
     */
    const std::string& get_instance_id() const;
    
    /**
     * @brief 检查Redis连接状态
     * @return true 已连接，false 未连接
     */
    bool is_connected() const;
    
    // 类型特化的便捷方法
    ATE_EC set_double(const std::string& name, double value, const std::string& unit = "");
    ATE_EC set_string(const std::string& name, const std::string& value, const std::string& unit = "");
    ATE_EC set_bool(const std::string& name, bool value, const std::string& unit = "");
    ATE_EC set_int(const std::string& name, int value, const std::string& unit = "");
    
    ATE_EC get_double(const std::string& name, double& value) const;
    ATE_EC get_string(const std::string& name, std::string& value) const;
    ATE_EC get_bool(const std::string& name, bool& value) const;
    ATE_EC get_int(const std::string& name, int& value) const;

private:
    std::string m_instance_id;                                    ///< 设备实例ID
    RedisConfig m_redis_config;                                   ///< Redis配置
    mutable std::unordered_map<std::string, VariableValue> m_variables;   ///< 本地变量缓存
    mutable std::mutex m_variables_mutex;                         ///< 变量访问互斥锁
    std::atomic<bool> m_initialized;                              ///< 初始化状态
    std::atomic<bool> m_connected;                                ///< Redis连接状态
    
    // Redis连接相关（使用原生socket实现）
    int m_redis_socket;                                           ///< Redis socket句柄
    mutable std::mutex m_redis_mutex;                             ///< Redis操作互斥锁
    
    /**
     * @brief 连接到Redis服务器
     * @return ATE_EC 错误代码
     */
    ATE_EC connect_to_redis();
    
    /**
     * @brief 断开Redis连接
     */
    void disconnect_from_redis();
    
    /**
     * @brief 生成Redis键名
     * @param variable_name 变量名
     * @return Redis键名
     */
    std::string generate_redis_key(const std::string& variable_name) const;
    
    /**
     * @brief 向Redis发送命令
     * @param command Redis命令
     * @param response 响应数据
     * @return ATE_EC 错误代码
     */
    ATE_EC send_redis_command(const std::string& command, std::string& response) const;
    
    /**
     * @brief 序列化变量值为字符串
     * @param value 变量值
     * @return 序列化字符串
     */
    std::string serialize_variable(const VariableValue& value) const;
    
    /**
     * @brief 反序列化字符串为变量值
     * @param data 序列化字符串
     * @param value 输出变量值
     * @return ATE_EC 错误代码
     */
    ATE_EC deserialize_variable(const std::string& data, VariableValue& value) const;
};

#endif // GLOBAL_VARIABLE_TASK_HPP