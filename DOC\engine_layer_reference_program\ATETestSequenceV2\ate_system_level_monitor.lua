-- ============================================================================
-- ATE系统级监控模块 - Step-Based Framework
-- 文件: ate_system_level_monitor.lua
-- 描述: 系统级监控，监控整个ATE系统的运行状态，包括硬件设备、软件状态等
-- 版本: V2.1 (标准化step-based工步控制框架)
-- 功能：监测ATE整体系统状态，包括系统资源、基础设施设备、全局安全保护
-- 职责：CPU、内存、磁盘、网络、电源、散热、UPS、环境监控
-- 作者：TyrOnline
-- 创建时间：2025
-- ============================================================================

-- 工步控制变量
local step = 0
local run_flag = {1, 1, 1, 1, 1, 1, 0, 0, 0, 0}  -- 前6个工步启用
local test_result = "UNKNOWN"
local error_status = {}

-- 测试数据记录
local test_data_points = {}
local measurement_count = 0

-- 固定功能begin
function add_string(str)
    if not error_status[str] then
        error_status[str] = true
        return true
    end
    return false
end

-- 工步保护状态检查
function seq_protect_check()
    -- 监控模块不需要设备状态检查，直接返回true
    return true
end

-- 工步保护动作执行
function seq_protect_act()
    if (next(error_status) ~= nil) then
        for error_key, _ in pairs(error_status) do
            log_system_level_message("系统级监控过程中发生错误: " .. error_key)
        end
        -- 通过故障保护动作的组合返回做3种情况，只记录+不动作+继续执行，记录+动作+继续执行，记录+动作+停止执行
        -- 无函数体 不动作
        -- return 1 动作后返回1  则动作了 后面停止执行
        -- return 2 动作了返回2  则动作了 后面继续执行
    end
    return false  -- 继续监控
end

-- 自定精确延时函数（毫秒级）
-- @param x 总延时时间（毫秒）
function delayms(x)
    local sleepms = 10  -- 每次循环基础延时
    local loops = math.floor(x / sleepms)  -- 完整循环次数
    local remainder = x % sleepms  -- 剩余延时
    
    -- 执行完整循环延时
    for i = 1, loops do
        -- 休眠循环基础延时 sleep_ms 采用C++注册的方式，注册一个通用的接口给lua
        if sleep_ms then sleep_ms(sleepms) end
        if seq_protect_check() == false then
            return
        end
    end
    
    -- 执行剩余延时
    if remainder > 0 then
        -- 休眠毫秒剩余
        if sleep_ms then sleep_ms(remainder) end
        if seq_protect_check() == false then
            return
        end
    end
end
-- 固定功能end

-- ============================================================================
-- 系统级监控状态和配置
-- ============================================================================

-- 系统监控状态
local system_monitor_state = {
    is_running = false,
    start_time = 0,
    last_check_time = 0,
    check_interval = 2000,  -- 系统级检查间隔(毫秒)
    emergency_stop = false,
    alert_count = 0,
    system_health_score = 100  -- 系统健康评分(0-100)
}

-- 系统级监控阈值配置
local system_thresholds = {
    -- 系统资源阈值 (3级分级)
    cpu_usage = {
        level1 = 70,    -- 1级告警：性能优化
        level2 = 85,    -- 2级告警：降低负载
        level3 = 95     -- 3级告警：紧急停机
    },
    memory_usage = {
        level1 = 75,    -- 1级告警：内存清理
        level2 = 90,    -- 2级告警：强制回收
        level3 = 98     -- 3级告警：系统重启
    },
    disk_usage = {
        level1 = 80,    -- 1级告警：清理临时文件
        level2 = 90,    -- 2级告警：清理日志
        level3 = 98     -- 3级告警：停止测试
    },
    network_latency = {
        level1 = 100,   -- 1级告警：网络优化
        level2 = 500,   -- 2级告警：切换网络
        level3 = 2000   -- 3级告警：网络故障
    },
    
    -- 基础设施阈值 (3级分级)
    system_temperature = {
        level1 = 70,    -- 1级告警：启动风扇
        level2 = 80,    -- 2级告警：降频运行
        level3 = 90     -- 3级告警：紧急停机
    },
    power_voltage = {
        level1_min = 11.8, level1_max = 12.2,  -- 1级告警：电压监控
        level2_min = 11.5, level2_max = 12.5,  -- 2级告警：电源调整
        level3_min = 11.0, level3_max = 13.0   -- 3级告警：紧急断电
    },
    ups_battery = {
        level1 = 30,    -- 1级告警：电池低
        level2 = 15,    -- 2级告警：准备停机
        level3 = 5      -- 3级告警：立即停机
    }
}

-- 系统状态缓存
local system_states = {
    cpu = {
        usage_percent = 0,
        temperature = 0,
        frequency = 0,
        last_update = 0
    },
    memory = {
        usage_percent = 0,
        available_mb = 0,
        total_mb = 0,
        last_update = 0
    },
    disk = {
        usage_percent = 0,
        free_space_gb = 0,
        total_space_gb = 0,
        last_update = 0
    },
    network = {
        latency_ms = 0,
        packet_loss = 0,
        bandwidth_usage = 0,
        last_update = 0
    },
    power_system = {
        main_voltage = 0,
        ups_status = "unknown",
        ups_battery_percent = 0,
        power_consumption = 0,
        last_update = 0
    },
    cooling_system = {
        fan_speed = 0,
        system_temp = 0,
        cooling_status = "unknown",
        last_update = 0
    }
}

-- 系统告警历史
local system_alert_history = {}

-- ============================================================================
-- 系统资源监控检查
-- ============================================================================

-- 检查CPU状态
local function check_cpu_status()
    local alerts = {}
    local current_time = os.time() * 1000
    
    -- 模拟获取CPU使用率 (实际应用中需要调用系统API)
    local cpu_usage = math.random(20, 100)  -- 模拟CPU使用率
    local cpu_temp = math.random(40, 95)    -- 模拟CPU温度
    
    system_states.cpu.usage_percent = cpu_usage
    system_states.cpu.temperature = cpu_temp
    system_states.cpu.last_update = current_time
    
    -- CPU使用率分级检查
    if cpu_usage >= system_thresholds.cpu_usage.level3 then
        table.insert(alerts, {level = 3, type = "CRITICAL", category = "SYSTEM_CPU", 
                             message = string.format("CPU使用率严重过高: %d%%，执行紧急停机", cpu_usage), 
                             value = cpu_usage})
    elseif cpu_usage >= system_thresholds.cpu_usage.level2 then
        table.insert(alerts, {level = 2, type = "WARNING", category = "SYSTEM_CPU", 
                             message = string.format("CPU使用率过高: %d%%，降低系统负载", cpu_usage), 
                             value = cpu_usage})
    elseif cpu_usage >= system_thresholds.cpu_usage.level1 then
        table.insert(alerts, {level = 1, type = "INFO", category = "SYSTEM_CPU", 
                             message = string.format("CPU使用率较高: %d%%，进行性能优化", cpu_usage), 
                             value = cpu_usage})
    end
    
    -- CPU温度分级检查
    if cpu_temp >= system_thresholds.system_temperature.level3 then
        table.insert(alerts, {level = 3, type = "CRITICAL", category = "SYSTEM_TEMP", 
                             message = string.format("CPU温度严重过高: %d°C，紧急停机保护", cpu_temp), 
                             value = cpu_temp})
    elseif cpu_temp >= system_thresholds.system_temperature.level2 then
        table.insert(alerts, {level = 2, type = "WARNING", category = "SYSTEM_TEMP", 
                             message = string.format("CPU温度过高: %d°C，启动降频保护", cpu_temp), 
                             value = cpu_temp})
    elseif cpu_temp >= system_thresholds.system_temperature.level1 then
        table.insert(alerts, {level = 1, type = "INFO", category = "SYSTEM_TEMP", 
                             message = string.format("CPU温度较高: %d°C，启动散热风扇", cpu_temp), 
                             value = cpu_temp})
    end
    
    return alerts
end

-- 检查内存状态
local function check_memory_status()
    local alerts = {}
    local current_time = os.time() * 1000
    
    -- 模拟获取内存使用率
    local memory_usage = math.random(30, 98)
    local available_mb = math.random(1000, 8000)
    local total_mb = 16384
    
    system_states.memory.usage_percent = memory_usage
    system_states.memory.available_mb = available_mb
    system_states.memory.total_mb = total_mb
    system_states.memory.last_update = current_time
    
    -- 内存使用率分级检查
    if memory_usage >= system_thresholds.memory_usage.level3 then
        table.insert(alerts, {level = 3, type = "CRITICAL", category = "SYSTEM_MEMORY", 
                             message = string.format("内存使用率严重过高: %d%%，系统即将重启", memory_usage), 
                             value = memory_usage})
    elseif memory_usage >= system_thresholds.memory_usage.level2 then
        table.insert(alerts, {level = 2, type = "WARNING", category = "SYSTEM_MEMORY", 
                             message = string.format("内存使用率过高: %d%%，强制垃圾回收", memory_usage), 
                             value = memory_usage})
    elseif memory_usage >= system_thresholds.memory_usage.level1 then
        table.insert(alerts, {level = 1, type = "INFO", category = "SYSTEM_MEMORY", 
                             message = string.format("内存使用率较高: %d%%，清理内存缓存", memory_usage), 
                             value = memory_usage})
    end
    
    return alerts
end

-- 检查磁盘状态
local function check_disk_status()
    local alerts = {}
    local current_time = os.time() * 1000
    
    -- 模拟获取磁盘使用率
    local disk_usage = math.random(50, 99)
    local free_space_gb = math.random(10, 500)
    local total_space_gb = 1000
    
    system_states.disk.usage_percent = disk_usage
    system_states.disk.free_space_gb = free_space_gb
    system_states.disk.total_space_gb = total_space_gb
    system_states.disk.last_update = current_time
    
    -- 磁盘使用率分级检查
    if disk_usage >= system_thresholds.disk_usage.level3 then
        table.insert(alerts, {level = 3, type = "CRITICAL", category = "SYSTEM_DISK", 
                             message = string.format("磁盘空间严重不足: %d%%，停止所有测试", disk_usage), 
                             value = disk_usage})
    elseif disk_usage >= system_thresholds.disk_usage.level2 then
        table.insert(alerts, {level = 2, type = "WARNING", category = "SYSTEM_DISK", 
                             message = string.format("磁盘空间不足: %d%%，清理日志文件", disk_usage), 
                             value = disk_usage})
    elseif disk_usage >= system_thresholds.disk_usage.level1 then
        table.insert(alerts, {level = 1, type = "INFO", category = "SYSTEM_DISK", 
                             message = string.format("磁盘空间较少: %d%%，清理临时文件", disk_usage), 
                             value = disk_usage})
    end
    
    return alerts
end

-- 检查电源系统状态
local function check_power_system_status()
    local alerts = {}
    local current_time = os.time() * 1000
    
    -- 模拟获取电源状态
    local main_voltage = 11.5 + math.random() * 1.0  -- 11.5-12.5V
    local ups_battery = math.random(5, 100)
    local power_consumption = math.random(200, 800)
    
    system_states.power_system.main_voltage = main_voltage
    system_states.power_system.ups_battery_percent = ups_battery
    system_states.power_system.power_consumption = power_consumption
    system_states.power_system.last_update = current_time
    
    -- 主电源电压分级检查
    if main_voltage <= system_thresholds.power_voltage.level3_min or 
       main_voltage >= system_thresholds.power_voltage.level3_max then
        table.insert(alerts, {level = 3, type = "CRITICAL", category = "SYSTEM_POWER", 
                             message = string.format("主电源电压异常: %.2fV，紧急断电保护", main_voltage), 
                             value = main_voltage})
    elseif main_voltage <= system_thresholds.power_voltage.level2_min or 
           main_voltage >= system_thresholds.power_voltage.level2_max then
        table.insert(alerts, {level = 2, type = "WARNING", category = "SYSTEM_POWER", 
                             message = string.format("主电源电压不稳: %.2fV，调整电源参数", main_voltage), 
                             value = main_voltage})
    elseif main_voltage <= system_thresholds.power_voltage.level1_min or 
           main_voltage >= system_thresholds.power_voltage.level1_max then
        table.insert(alerts, {level = 1, type = "INFO", category = "SYSTEM_POWER", 
                             message = string.format("主电源电压偏离: %.2fV，加强监控", main_voltage), 
                             value = main_voltage})
    end
    
    -- UPS电池分级检查
    if ups_battery <= system_thresholds.ups_battery.level3 then
        table.insert(alerts, {level = 3, type = "CRITICAL", category = "SYSTEM_UPS", 
                             message = string.format("UPS电池严重不足: %d%%，立即停机", ups_battery), 
                             value = ups_battery})
    elseif ups_battery <= system_thresholds.ups_battery.level2 then
        table.insert(alerts, {level = 2, type = "WARNING", category = "SYSTEM_UPS", 
                             message = string.format("UPS电池不足: %d%%，准备停机", ups_battery), 
                             value = ups_battery})
    elseif ups_battery <= system_thresholds.ups_battery.level1 then
        table.insert(alerts, {level = 1, type = "INFO", category = "SYSTEM_UPS", 
                             message = string.format("UPS电池较低: %d%%，检查主电源", ups_battery), 
                             value = ups_battery})
    end
    
    return alerts
end

-- ============================================================================
-- 系统级告警处理中心
-- ============================================================================

-- 记录系统告警
local function log_system_alert(alert)
    local timestamp = os.date("%Y-%m-%d %H:%M:%S")
    local log_entry = {
        timestamp = timestamp,
        level = alert.level,
        type = alert.type,
        category = alert.category,
        message = alert.message,
        value = alert.value
    }
    
    table.insert(system_alert_history, log_entry)
    
    -- 保持告警历史记录在合理范围内
    if #system_alert_history > 500 then
        table.remove(system_alert_history, 1)
    end
    
    -- 输出告警信息
    print(string.format("[SYSTEM][%s] Level-%d %s - %s: %s", 
          timestamp, alert.level, alert.type, alert.category, alert.message))
end

-- 处理系统告警
local function handle_system_alerts(alerts)
    for _, alert in ipairs(alerts) do
        log_system_alert(alert)
        system_monitor_state.alert_count = system_monitor_state.alert_count + 1
        
        -- 根据告警级别执行相应动作
        if alert.level == 3 then
            handle_system_level3_alert(alert)
        elseif alert.level == 2 then
            handle_system_level2_alert(alert)
        elseif alert.level == 1 then
            handle_system_level1_alert(alert)
        end
    end
end

-- ============================================================================
-- 系统级3级分级响应动作
-- ============================================================================

-- 1级响应动作：优化和调整
function handle_system_level1_alert(alert)
    if alert.category == "SYSTEM_CPU" then
        print("执行1级CPU优化：清理进程缓存，优化任务调度")
        -- 清理系统缓存
        collectgarbage("collect")
        -- 降低非关键任务优先级
        
    elseif alert.category == "SYSTEM_MEMORY" then
        print("执行1级内存优化：清理内存缓存，释放未使用资源")
        collectgarbage("collect")
        -- 清理临时变量
        
    elseif alert.category == "SYSTEM_DISK" then
        print("执行1级磁盘清理：删除临时文件")
        cleanup_temp_files_level1()
        
    elseif alert.category == "SYSTEM_TEMP" then
        print("执行1级散热：启动系统风扇")
        activate_system_fans()
        
    elseif alert.category == "SYSTEM_POWER" then
        print("执行1级电源监控：加强电压监测")
        increase_power_monitoring_frequency()
        
    elseif alert.category == "SYSTEM_UPS" then
        print("执行1级UPS检查：检查主电源连接")
        check_main_power_connection()
    end
end

-- 2级响应动作：保护和限制
function handle_system_level2_alert(alert)
    if alert.category == "SYSTEM_CPU" then
        print("执行2级CPU保护：降低系统负载，关闭非必要服务")
        reduce_system_load()
        stop_non_essential_services()
        
    elseif alert.category == "SYSTEM_MEMORY" then
        print("执行2级内存保护：强制垃圾回收，清理大对象")
        collectgarbage("collect")
        force_memory_cleanup()
        
    elseif alert.category == "SYSTEM_DISK" then
        print("执行2级磁盘保护：清理日志文件，压缩数据")
        cleanup_log_files_level2()
        compress_old_data()
        
    elseif alert.category == "SYSTEM_TEMP" then
        print("执行2级散热保护：启动降频模式")
        activate_cpu_throttling()
        increase_fan_speed()
        
    elseif alert.category == "SYSTEM_POWER" then
        print("执行2级电源保护：调整电源参数，降低功耗")
        adjust_power_parameters()
        reduce_power_consumption()
        
    elseif alert.category == "SYSTEM_UPS" then
        print("执行2级UPS保护：准备系统停机，保存关键数据")
        prepare_system_shutdown()
        save_critical_data()
    end
end

-- 3级响应动作：紧急保护
function handle_system_level3_alert(alert)
    system_monitor_state.emergency_stop = true
    
    if alert.category == "SYSTEM_CPU" then
        print("执行3级CPU保护：紧急停机，保护硬件")
        emergency_system_shutdown("CPU过载")
        
    elseif alert.category == "SYSTEM_MEMORY" then
        print("执行3级内存保护：系统重启，恢复内存")
        emergency_system_restart("内存耗尽")
        
    elseif alert.category == "SYSTEM_DISK" then
        print("执行3级磁盘保护：停止所有测试，保护数据")
        stop_all_tests("磁盘空间不足")
        emergency_data_backup()
        
    elseif alert.category == "SYSTEM_TEMP" then
        print("执行3级散热保护：紧急停机，防止硬件损坏")
        emergency_thermal_shutdown()
        
    elseif alert.category == "SYSTEM_POWER" then
        print("执行3级电源保护：紧急断电，保护设备")
        emergency_power_cutoff()
        
    elseif alert.category == "SYSTEM_UPS" then
        print("执行3级UPS保护：立即停机，保存所有数据")
        immediate_system_shutdown("UPS电池耗尽")
    end
end

-- ============================================================================
-- 系统级响应动作实现函数
-- ============================================================================

-- 1级响应动作实现
function cleanup_temp_files_level1()
    print("清理临时文件：删除缓存和临时数据")
    -- 实际实现：删除临时文件
end

function activate_system_fans()
    print("启动系统风扇：设置风扇转速为中等")
    -- 实际实现：控制风扇转速
end

function increase_power_monitoring_frequency()
    print("增加电源监控频率：提高电压检测精度")
    -- 实际实现：调整监控参数
end

function check_main_power_connection()
    print("检查主电源连接：验证电源线路状态")
    -- 实际实现：检测电源连接
end

-- 2级响应动作实现
function reduce_system_load()
    print("降低系统负载：限制CPU密集型任务")
    -- 实际实现：调整任务优先级
end

function stop_non_essential_services()
    print("停止非必要服务：关闭后台应用")
    -- 实际实现：停止服务
end

function force_memory_cleanup()
    print("强制内存清理：释放大对象和缓存")
    -- 实际实现：内存清理
end

function cleanup_log_files_level2()
    print("清理日志文件：删除旧日志，压缩当前日志")
    -- 实际实现：日志清理
end

function compress_old_data()
    print("压缩旧数据：压缩历史测试数据")
    -- 实际实现：数据压缩
end

function activate_cpu_throttling()
    print("启动CPU降频：降低处理器频率")
    -- 实际实现：CPU频率控制
end

function increase_fan_speed()
    print("提高风扇转速：最大散热模式")
    -- 实际实现：风扇控制
end

function adjust_power_parameters()
    print("调整电源参数：优化电压稳定性")
    -- 实际实现：电源调整
end

function reduce_power_consumption()
    print("降低功耗：关闭非必要硬件")
    -- 实际实现：功耗控制
end

function prepare_system_shutdown()
    print("准备系统停机：保存系统状态")
    -- 实际实现：停机准备
end

function save_critical_data()
    print("保存关键数据：备份重要文件")
    -- 实际实现：数据备份
end

-- 3级响应动作实现
function emergency_system_shutdown(reason)
    print(string.format("紧急系统停机：%s", reason))
    system_monitor_state.emergency_stop = true
    -- 实际实现：系统停机
end

function emergency_system_restart(reason)
    print(string.format("紧急系统重启：%s", reason))
    -- 实际实现：系统重启
end

function stop_all_tests(reason)
    print(string.format("停止所有测试：%s", reason))
    -- 实际实现：停止测试
end

function emergency_data_backup()
    print("紧急数据备份：备份所有重要数据")
    -- 实际实现：数据备份
end

function emergency_thermal_shutdown()
    print("紧急散热停机：防止硬件过热损坏")
    system_monitor_state.emergency_stop = true
    -- 实际实现：散热保护
end

function emergency_power_cutoff()
    print("紧急断电保护：切断所有电源")
    system_monitor_state.emergency_stop = true
    -- 实际实现：断电保护
end

function immediate_system_shutdown(reason)
    print(string.format("立即系统停机：%s", reason))
    system_monitor_state.emergency_stop = true
    -- 实际实现：立即停机
end

-- ============================================================================
-- 系统级主监控循环
-- ============================================================================

-- 执行系统级监控检查
local function perform_system_monitoring_check()
    local all_alerts = {}
    
    -- 执行各项系统检查
    local cpu_alerts = check_cpu_status()
    local memory_alerts = check_memory_status()
    local disk_alerts = check_disk_status()
    local power_alerts = check_power_system_status()
    
    -- 合并所有告警
    for _, alert in ipairs(cpu_alerts) do table.insert(all_alerts, alert) end
    for _, alert in ipairs(memory_alerts) do table.insert(all_alerts, alert) end
    for _, alert in ipairs(disk_alerts) do table.insert(all_alerts, alert) end
    for _, alert in ipairs(power_alerts) do table.insert(all_alerts, alert) end
    
    -- 处理告警
    if #all_alerts > 0 then
        handle_system_alerts(all_alerts)
    end
    
    -- 更新系统健康评分
    update_system_health_score(all_alerts)
    
    return all_alerts
end

-- 更新系统健康评分
function update_system_health_score(alerts)
    local score = 100
    
    for _, alert in ipairs(alerts) do
        if alert.level == 3 then
            score = score - 30  -- 3级告警严重影响健康评分
        elseif alert.level == 2 then
            score = score - 15  -- 2级告警中等影响
        elseif alert.level == 1 then
            score = score - 5   -- 1级告警轻微影响
        end
    end
    
    system_monitor_state.system_health_score = math.max(0, score)
end

-- 系统级监控主循环
function start_system_level_monitor()
    if system_monitor_state.is_running then
        print("系统级监控已在运行中")
        return false
    end
    
    system_monitor_state.is_running = true
    system_monitor_state.start_time = os.time() * 1000
    system_monitor_state.emergency_stop = false
    
    print("启动ATE系统级监控...")
    print("监控范围：CPU、内存、磁盘、网络、电源、散热、UPS")
    print("保护级别：3级分级响应机制")
    
    while system_monitor_state.is_running and not system_monitor_state.emergency_stop do
        local current_time = os.time() * 1000
        
        if current_time - system_monitor_state.last_check_time >= system_monitor_state.check_interval then
            system_monitor_state.last_check_time = current_time
            
            -- 执行系统级监控检查
            local alerts = perform_system_monitoring_check()
            
            -- 输出系统状态摘要
            if #alerts == 0 then
                print(string.format("[系统级监控] 系统状态正常 - 健康评分: %d/100", 
                      system_monitor_state.system_health_score))
            end
        end
        
        -- 短暂休眠，避免过度占用CPU
        -- 在实际应用中，这里应该使用适当的休眠函数
        os.execute("timeout /t 1 /nobreak > nul 2>&1")  -- Windows
        -- os.execute("sleep 1")  -- Linux/Unix
    end
    
    print("系统级监控已停止")
    return true
end

-- 停止系统级监控
function stop_system_level_monitor()
    if not system_monitor_state.is_running then
        print("系统级监控未在运行")
        return false
    end
    
    system_monitor_state.is_running = false
    print("正在停止系统级监控...")
    return true
end

-- 获取系统级监控状态
function get_system_monitor_status()
    return {
        is_running = system_monitor_state.is_running,
        start_time = system_monitor_state.start_time,
        alert_count = system_monitor_state.alert_count,
        emergency_stop = system_monitor_state.emergency_stop,
        health_score = system_monitor_state.system_health_score,
        system_states = system_states,
        recent_alerts = system_alert_history
    }
end

-- 更新系统级监控配置
function update_system_monitor_config(new_config)
    if new_config.check_interval then
        system_monitor_state.check_interval = new_config.check_interval
    end
    
    if new_config.thresholds then
        for key, value in pairs(new_config.thresholds) do
            if system_thresholds[key] then
                system_thresholds[key] = value
            end
        end
    end
    
    print("系统级监控配置已更新")
    return true
end

-- ============================================================================
-- 系统级监控初始化
-- ============================================================================

print("ATE系统级监控模块已加载")
print("功能：系统资源、基础设施、全局安全保护")
print("保护机制：3级分级响应 (1级优化 -> 2级保护 -> 3级紧急)")
print("调用方式：start_system_level_monitor() 启动监控")
print("状态查询：get_system_monitor_status() 获取状态")
print("配置更新：update_system_monitor_config(config) 更新配置")
print("停止监控：stop_system_level_monitor() 停止监控")

-- 脚本加载完成，等待外部调用启动函数