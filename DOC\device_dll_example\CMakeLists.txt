# 指定CMake最低版本要求为3.16
# 这个版本支持现代C++特性和更好的目标管理功能
cmake_minimum_required(VERSION 3.16)

# 定义项目名称、版本号和使用的编程语言
# E5000_Driver: 项目名称，用于生成的目标文件命名
# VERSION 1.0.0: 项目版本号，可用于版本控制和发布管理
# LANGUAGES CXX: 指定项目使用C++语言
project(EthClient_Driver VERSION 1.0.0 LANGUAGES CXX)

# ============================================================================
# C++标准设置
# ============================================================================

# 设置C++17标准
# C++17提供了现代C++特性，如结构化绑定、if constexpr、std::optional等
set(CMAKE_CXX_STANDARD 17)

# 要求必须支持指定的C++标准，如果编译器不支持则报错
# 确保代码在所有平台上都能使用C++17特性
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 禁用编译器特定的C++扩展
# 确保代码的可移植性，避免依赖特定编译器的非标准扩展
set(CMAKE_CXX_EXTENSIONS OFF)

# ============================================================================
# 构建类型配置
# ============================================================================

# 如果没有指定构建类型，默认设置为Release模式
# Release模式会启用优化，生成更高效的可执行文件
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# ============================================================================
# 编译器特定选项配置
# ============================================================================

# 针对不同编译器设置特定的编译选项
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    # GCC编译器选项
    # -Wall: 启用大部分警告
    # -Wextra: 启用额外的警告
    # -Wpedantic: 启用严格的ISO C++标准警告
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Wpedantic")
    
    # Debug模式选项
    # -g: 生成调试信息
    # -O0: 禁用优化，便于调试
    set(CMAKE_CXX_FLAGS_DEBUG "-g -O0")
    
    # Release模式选项
    # -O3: 最高级别优化
    # -DNDEBUG: 定义NDEBUG宏，禁用assert断言
    set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
    # MSVC编译器选项
    # /W4: 启用4级警告（最高级别的有用警告）
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W4")
endif()

# ============================================================================
# 包含目录设置
# ============================================================================

# 添加项目的头文件目录
# 使所有源文件都能找到include目录下的头文件
include_directories(include)

# 添加第三方库目录到包含路径
include_directories(${CMAKE_SOURCE_DIR}/third_library/)  

# ============================================================================
# 依赖包查找
# ============================================================================

# 查找线程库，用于多线程支持
# REQUIRED表示如果找不到该包则构建失败
find_package(Threads REQUIRED)

# 注意：Redis客户端使用原生socket实现（Windows上使用ws2_32）
# 不需要外部Redis库依赖，减少了第三方依赖的复杂性

# ============================================================================
# 子目录添加
# ============================================================================

# 添加源代码子目录
# 这会处理src目录下的CMakeLists.txt文件
add_subdirectory(src)

# 添加测试子目录（当前被注释掉）
# 取消注释后可以构建和运行单元测试
# add_subdirectory(tests)

# ============================================================================
# 文件复制配置
# ============================================================================

# 将配置文件复制到构建目录
# 确保运行时能够找到必要的配置文件（如KwlEthClient_V01.json）
file(COPY config/ DESTINATION ${CMAKE_BINARY_DIR}/config/)

# ============================================================================
# 安装配置
# ============================================================================

# 安装生成的库文件到指定目录
# RUNTIME DESTINATION bin: 可执行文件和DLL安装到bin目录
# LIBRARY DESTINATION lib: 共享库安装到lib目录
# ARCHIVE DESTINATION lib: 静态库安装到lib目录
install(TARGETS EthClient_lib EthClient_dll
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

# 安装配置文件目录
# 将config目录下的所有文件安装到目标系统的config目录
install(DIRECTORY config/ DESTINATION config/)

# 安装头文件目录
# 将include目录下的所有头文件安装到目标系统的include目录
# 便于其他项目使用这个库
install(DIRECTORY include/ DESTINATION include/)