/**
 * @file variable_types.hpp
 * @brief 变量类型定义文件
 * <AUTHOR> Framework Team
 * @version 1.0
 * @date 2024
 * @details 定义了ATE系统中使用的变量类型和变量值结构体，
 *          供engine_variable_system、global_variable_service和global_variable_system模块共同使用
 */

#ifndef VARIABLE_TYPES_HPP
#define VARIABLE_TYPES_HPP

#include <string>
#include <cstring>
#include <memory>
#include <array>
#include <chrono>
#include <set>

// 前向声明
enum class LuaExecutionState;
enum class LuaScriptType;
struct DebugBreakpoint;
struct LuaExecutionContext;

/**
 * @brief Lua脚本执行状态枚举
 */
enum class LuaExecutionState {
    IDLE,           ///< 空闲状态
    RUNNING,        ///< 正在执行
    SUSPENDED,      ///< 已挂起
    STOPPED,        ///< 已停止
    DEBUG_BREAK,    ///< 调试断点
    ERROR_STATE     ///< 错误状态
};

/**
 * @brief Lua脚本类型枚举
 */
enum class LuaScriptType {
    MA_SCRIPT,      ///< 主工艺脚本
    PR_SCRIPT       ///< 保护工艺脚本
};

/**
 * @brief 调试断点信息结构体
 */
struct DebugBreakpoint {
    int line_number;                    ///< 断点行号
    std::string script_name;            ///< 脚本名称
    bool enabled;                       ///< 是否启用
    std::string condition;              ///< 断点条件（可选）
    
    DebugBreakpoint(int line, const std::string& name, bool enable = true, const std::string& cond = "")
        : line_number(line), script_name(name), enabled(enable), condition(cond) {}
    
    // 为std::set提供比较操作符
    bool operator<(const DebugBreakpoint& other) const {
        if (script_name != other.script_name) {
            return script_name < other.script_name;
        }
        return line_number < other.line_number;
    }
    
    bool operator==(const DebugBreakpoint& other) const {
        return script_name == other.script_name && line_number == other.line_number;
    }
};

/**
 * @brief Lua执行上下文结构体
 */
struct LuaExecutionContext {
    LuaExecutionState state;            ///< 执行状态
    LuaScriptType script_type;          ///< 脚本类型
    std::string current_script;         ///< 当前执行的脚本内容
    std::string script_name;            ///< 脚本名称
    int current_line;                   ///< 当前执行行号
    std::string last_error;             ///< 最后错误信息
    std::chrono::system_clock::time_point start_time;  ///< 开始执行时间
    
    LuaExecutionContext() 
        : state(LuaExecutionState::IDLE)
        , script_type(LuaScriptType::MA_SCRIPT)
        , current_line(0)
        , start_time(std::chrono::system_clock::now()) {}
};

/**
 * @brief 变量类型枚举
 * @details 定义了系统支持的基本数据类型
 */
enum class VariableType {
    DOUBLE,  ///< 双精度浮点数类型
    STRING,  ///< 字符串类型
    BOOL,    ///< 布尔类型
    INT      ///< 整数类型
};

/**
 * @brief 固定大小的变量值结构体，包含单位信息
 * @details 支持多种基本数据类型，包含单位信息和保存标志
 */
struct VariableValue {
    VariableType type;        ///< 变量类型
    std::array<char, 16> unit;///< 变量单位
    bool should_save;         ///< 是否保存到数据记录器，默认不保存
    int scale_factor;         ///< 系数字段，表示有效小数位数（避免浮点精度问题）
    
    /**
     * @brief 使用union存储基本类型，字符串使用共享指针单独存储
     */
    union {
        double doubleValue;   ///< 双精度浮点数值
        bool boolValue;       ///< 布尔值
        int intValue;         ///< 整数值
    } data;
    
    std::shared_ptr<std::string> stringData;  ///< 动态字符串存储，支持任意长度和多国语言
    
    /**
     * @brief 默认构造函数
     * @details 初始化为double类型，值为0.0
     */
    VariableValue() : type(VariableType::DOUBLE), should_save(false), scale_factor(0) {
        data.doubleValue = 0.0;
        setUnit("");
    }
    
    /**
     * @brief double类型构造函数
     * @param value 双精度浮点数值
     * @param unit 单位，默认为空
     */
    VariableValue(double value, const std::string& unit = "") : type(VariableType::DOUBLE), should_save(false), scale_factor(0) {
        data.doubleValue = value;
        setUnit("");
    }
    
    /**
     * @brief string类型构造函数
     * @param value 字符串值
     * @param unit 单位，默认为空
     */
    VariableValue(const std::string& value, const std::string& unit = "") : type(VariableType::STRING), should_save(false), scale_factor(0) {
        stringData = std::make_shared<std::string>(value);
        setUnit("");
    }
    
    /**
     * @brief C字符串类型构造函数
     * @param value C字符串值
     * @param unit 单位，默认为空
     */
    VariableValue(const char* value, const std::string& unit = "") : type(VariableType::STRING), should_save(false), scale_factor(0) {
        stringData = std::make_shared<std::string>(value);
        setUnit("");
    }
    
    /**
     * @brief bool类型构造函数
     * @param value 布尔值
     * @param unit 单位，默认为空
     */
    VariableValue(bool value, const std::string& unit = "") : type(VariableType::BOOL), should_save(false), scale_factor(0) {
        data.boolValue = value;
        setUnit("");
    }
    
    /**
     * @brief int类型构造函数
     * @param value 整数值
     * @param unit 单位，默认为空
     */
    VariableValue(int value, const std::string& unit = "") : type(VariableType::INT), should_save(false), scale_factor(0) {
        data.intValue = value;
        setUnit("");
    }
    
    /**
     * @brief 拷贝构造函数
     * @param other 要拷贝的变量值对象
     */
    VariableValue(const VariableValue& other) : type(other.type), should_save(other.should_save), scale_factor(other.scale_factor) {
        unit = other.unit;
        if (type == VariableType::STRING && other.stringData) {
            stringData = std::make_shared<std::string>(*other.stringData);  // 深拷贝字符串数据
        } else {
            data = other.data;
        }
    }
    
    /**
     * @brief 赋值操作符
     * @param other 要赋值的变量值对象
     * @return 当前对象的引用
     */
    VariableValue& operator=(const VariableValue& other) {
        if (this != &other) {
            type = other.type;
            unit = other.unit;
            should_save = other.should_save;
            scale_factor = other.scale_factor;
            if (type == VariableType::STRING && other.stringData) {
                stringData = std::make_shared<std::string>(*other.stringData);  // 深拷贝字符串数据
            } else {
                data = other.data;
            }
        }
        return *this;
    }
    
    /**
     * @brief 转换为double类型
     * @return 双精度浮点数值
     * @details 根据当前类型进行适当的类型转换
     */
    double asDouble() const {
        switch (type) {
            case VariableType::DOUBLE: return data.doubleValue;
            case VariableType::INT: return static_cast<double>(data.intValue);
            case VariableType::BOOL: return data.boolValue ? 1.0 : 0.0;
            case VariableType::STRING: return stringData ? std::stod(*stringData) : 0.0;
            default: return 0.0;
        }
    }
    
    /**
     * @brief 转换为string类型
     * @return 字符串值
     * @details 根据当前类型进行适当的类型转换
     */
    std::string asString() const {
        switch (type) {
            case VariableType::STRING: return stringData ? *stringData : "";
            case VariableType::DOUBLE: return std::to_string(data.doubleValue);
            case VariableType::INT: return std::to_string(data.intValue);
            case VariableType::BOOL: return data.boolValue ? "true" : "false";
            default: return "";
        }
    }
    
    /**
     * @brief 转换为bool类型
     * @return 布尔值
     * @details 根据当前类型进行适当的类型转换
     */
    bool asBool() const {
        switch (type) {
            case VariableType::BOOL: return data.boolValue;
            case VariableType::DOUBLE: return data.doubleValue != 0.0;
            case VariableType::INT: return data.intValue != 0;
            case VariableType::STRING: return stringData && !stringData->empty();
            default: return false;
        }
    }
    
    /**
     * @brief 转换为int类型
     * @return 整数值
     * @details 根据当前类型进行适当的类型转换
     */
    int asInt() const {
        switch (type) {
            case VariableType::INT: return data.intValue;
            case VariableType::DOUBLE: return static_cast<int>(data.doubleValue);
            case VariableType::BOOL: return data.boolValue ? 1 : 0;
            case VariableType::STRING: return stringData ? std::stoi(*stringData) : 0;
            default: return 0;
        }
    }
    
    /**
     * @brief 设置单位
     * @param newUnit 新的单位字符串
     */
    void setUnit(const std::string& newUnit) {
        strncpy(unit.data(), newUnit.c_str(), unit.size() - 1);
        unit[unit.size() - 1] = '\0';  // 确保null终止
    }
    
    /**
     * @brief 获取单位
     * @return 单位字符串的常量引用
     */
    std::string getUnit() const {
        return std::string(unit.data());
    }
    
    /**
     * @brief 设置是否保存标志
     * @param save 是否保存到数据记录器
     */
    void setShouldSave(bool save) {
        should_save = save;
    }
    
    /**
     * @brief 获取是否保存标志
     * @return 是否保存到数据记录器
     */
    bool getShouldSave() const {
        return should_save;
    }
    
    /**
     * @brief 设置系数字段（有效小数位数）
     * @param scale 有效小数位数
     */
    void setScaleFactor(int scale) {
        scale_factor = scale;
    }
    
    /**
     * @brief 获取系数字段（有效小数位数）
     * @return 有效小数位数
     */
    int getScaleFactor() const {
        return scale_factor;
    }
    
    /**
     * @brief 获取类型名称
     * @return 类型名称字符串
     */
    std::string getTypeName() const {
        switch (type) {
            case VariableType::DOUBLE: return "double";
            case VariableType::STRING: return "string";
            case VariableType::BOOL: return "bool";
            case VariableType::INT: return "int";
            default: return "unknown";
        }
    }
    
    /**
     * @brief 转换为字符串表示
     * @return 变量值的字符串表示
     */
    std::string toString() const {
        switch (type) {
            case VariableType::DOUBLE: return std::to_string(data.doubleValue);
            case VariableType::STRING: return stringData ? *stringData : "";
            case VariableType::BOOL: return data.boolValue ? "true" : "false";
            case VariableType::INT: return std::to_string(data.intValue);
            default: return "";
        }
    }
};

#endif // VARIABLE_TYPES_HPP