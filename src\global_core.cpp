#include "global_core.hpp"
#include "global_variable_service.hpp"
#include "engine_variable_system.hpp"
#include "logger.hpp"
#include <iostream>
#include <thread>
#include <chrono>
#include <algorithm>
#include <numeric>
#include <sstream>
#include <iomanip>

#ifdef _WIN32
#include <windows.h>
#include <psapi.h>
#include <pdh.h>
#elif __linux__
#include <unistd.h>
#include <fstream>
#include <sys/sysinfo.h>
#endif

// 声明外部的异步回调函数
extern void async_invoke_global_core_state_change_callback(FSMState from, FSMState to, const std::string& event_data);

// 静态成员变量定义
GlobalCore& GlobalCore::get_instance() {
    static GlobalCore instance;
    return instance;
}

GlobalCore::GlobalCore()
    : m_monitor_interval(std::chrono::milliseconds(1000))  // 默认1秒间隔
    , m_cpu_usage(0.0)
    , m_memory_usage(0.0) {
    // 初始化Lua执行上下文
    m_execution_context = std::make_unique<LuaExecutionContext>();
    auto logger = Logger::get_root_logger();
    logger->log(Logging::LogLevel::INFO, "GlobalCore created");
}

GlobalCore::~GlobalCore() {
    stop();
    auto logger = Logger::get_root_logger();
    logger->log(Logging::LogLevel::INFO, "GlobalCore destroyed file " + std::string(__FILE__) + " line " + std::to_string(__LINE__));
}

ATE_EC GlobalCore::start() {
    if (m_state.load() == FSMState::RUNNING) {
        auto logger = Logger::get_root_logger();
        logger->log(Logging::LogLevel::WARN, "GlobalCore is already running file " + std::string(__FILE__) + " line " + std::to_string(__LINE__));
        return ATE_SUCCESS;
    }
    
    try {
        set_state(FSMState::RUNNING);
        m_monitor_thread = std::make_unique<std::thread>(&GlobalCore::monitor_thread_func, this);
        auto logger = Logger::get_root_logger();
        logger->log(Logging::LogLevel::INFO, "GlobalCore started successfully");
        return ATE_SUCCESS;
    } catch (const std::exception& e) {
        set_state(FSMState::ERROR_STATE);
        auto logger = Logger::get_root_logger();
        logger->log(Logging::LogLevel::ERROR, "Failed to start GlobalCore: " + std::string(e.what()));
        return ATE_ERROR_COMMAND_FAILED;
    }
}

ATE_EC GlobalCore::stop() {
    if (m_state.load() == FSMState::TERMINATED) {
        auto logger = Logger::get_root_logger();
        logger->log(Logging::LogLevel::WARN, "GlobalCore is already terminated");
        return ATE_SUCCESS;
    }
    
    try {
        set_state(FSMState::TERMINATED);
        
        if (m_monitor_thread && m_monitor_thread->joinable()) {
            m_monitor_thread->join();
            m_monitor_thread.reset();
        }
        
        auto logger = Logger::get_root_logger();
        logger->log(Logging::LogLevel::INFO, "GlobalCore stopped successfully");
        return ATE_SUCCESS;
    } catch (const std::exception& e) {
        set_state(FSMState::ERROR_STATE);
        auto logger = Logger::get_root_logger();
        logger->log(Logging::LogLevel::ERROR, "Failed to stop GlobalCore: " + std::string(e.what()));
        return ATE_ERROR_COMMAND_FAILED;
    }
}

FSMState GlobalCore::get_state() const {
    return m_state.load();
}

double GlobalCore::get_cpu_usage() const {
    return m_cpu_usage.load();
}

double GlobalCore::get_memory_usage() const {
    return m_memory_usage.load();
}

void GlobalCore::monitor_thread_func() {
    auto logger = Logger::get_root_logger();
    logger->log(Logging::LogLevel::INFO, "GlobalCore monitor thread started");
    
    while (m_state.load() == FSMState::RUNNING) {
        try {
            update_system_status();
            check_system_health();
            
            // 调用状态回调
            if (m_status_callback) {
                std::stringstream status_msg;
                status_msg << "CPU: " << std::fixed << std::setprecision(1) << m_cpu_usage.load() 
                          << "%, Memory: " << std::fixed << std::setprecision(1) << m_memory_usage.load() << "%";
                m_status_callback(status_msg.str());
            }
            
            std::this_thread::sleep_for(m_monitor_interval);
        } catch (const std::exception& e) {
            logger->log(Logging::LogLevel::ERROR, "Error in GlobalCore monitor thread: " + std::string(e.what()));
            set_state(FSMState::ERROR_STATE);
            break;
        }
    }
    
    logger->log(Logging::LogLevel::INFO, "GlobalCore monitor thread stopped");
}

void GlobalCore::update_system_status() {
    // 更新CPU使用率
#ifdef _WIN32
    static PDH_HQUERY cpuQuery;
    static PDH_HCOUNTER cpuTotal;
    static bool initialized = false;
    
    if (!initialized) {
        PdhOpenQuery(NULL, 0, &cpuQuery);
        PdhAddEnglishCounter(cpuQuery, "\\Processor(_Total)\\% Processor Time", NULL, &cpuTotal);
        PdhCollectQueryData(cpuQuery);
        initialized = true;
    }
    
    PDH_FMT_COUNTERVALUE counterVal;
    PdhCollectQueryData(cpuQuery);
    PdhGetFormattedCounterValue(cpuTotal, PDH_FMT_DOUBLE, NULL, &counterVal);
    m_cpu_usage.store(counterVal.doubleValue);
    
    // 更新内存使用率
    MEMORYSTATUSEX memInfo;
    memInfo.dwLength = sizeof(MEMORYSTATUSEX);
    GlobalMemoryStatusEx(&memInfo);
    double memory_usage = (double)(memInfo.ullTotalPhys - memInfo.ullAvailPhys) / memInfo.ullTotalPhys * 100.0;
    m_memory_usage.store(memory_usage);
    
#elif __linux__
    // Linux系统的CPU和内存使用率获取
    std::ifstream stat_file("/proc/stat");
    std::string line;
    if (std::getline(stat_file, line)) {
        std::istringstream iss(line);
        std::string cpu;
        long user, nice, system, idle;
        iss >> cpu >> user >> nice >> system >> idle;
        
        static long prev_idle = 0, prev_total = 0;
        long total = user + nice + system + idle;
        long total_diff = total - prev_total;
        long idle_diff = idle - prev_idle;
        
        if (total_diff > 0) {
            double cpu_usage = 100.0 * (total_diff - idle_diff) / total_diff;
            m_cpu_usage.store(cpu_usage);
        }
        
        prev_idle = idle;
        prev_total = total;
    }
    
    // 内存使用率
    std::ifstream meminfo("/proc/meminfo");
    long total_mem = 0, free_mem = 0, available_mem = 0;
    std::string key;
    long value;
    
    while (meminfo >> key >> value) {
        if (key == "MemTotal:") total_mem = value;
        else if (key == "MemFree:") free_mem = value;
        else if (key == "MemAvailable:") available_mem = value;
    }
    
    if (total_mem > 0) {
        double memory_usage = 100.0 * (total_mem - available_mem) / total_mem;
        m_memory_usage.store(memory_usage);
    }
#endif
}

void GlobalCore::check_system_health() {
    double cpu = m_cpu_usage.load();
    double memory = m_memory_usage.load();
    
    auto logger = Logger::get_root_logger();
    
    // 检查CPU使用率阈值
    if (cpu > m_config.cpu_threshold) {
        std::stringstream msg;
        msg << "High CPU usage detected: " << std::fixed << std::setprecision(1) << cpu 
            << "% (threshold: " << m_config.cpu_threshold << "%)";
        logger->log(Logging::LogLevel::WARN, msg.str());
    }
    
    // 检查内存使用率阈值
    if (memory > m_config.memory_threshold) {
        std::stringstream msg;
        msg << "High memory usage detected: " << std::fixed << std::setprecision(1) << memory 
            << "% (threshold: " << m_config.memory_threshold << "%)";
        logger->log(Logging::LogLevel::WARN, msg.str());
    }
}

ATE_EC GlobalCore::initialize(const GlobalCoreConfig& config) {
    if (m_state.load() != FSMState::TERMINATED) {
        auto logger = Logger::get_root_logger();
        logger->log(Logging::LogLevel::WARN, "GlobalCore is already initialized");
        return ATE_ERROR_ALREADY_INITIALIZED;
    }
    
    if (!config.is_valid()) {
        auto logger = Logger::get_root_logger();
        logger->log(Logging::LogLevel::ERROR, "Invalid GlobalCore configuration");
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    try {
        m_config = config;
        m_monitor_interval = std::chrono::milliseconds(config.update_frequency);
        
        // 初始化Lua状态机
        setup_lua_binding();
        
        set_state(FSMState::READY);
        
        auto logger = Logger::get_root_logger();
        logger->log(Logging::LogLevel::INFO, "GlobalCore initialized successfully with monitor name: " + config.monitor_name);
        return ATE_SUCCESS;
    } catch (const std::exception& e) {
        set_state(FSMState::ERROR_STATE);
        auto logger = Logger::get_root_logger();
        logger->log(Logging::LogLevel::ERROR, "Failed to initialize GlobalCore: " + std::string(e.what()));
        return ATE_ERROR_INITIALIZATION_FAILED;
    }
}

ATE_EC GlobalCore::suspend() {
    if (m_state.load() != FSMState::RUNNING) {
        auto logger = Logger::get_root_logger();
        logger->log(Logging::LogLevel::WARN, "GlobalCore is not running, cannot suspend");
        return ATE_ERROR_COMMAND_FAILED;
    }
    
    try {
        set_state(FSMState::PAUSED);
        auto logger = Logger::get_root_logger();
        logger->log(Logging::LogLevel::INFO, "GlobalCore suspended successfully");
        return ATE_SUCCESS;
    } catch (const std::exception& e) {
        set_state(FSMState::ERROR_STATE);
        auto logger = Logger::get_root_logger();
        logger->log(Logging::LogLevel::ERROR, "Failed to suspend GlobalCore: " + std::string(e.what()));
        return ATE_ERROR_COMMAND_FAILED;
    }
}

ATE_EC GlobalCore::resume() {
    if (m_state.load() != FSMState::PAUSED) {
        auto logger = Logger::get_root_logger();
        logger->log(Logging::LogLevel::WARN, "GlobalCore is not suspended, cannot resume");
        return ATE_ERROR_COMMAND_FAILED;
    }
    
    try {
        set_state(FSMState::RUNNING);
        auto logger = Logger::get_root_logger();
        logger->log(Logging::LogLevel::INFO, "GlobalCore resumed successfully");
        return ATE_SUCCESS;
    } catch (const std::exception& e) {
        set_state(FSMState::ERROR_STATE);
        auto logger = Logger::get_root_logger();
        logger->log(Logging::LogLevel::ERROR, "Failed to resume GlobalCore: " + std::string(e.what()));
        return ATE_ERROR_COMMAND_FAILED;
    }
}

ATE_EC GlobalCore::debug_script(const std::string& script_content) {
    auto logger = Logger::get_root_logger();
    logger->log(Logging::LogLevel::INFO, "GlobalCore debug script execution started");
    
    try {
        // 这里可以添加脚本执行逻辑
        // 目前只是记录日志
        logger->log(Logging::LogLevel::DEBUG, "Script content: " + script_content);
        
        logger->log(Logging::LogLevel::INFO, "GlobalCore debug script execution completed");
        return ATE_SUCCESS;
    } catch (const std::exception& e) {
        logger->log(Logging::LogLevel::ERROR, "Failed to execute debug script: " + std::string(e.what()));
        return ATE_ERROR_COMMAND_FAILED;
    }
}

ATE_EC GlobalCore::cleanup() {
    auto logger = Logger::get_root_logger();
    logger->log(Logging::LogLevel::INFO, "GlobalCore cleanup started");
    
    try {
        // 停止监控
        stop();
        
        // 重置状态
        set_state(FSMState::TERMINATED);
        
        // 清理回调函数
        m_status_callback = nullptr;
        
        // 重置配置
        m_config = GlobalCoreConfig{};
        
        logger->log(Logging::LogLevel::INFO, "GlobalCore cleanup completed");
        return ATE_SUCCESS;
    } catch (const std::exception& e) {
        logger->log(Logging::LogLevel::ERROR, "Failed to cleanup GlobalCore: " + std::string(e.what()));
        return ATE_ERROR_COMMAND_FAILED;
    }
}

ATE_EC GlobalCore::FSM_callback() {
    auto logger = Logger::get_root_logger();
    
    try {
        FSMState current_state = m_state.load();
        
        switch (current_state) {
            case FSMState::READY:
                logger->log(Logging::LogLevel::DEBUG, "GlobalCore FSM: READY state");
                break;
            case FSMState::RUNNING:
                logger->log(Logging::LogLevel::DEBUG, "GlobalCore FSM: RUNNING state");
                break;
            case FSMState::PAUSED:
                logger->log(Logging::LogLevel::DEBUG, "GlobalCore FSM: PAUSED state");
                break;
            case FSMState::DEBUGGING:
                logger->log(Logging::LogLevel::DEBUG, "GlobalCore FSM: DEBUGGING state");
                break;
            case FSMState::TERMINATED:
                logger->log(Logging::LogLevel::DEBUG, "GlobalCore FSM: TERMINATED state");
                break;
            case FSMState::ERROR_STATE:
                logger->log(Logging::LogLevel::ERROR, "GlobalCore FSM: ERROR_STATE");
                break;
            default:
                logger->log(Logging::LogLevel::WARN, "GlobalCore FSM: Unknown state");
                break;
        }
        
        return ATE_SUCCESS;
    } catch (const std::exception& e) {
        logger->log(Logging::LogLevel::ERROR, "Failed to execute GlobalCore FSM callback: " + std::string(e.what()));
        return ATE_ERROR_COMMAND_FAILED;
    }
}

void GlobalCore::set_state(FSMState new_state) {
    FSMState old_state = m_state.load();
    m_state.store(new_state);
    
    // 如果状态发生变化，触发回调
    if (old_state != new_state) {
        // 触发外部注册的回调函数（异步执行）
        async_invoke_global_core_state_change_callback(old_state, new_state, "");
    }
}

void GlobalCore::setup_lua_binding() {
    // 打开Lua标准库
    m_lua.open_libraries();
    
    // 获取全局变量服务实例
    auto& global_var_service = GlobalVariableService::get_instance();
    
    // 保存原始的全局表
    sol::table original_G = m_lua.globals();
    
    // 创建新的元表来实现自动同步
    sol::table global_meta = m_lua.create_table();
    
    // 重写__index元方法 - 从global_variable_service读取变量
    global_meta["__index"] = [&global_var_service, &original_G](sol::table t, const std::string& key) -> sol::object {
        // 首先检查原始全局表
        sol::object value = original_G.raw_get<sol::object>(key);
        if (value.valid() && value.get_type() != sol::type::lua_nil) {
            return value;
        }
        
        // 从global_variable_service获取变量
        if (global_var_service.has_variable(key)) {
            VariableValue var_value;
            if (global_var_service.get_variable(key, var_value) == ATE_SUCCESS) {
                // 根据变量类型转换为sol::object
                switch (var_value.type) {
                    case VariableType::DOUBLE:
                        return sol::make_object(t.lua_state(), var_value.data.doubleValue);
                    case VariableType::STRING:
                        return sol::make_object(t.lua_state(), var_value.stringData ? *var_value.stringData : std::string(""));
                    case VariableType::BOOL:
                        return sol::make_object(t.lua_state(), var_value.data.boolValue);
                    case VariableType::INT:
                        return sol::make_object(t.lua_state(), var_value.data.intValue);
                    default:
                        return sol::nil;
                }
            }
        }
        
        return sol::nil;
    };
    
    // 重写__newindex元方法 - 向global_variable_service写入变量
    global_meta["__newindex"] = [&global_var_service, &original_G](sol::table t, const std::string& key, sol::object value) {
        // 使用rawset避免递归调用元方法
        lua_State* L = t.lua_state();
        
        // 获取全局表
        lua_pushglobaltable(L);
        
        // 推入key和value
        lua_pushstring(L, key.c_str());
        sol::stack::push(L, value);
        
        // 使用rawset设置，避免触发元方法
        lua_rawset(L, -3);
        
        // 弹出全局表
        lua_pop(L, 1);
        
        // 将变量同步到global_variable_service
        try {
            VariableValue var_value;
            
            // 根据Lua值类型转换为VariableValue
            switch (value.get_type()) {
                case sol::type::number: {
                    double num_val = value.as<double>();
                    var_value.type = VariableType::DOUBLE;
                    var_value.data.doubleValue = num_val;
                    break;
                }
                case sol::type::string: {
                    std::string str_val = value.as<std::string>();
                    var_value.type = VariableType::STRING;
                    var_value.stringData = std::make_unique<std::string>(str_val);
                    break;
                }
                case sol::type::boolean: {
                    bool bool_val = value.as<bool>();
                    var_value.type = VariableType::BOOL;
                    var_value.data.boolValue = bool_val;
                    break;
                }
                default:
                    // 对于其他类型，尝试转换为字符串
                    var_value.type = VariableType::STRING;
                    var_value.stringData = std::make_unique<std::string>("unsupported_type");
                    break;
            }
            
            // 设置变量到global_variable_service
            global_var_service.set_variable(key, var_value);
            
            auto logger = Logger::get_root_logger();
            logger->log(Logging::LogLevel::INFO, "GlobalCore Lua: Synced variable '" + key + "' to global service");
            
        } catch (const std::exception& e) {
            auto logger = Logger::get_root_logger();
            logger->log(Logging::LogLevel::ERROR, "GlobalCore Lua: Error syncing variable '" + key + "': " + e.what());
        }
    };
    
    // 设置全局表的元表
    m_lua.globals().set(sol::metatable_key, global_meta);
    
    auto logger = Logger::get_root_logger();
    logger->log(Logging::LogLevel::INFO, "GlobalCore Lua binding setup completed. Variables will sync with GlobalVariableService.");
}

// ========================================================================
// Lua脚本执行控制接口实现
// ========================================================================

ATE_EC GlobalCore::start_script_execution(const std::string& script_content, 
                                          const std::string& script_name) {
    std::lock_guard<std::mutex> lock(m_execution_mutex);
    
    if (m_execution_context->state != LuaExecutionState::IDLE) {
        auto logger = Logger::get_root_logger();
        logger->log(Logging::LogLevel::WARN, "Script is already running or suspended");
        return ATE_ERROR_COMMAND_FAILED;
    }
    
    // 停止之前的执行线程
    if (m_execution_thread && m_execution_thread->joinable()) {
        m_stop_requested = true;
        m_execution_cv.notify_all();
        m_execution_thread->join();
    }
    
    // 重置状态
    m_stop_requested = false;
    m_suspend_requested = false;
    m_execution_context->state = LuaExecutionState::RUNNING;
    m_execution_context->script_type = LuaScriptType::MA_SCRIPT;
    m_execution_context->script_name = script_name;
    m_execution_context->current_line = 0;
    m_execution_context->last_error.clear();
    
    // 启动新的执行线程
    m_execution_thread = std::make_unique<std::thread>(
        &GlobalCore::script_execution_thread, this, script_content, script_name);
    
    auto logger = Logger::get_root_logger();
    logger->log(Logging::LogLevel::INFO, "Started script execution: " + script_name);
    
    return ATE_SUCCESS;
}

ATE_EC GlobalCore::stop_script_execution() {
    std::lock_guard<std::mutex> lock(m_execution_mutex);
    
    if (m_execution_context->state == LuaExecutionState::IDLE) {
        return ATE_SUCCESS;
    }
    
    m_stop_requested = true;
    m_execution_cv.notify_all();
    
    if (m_execution_thread && m_execution_thread->joinable()) {
        m_execution_thread->join();
    }
    
    m_execution_context->state = LuaExecutionState::IDLE;
    
    auto logger = Logger::get_root_logger();
    logger->log(Logging::LogLevel::INFO, "Stopped script execution");
    
    return ATE_SUCCESS;
}

ATE_EC GlobalCore::suspend_script_execution() {
    std::lock_guard<std::mutex> lock(m_execution_mutex);
    
    if (m_execution_context->state != LuaExecutionState::RUNNING) {
        return ATE_ERROR_COMMAND_FAILED;
    }
    
    m_suspend_requested = true;
    m_execution_context->state = LuaExecutionState::SUSPENDED;
    
    auto logger = Logger::get_root_logger();
    logger->log(Logging::LogLevel::INFO, "Suspended script execution");
    
    return ATE_SUCCESS;
}

ATE_EC GlobalCore::resume_script_execution() {
    std::lock_guard<std::mutex> lock(m_execution_mutex);
    
    if (m_execution_context->state != LuaExecutionState::SUSPENDED) {
        return ATE_ERROR_COMMAND_FAILED;
    }
    
    m_suspend_requested = false;
    m_execution_context->state = LuaExecutionState::RUNNING;
    m_execution_cv.notify_all();
    
    auto logger = Logger::get_root_logger();
    logger->log(Logging::LogLevel::INFO, "Resumed script execution");
    
    return ATE_SUCCESS;
}

ATE_EC GlobalCore::debug_script_to_line(int line_number) {
    DebugBreakpoint breakpoint(line_number, "global_script", true);
    
    return add_debug_breakpoint(breakpoint);
}

ATE_EC GlobalCore::add_debug_breakpoint(const DebugBreakpoint& breakpoint) {
    std::lock_guard<std::mutex> lock(m_breakpoints_mutex);
    
    m_breakpoints.insert(breakpoint);
    
    auto logger = Logger::get_root_logger();
    logger->log(Logging::LogLevel::INFO, "Added breakpoint at line " + std::to_string(breakpoint.line_number));
    
    return ATE_SUCCESS;
}

ATE_EC GlobalCore::remove_debug_breakpoint(int line_number) {
    std::lock_guard<std::mutex> lock(m_breakpoints_mutex);
    
    auto it = std::find_if(m_breakpoints.begin(), m_breakpoints.end(),
        [line_number](const DebugBreakpoint& bp) {
            return bp.line_number == line_number;
        });
    
    if (it != m_breakpoints.end()) {
        m_breakpoints.erase(it);
        
        auto logger = Logger::get_root_logger();
        logger->log(Logging::LogLevel::INFO, "Removed breakpoint at line " + std::to_string(line_number));
        
        return ATE_SUCCESS;
    }
    
    return ATE_ERROR_NOT_FOUND;
}

void GlobalCore::clear_all_breakpoints() {
    std::lock_guard<std::mutex> lock(m_breakpoints_mutex);
    
    m_breakpoints.clear();
    
    auto logger = Logger::get_root_logger();
    logger->log(Logging::LogLevel::INFO, "Cleared all breakpoints");
}

LuaExecutionState GlobalCore::get_script_execution_state() const {
    std::lock_guard<std::mutex> lock(m_execution_mutex);
    return m_execution_context->state;
}

LuaExecutionContext GlobalCore::get_execution_context() const {
    std::lock_guard<std::mutex> lock(m_execution_mutex);
    return *m_execution_context;
}

void GlobalCore::handle_lua_hook(lua_State* L, lua_Debug* ar) {
    // 获取GlobalCore实例
    GlobalCore& instance = GlobalCore::get_instance();
    instance.lua_hook_call(L, ar);
}

// ========================================================================
// Lua钩子函数相关私有方法实现
// ========================================================================

void GlobalCore::setup_lua_hook() {
    lua_State* L = m_lua.lua_state();
    lua_sethook(L, handle_lua_hook, LUA_MASKLINE | LUA_MASKCALL | LUA_MASKRET, 0);
    
    auto logger = Logger::get_root_logger();
    logger->log(Logging::LogLevel::INFO, "Lua hook setup completed");
}

void GlobalCore::script_execution_thread(const std::string& script_content, 
                                        const std::string& script_name) {
    try {
        // 设置Lua钩子
        setup_lua_hook();
        
        auto logger = Logger::get_root_logger();
        logger->log(Logging::LogLevel::INFO, "Executing script: " + script_name);
        
        // 执行脚本
        auto result = m_lua.safe_script(script_content);
        
        if (!result.valid()) {
            sol::error err = result;
            std::lock_guard<std::mutex> lock(m_execution_mutex);
            m_execution_context->last_error = err.what();
            m_execution_context->state = LuaExecutionState::ERROR_STATE;
            
            logger->log(Logging::LogLevel::ERROR, "Script execution error: " + std::string(err.what()));
        } else {
            std::lock_guard<std::mutex> lock(m_execution_mutex);
            m_execution_context->state = LuaExecutionState::IDLE;
            
            logger->log(Logging::LogLevel::INFO, "Script execution completed: " + script_name);
        }
    } catch (const std::exception& e) {
        std::lock_guard<std::mutex> lock(m_execution_mutex);
        m_execution_context->last_error = e.what();
        m_execution_context->state = LuaExecutionState::ERROR_STATE;
        
        auto logger = Logger::get_root_logger();
        logger->log(Logging::LogLevel::ERROR, "Script execution exception: " + std::string(e.what()));
    }
    
    // 清理钩子
    lua_State* L = m_lua.lua_state();
    lua_sethook(L, nullptr, 0, 0);
}

bool GlobalCore::should_break_at_line(int line_number) const {
    std::lock_guard<std::mutex> lock(m_breakpoints_mutex);
    
    auto it = std::find_if(m_breakpoints.begin(), m_breakpoints.end(),
        [line_number](const DebugBreakpoint& bp) {
            return bp.line_number == line_number && bp.enabled;
        });
    
    return it != m_breakpoints.end();
}

void GlobalCore::lua_hook_call(lua_State* lua_state, lua_Debug* debug_info) {
    // 获取GlobalCore实例
    GlobalCore& instance = GlobalCore::get_instance();
    
    if (debug_info->event == LUA_HOOKLINE) {
        // 更新当前行号
        {
            std::lock_guard<std::mutex> lock(instance.m_execution_mutex);
            instance.m_execution_context->current_line = debug_info->currentline;
        }
        
        // 检查是否需要停止
        if (instance.m_stop_requested) {
            luaL_error(lua_state, "Script execution stopped by user");
            return;
        }
        
        // 检查是否需要挂起
        if (instance.m_suspend_requested) {
            std::unique_lock<std::mutex> lock(instance.m_execution_mutex);
            instance.m_execution_cv.wait(lock, [&instance] { 
                return !instance.m_suspend_requested || instance.m_stop_requested; 
            });
            
            if (instance.m_stop_requested) {
                luaL_error(lua_state, "Script execution stopped by user");
                return;
            }
        }
        
        // 检查断点
        if (instance.should_break_at_line(debug_info->currentline)) {
            std::lock_guard<std::mutex> lock(instance.m_execution_mutex);
            instance.m_execution_context->state = LuaExecutionState::DEBUG_BREAK;
            
            auto logger = Logger::get_root_logger();
            logger->log(Logging::LogLevel::INFO, "Hit breakpoint at line " + std::to_string(debug_info->currentline));
            
            // 等待用户操作
            std::unique_lock<std::mutex> exec_lock(instance.m_execution_mutex);
            instance.m_execution_cv.wait(exec_lock, [&instance] { 
                return instance.m_execution_context->state != LuaExecutionState::DEBUG_BREAK || instance.m_stop_requested; 
            });
            
            if (instance.m_stop_requested) {
                luaL_error(lua_state, "Script execution stopped by user");
                return;
            }
        }
    }
}