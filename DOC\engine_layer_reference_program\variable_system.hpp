#pragma once

#include <unordered_map>
#include <string>
#include <memory>
#include <iostream>
#include <vector>
#include <mutex>

#include <sol/sol.hpp>

// 变量类型枚举
enum class VariableType {
    DOUBLE,
    STRING,
    BOOL,
    INT
};

// 固定大小的变量值结构体，包含单位信息
struct VariableValue {
    VariableType type;
    std::string unit;  // 变量单位
    bool should_save;  // 是否保存到数据记录器，默认不保存
    
    // 使用union存储基本类型，字符串使用共享指针单独存储
    union {
        double doubleValue;
        bool boolValue;
        int intValue;
    } data;
    
    std::shared_ptr<std::string> stringData;  // 动态字符串存储，支持任意长度和多国语言
    
    // 构造函数
    VariableValue() : type(VariableType::DOUBLE), unit(""), should_save(false) {
        data.doubleValue = 0.0;
    }
    
    VariableValue(double value, const std::string& unit = "") : type(VariableType::DOUBLE), unit(unit), should_save(false) {
        data.doubleValue = value;
    }
    
    VariableValue(const std::string& value, const std::string& unit = "") : type(VariableType::STRING), unit(unit), should_save(false) {
        stringData = std::make_shared<std::string>(value);
    }
    
    VariableValue(const char* value, const std::string& unit = "") : type(VariableType::STRING), unit(unit), should_save(false) {
        stringData = std::make_shared<std::string>(value);
    }
    
    VariableValue(bool value, const std::string& unit = "") : type(VariableType::BOOL), unit(unit), should_save(false) {
        data.boolValue = value;
    }
    
    VariableValue(int value, const std::string& unit = "") : type(VariableType::INT), unit(unit), should_save(false) {
        data.intValue = value;
    }
    
    // 拷贝构造函数
    VariableValue(const VariableValue& other) : type(other.type), unit(other.unit), should_save(other.should_save) {
        if (type == VariableType::STRING) {
            stringData = other.stringData;  // 共享字符串数据
        } else {
            data = other.data;
        }
    }
    
    // 赋值操作符
    VariableValue& operator=(const VariableValue& other) {
        if (this != &other) {
            type = other.type;
            unit = other.unit;
            should_save = other.should_save;
            if (type == VariableType::STRING) {
                stringData = other.stringData;  // 共享字符串数据
            } else {
                data = other.data;
            }
        }
        return *this;
    }
    
    // 获取值的方法
    double asDouble() const {
        switch (type) {
            case VariableType::DOUBLE: return data.doubleValue;
            case VariableType::INT: return static_cast<double>(data.intValue);
            case VariableType::BOOL: return data.boolValue ? 1.0 : 0.0;
            case VariableType::STRING: return stringData ? std::stod(*stringData) : 0.0;
            default: return 0.0;
        }
    }
    
    std::string asString() const {
        switch (type) {
            case VariableType::STRING: return stringData ? *stringData : "";
            case VariableType::DOUBLE: return std::to_string(data.doubleValue);
            case VariableType::INT: return std::to_string(data.intValue);
            case VariableType::BOOL: return data.boolValue ? "true" : "false";
            default: return "";
        }
    }
    
    bool asBool() const {
        switch (type) {
            case VariableType::BOOL: return data.boolValue;
            case VariableType::DOUBLE: return data.doubleValue != 0.0;
            case VariableType::INT: return data.intValue != 0;
            case VariableType::STRING: return stringData && !stringData->empty();
            default: return false;
        }
    }
    
    int asInt() const {
        switch (type) {
            case VariableType::INT: return data.intValue;
            case VariableType::DOUBLE: return static_cast<int>(data.doubleValue);
            case VariableType::BOOL: return data.boolValue ? 1 : 0;
            case VariableType::STRING: return stringData ? std::stoi(*stringData) : 0;
            default: return 0;
        }
    }
    
    // 设置单位
    void setUnit(const std::string& newUnit) {
        unit = newUnit;
    }
    
    // 获取单位
    const std::string& getUnit() const {
        return unit;
    }
    
    // 设置是否保存
    void setShouldSave(bool save) {
        should_save = save;
    }
    
    // 获取是否保存
    bool getShouldSave() const {
        return should_save;
    }
    
    // 获取类型名称
    std::string getTypeName() const {
        switch (type) {
            case VariableType::DOUBLE: return "double";
            case VariableType::STRING: return "string";
            case VariableType::BOOL: return "bool";
            case VariableType::INT: return "int";
            default: return "unknown";
        }
    }
    
    std::string toString() const {
        switch (type) {
            case VariableType::DOUBLE: return std::to_string(data.doubleValue);
            case VariableType::STRING: return stringData ? *stringData : "";
            case VariableType::BOOL: return data.boolValue ? "true" : "false";
            case VariableType::INT: return std::to_string(data.intValue);
            default: return "";
        }
    }
};



/**
 * C++和Lua之间的变量共享系统
 * 使用unordered_map存储变量，通过Sol2库实现透明访问
 */
class VariableSystem {
private:
    std::unordered_map<std::string, VariableValue> m_variables;
    sol::state m_lua;

    mutable std::mutex m_variables_mutex;

public:
    VariableSystem();
    ~VariableSystem() noexcept;

    // 禁用拷贝构造和拷贝赋值（不可拷贝）
    VariableSystem(const VariableSystem&) = delete;
    VariableSystem& operator=(const VariableSystem&) = delete;

    // 禁用移动构造和移动赋值（包含线程，不可移动）
    VariableSystem(VariableSystem&&) = delete;
    VariableSystem& operator=(VariableSystem&&) = delete;

    // 统一的变量设置接口（模板方法，自动推导类型）
    template<typename T>
    void set(const std::string& name, T&& value) {
        static_assert(std::is_same_v<std::decay_t<T>, double> ||
                     std::is_same_v<std::decay_t<T>, std::string> ||
                     std::is_same_v<std::decay_t<T>, bool> ||
                     std::is_same_v<std::decay_t<T>, int> ||
                     std::is_same_v<std::decay_t<T>, const char*>,
                     "Unsupported type for variable");

        if constexpr (std::is_same_v<std::decay_t<T>, const char*>) {
            set_variable(name, VariableValue(std::string(value)));
        } else {
            set_variable(name, VariableValue(std::forward<T>(value)));
        }
    }

    // 高性能的类型特化设置方法（避免variant构造开销）
    void set_number(const std::string& name, double value);
    void set_string(const std::string& name, const std::string& value);
    void set_bool(const std::string& name, bool value);
    void set_int(const std::string& name, int value);
    
    // 带单位的设置方法
    void set_number(const std::string& name, double value, const std::string& unit);
    void set_string(const std::string& name, const std::string& value, const std::string& unit);
    void set_bool(const std::string& name, bool value, const std::string& unit);
    void set_int(const std::string& name, int value, const std::string& unit);
    
    // 设置和获取变量单位的方法
    void set_variable_unit(const std::string& name, const std::string& unit);
    std::string get_variable_unit(const std::string& name) const;
    bool has_variable_unit(const std::string& name) const;
    
    // 设置和获取变量保存状态的方法
    void set_variable_save_status(const std::string& name, bool should_save);
    bool get_variable_save_status(const std::string& name) const;
    std::vector<std::string> get_saveable_variable_names() const;
    
    // DataRecorder专用接口
    bool set_variable_save_flag(const std::string& name, bool should_save);
    void* get_variable_data_pointer(const std::string& name);
    
    // 变量信息结构体
    struct VariableInfo {
        std::string name;
        std::string type;
        bool should_save;
        std::string unit;
        
        VariableInfo(const std::string& n, const std::string& t, bool save, const std::string& u)
            : name(n), type(t), should_save(save), unit(u) {}
    };
    
    // 获取所有变量信息列表
    std::vector<VariableInfo> list_variables() const;

    // 变量管理方法
    bool set_variable(const std::string& name, const VariableValue& value);
    VariableValue get_variable(const std::string& name) const;
    bool has_variable(const std::string& name) const;
    void remove_variable(const std::string& name);
    void clear_all_variables();

    // 统一的获取接口（模板方法，自动推导返回类型）
    template<typename T>
    T get(const std::string& name, const T& defaultValue = T{}) const {
        static_assert(std::is_same_v<T, double> ||
                     std::is_same_v<T, std::string> ||
                     std::is_same_v<T, bool> ||
                     std::is_same_v<T, int>,
                     "Unsupported type for variable retrieval");

        if (!has_variable(name)) {
            return defaultValue;
        }

        auto value = get_variable(name);
        if constexpr (std::is_same_v<T, double>) {
            return value.asDouble();
        }
        else if constexpr (std::is_same_v<T, std::string>) {
            return value.asString();
        }
        else if constexpr (std::is_same_v<T, bool>) {
            return value.asBool();
        }
        else if constexpr (std::is_same_v<T, int>) {
            return value.asInt();
        }
        return defaultValue;
    }

public:

    // 类型特化的获取方法（保留用于高性能场景）
    double get_number(const std::string& name, double default_value = 0.0) const;
    std::string get_string(const std::string& name, const std::string& default_value = "") const;
    bool get_bool(const std::string& name, bool default_value = false) const;
    int get_int(const std::string& name, int default_value = 0) const;

    // Lua绑定相关
    void setup_lua_binding();

    // 获取Lua状态
    sol::state& get_lua_state() { return m_lua; }
    const sol::state& get_lua_state() const { return m_lua; }

    // ATE自动化测试专用函数
    bool check_variable_conflict(const std::string& name) const;  // 检测变量系统中是否有同名变量
    sol::table check_lua_script_conflicts(const std::string& script_content);  // 检索Lua脚本中的全局变量并检测冲突

    // 调试和信息方法
    void print_all_variables() const;
    size_t get_variable_count() const noexcept;
    std::vector<std::string> get_variable_names() const;

private:
    // 内部辅助方法
    sol::object push_variable_to_sol(const VariableValue& value) const;
    VariableValue get_variable_from_sol(const sol::object& obj) const;
    std::string get_type_name(const VariableValue& value) const;
    
    // 用户脚本变量过滤函数
    bool is_user_script_variable(const std::string& variable_name) const;
    
    // Lua同步相关
    bool sync_lua_globals();
};
