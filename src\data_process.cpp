#include "data_process.hpp"
#include "global_variable_task.hpp"
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <iomanip>
#include <nlohmann/json.hpp>

DataProcess::DataProcess() {
    m_start_time = std::chrono::steady_clock::now();
}

DataProcess::~DataProcess() {
    shutdown();
}

bool DataProcess::initialize(const DataProcessing::ProcessConfig& config) {
    if (m_initialized) {
        add_error_message("DataProcess already initialized");
        return false;
    }

    try {
        m_config = config;
        m_status = DataProcessing::ProcessStatus::IDLE;
        m_should_stop = false;
        m_paused = false;
        
        // 重置统计信息
        m_input_count = 0;
        m_processed_count = 0;
        m_filtered_count = 0;
        m_error_count = 0;
        m_start_time = std::chrono::steady_clock::now();
        
        m_initialized = true;
        return true;
    }
    catch (const std::exception& e) {
        add_error_message("Failed to initialize DataProcess: " + std::string(e.what()));
        return false;
    }
}

void DataProcess::shutdown() {
    if (!m_initialized) {
        return;
    }

    // 停止处理
    stop_processing();
    
    // 等待所有工作线程结束
    for (auto& thread : m_worker_threads) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    m_worker_threads.clear();
    
    // 清理组件
    {
        std::lock_guard<std::mutex> lock(m_components_mutex);
        m_filters.clear();
        m_transformers.clear();
        m_validators.clear();
        m_aggregators.clear();
    }
    
    // 清理队列
    {
        std::lock_guard<std::mutex> input_lock(m_input_mutex);
        std::lock_guard<std::mutex> output_lock(m_output_mutex);
        std::queue<DataProcessing::DataItem> empty_input;
        std::queue<DataProcessing::DataItem> empty_output;
        m_input_queue.swap(empty_input);
        m_output_queue.swap(empty_output);
    }
    
    m_initialized = false;
    m_status = DataProcessing::ProcessStatus::IDLE;
}

void DataProcess::set_config(const DataProcessing::ProcessConfig& config) {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    m_config = config;
}

DataProcessing::ProcessConfig DataProcess::get_config() const {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    return m_config;
}

void DataProcess::update_config_setting(const std::string& key, const std::string& value) {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    m_config.custom_settings[key] = value;
}

bool DataProcess::add_data_item(const DataProcessing::DataItem& item) {
    if (!m_initialized) {
        add_error_message("DataProcess not initialized");
        return false;
    }

    {
        std::lock_guard<std::mutex> lock(m_input_mutex);
        if (m_input_queue.size() >= m_config.max_queue_size) {
            add_error_message("Input queue is full");
            return false;
        }
        m_input_queue.push(item);
        ++m_input_count;
    }
    
    m_input_cv.notify_one();
    return true;
}

bool DataProcess::add_data_batch(const DataProcessing::DataBatch& batch) {
    if (!m_initialized) {
        add_error_message("DataProcess not initialized");
        return false;
    }

    for (const auto& item : batch.items) {
        if (!add_data_item(item)) {
            return false;
        }
    }
    return true;
}

bool DataProcess::add_data_from_variable_system(GlobalVariableTask* var_system, const std::vector<std::string>& variable_names) {
    if (!m_initialized || !var_system) {
        add_error_message("DataProcess not initialized or invalid variable system");
        return false;
    }

    try {
        for (const auto& var_name : variable_names) {
            VariableValue var_value;
            ATE_EC result = var_system->get_variable(var_name, var_value);
            if (result == ATE_EC::ATE_SUCCESS) {
                DataProcessing::DataItem item;
                item.name = var_name;
                item.type = DataProcessing::DataType::STRING; // 简化处理，实际应根据变量类型确定
                item.value = var_value.asString();
                item.timestamp = std::chrono::system_clock::now();
                
                if (!add_data_item(item)) {
                    return false;
                }
            }
        }
        return true;
    }
    catch (const std::exception& e) {
        add_error_message("Failed to add data from variable system: " + std::string(e.what()));
        return false;
    }
}

bool DataProcess::start_processing() {
    if (!m_initialized) {
        add_error_message("DataProcess not initialized");
        return false;
    }

    if (m_status == DataProcessing::ProcessStatus::PROCESSING) {
        return true; // 已在处理中
    }

    try {
        m_should_stop = false;
        m_paused = false;
        m_status = DataProcessing::ProcessStatus::PROCESSING;
        
        // 启动工作线程
        for (int i = 0; i < m_config.worker_thread_count; ++i) {
            m_worker_threads.emplace_back(&DataProcess::worker_thread_function, this);
        }
        
        return true;
    }
    catch (const std::exception& e) {
        add_error_message("Failed to start processing: " + std::string(e.what()));
        m_status = DataProcessing::ProcessStatus::ERROR;
        return false;
    }
}

bool DataProcess::stop_processing() {
    if (m_status == DataProcessing::ProcessStatus::IDLE) {
        return true;
    }

    m_should_stop = true;
    m_paused = false;
    m_input_cv.notify_all();
    
    // 等待工作线程结束
    for (auto& thread : m_worker_threads) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    m_worker_threads.clear();
    
    m_status = DataProcessing::ProcessStatus::IDLE;
    return true;
}

bool DataProcess::pause_processing() {
    if (m_status != DataProcessing::ProcessStatus::PROCESSING) {
        return false;
    }
    
    m_paused = true;
    return true;
}

bool DataProcess::resume_processing() {
    if (m_status != DataProcessing::ProcessStatus::PROCESSING || !m_paused) {
        return false;
    }
    
    m_paused = false;
    m_input_cv.notify_all();
    return true;
}

void DataProcess::add_filter(std::unique_ptr<DataProcessing::IDataFilter> filter) {
    if (filter) {
        std::lock_guard<std::mutex> lock(m_components_mutex);
        m_filters.push_back(std::move(filter));
    }
}

void DataProcess::remove_filter(const std::string& filter_name) {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    m_filters.erase(
        std::remove_if(m_filters.begin(), m_filters.end(),
            [&filter_name](const auto& filter) {
                return filter->get_name() == filter_name;
            }),
        m_filters.end());
}

void DataProcess::clear_filters() {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    m_filters.clear();
}

std::vector<std::string> DataProcess::get_filter_names() const {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    std::vector<std::string> names;
    for (const auto& filter : m_filters) {
        names.push_back(filter->get_name());
    }
    return names;
}

void DataProcess::add_transformer(std::unique_ptr<DataProcessing::IDataTransformer> transformer) {
    if (transformer) {
        std::lock_guard<std::mutex> lock(m_components_mutex);
        m_transformers.push_back(std::move(transformer));
    }
}

void DataProcess::remove_transformer(const std::string& transformer_name) {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    m_transformers.erase(
        std::remove_if(m_transformers.begin(), m_transformers.end(),
            [&transformer_name](const auto& transformer) {
                return transformer->get_name() == transformer_name;
            }),
        m_transformers.end());
}

void DataProcess::clear_transformers() {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    m_transformers.clear();
}

std::vector<std::string> DataProcess::get_transformer_names() const {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    std::vector<std::string> names;
    for (const auto& transformer : m_transformers) {
        names.push_back(transformer->get_name());
    }
    return names;
}

void DataProcess::add_validator(std::unique_ptr<DataProcessing::IDataValidator> validator) {
    if (validator) {
        std::lock_guard<std::mutex> lock(m_components_mutex);
        m_validators.push_back(std::move(validator));
    }
}

void DataProcess::remove_validator(const std::string& validator_name) {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    m_validators.erase(
        std::remove_if(m_validators.begin(), m_validators.end(),
            [&validator_name](const auto& validator) {
                return validator->get_name() == validator_name;
            }),
        m_validators.end());
}

void DataProcess::clear_validators() {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    m_validators.clear();
}

std::vector<std::string> DataProcess::get_validator_names() const {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    std::vector<std::string> names;
    for (const auto& validator : m_validators) {
        names.push_back(validator->get_name());
    }
    return names;
}

void DataProcess::add_aggregator(std::unique_ptr<DataProcessing::IDataAggregator> aggregator) {
    if (aggregator) {
        std::lock_guard<std::mutex> lock(m_components_mutex);
        m_aggregators.push_back(std::move(aggregator));
    }
}

void DataProcess::remove_aggregator(const std::string& aggregator_name) {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    m_aggregators.erase(
        std::remove_if(m_aggregators.begin(), m_aggregators.end(),
            [&aggregator_name](const auto& aggregator) {
                return aggregator->get_name() == aggregator_name;
            }),
        m_aggregators.end());
}

void DataProcess::clear_aggregators() {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    m_aggregators.clear();
}

std::vector<std::string> DataProcess::get_aggregator_names() const {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    std::vector<std::string> names;
    for (const auto& aggregator : m_aggregators) {
        names.push_back(aggregator->get_name());
    }
    return names;
}

std::vector<DataProcessing::DataItem> DataProcess::get_processed_data() {
    std::lock_guard<std::mutex> lock(m_output_mutex);
    std::vector<DataProcessing::DataItem> result;
    
    while (!m_output_queue.empty()) {
        result.push_back(m_output_queue.front());
        m_output_queue.pop();
    }
    
    return result;
}

std::vector<DataProcessing::DataBatch> DataProcess::get_processed_batches() {
    // 简化实现，将输出队列中的数据组织成批次
    auto items = get_processed_data();
    std::vector<DataProcessing::DataBatch> batches;
    
    if (!items.empty()) {
        DataProcessing::DataBatch batch("batch_" + std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count()));
        batch.items = std::move(items);
        batches.push_back(std::move(batch));
    }
    
    return batches;
}

bool DataProcess::export_results(const std::string& file_path, const std::string& format) {
    try {
        auto data = get_processed_data();
        
        if (format == "json") {
            nlohmann::json root = nlohmann::json::array();
            
            for (const auto& item : data) {
                nlohmann::json json_item;
                json_item["name"] = item.name;
                json_item["type"] = static_cast<int>(item.type);
                json_item["value"] = item.value;
                
                auto time_t = std::chrono::system_clock::to_time_t(item.timestamp);
                std::stringstream ss;
                ss << std::put_time(std::gmtime(&time_t), "%Y-%m-%dT%H:%M:%SZ");
                json_item["timestamp"] = ss.str();
                
                nlohmann::json metadata;
                for (const auto& [key, value] : item.metadata) {
                    metadata[key] = value;
                }
                json_item["metadata"] = metadata;
                
                root.push_back(json_item);
            }
            
            std::ofstream file(file_path);
            if (!file.is_open()) {
                add_error_message("Failed to open file for writing: " + file_path);
                return false;
            }
            
            file << root.dump(2);
            
            return true;
        }
        else if (format == "csv") {
            std::ofstream file(file_path);
            if (!file.is_open()) {
                add_error_message("Failed to open file for writing: " + file_path);
                return false;
            }
            
            // CSV 头部
            file << "Name,Type,Value,Timestamp\n";
            
            for (const auto& item : data) {
                auto time_t = std::chrono::system_clock::to_time_t(item.timestamp);
                std::stringstream ss;
                ss << std::put_time(std::gmtime(&time_t), "%Y-%m-%dT%H:%M:%SZ");
                
                file << item.name << "," << static_cast<int>(item.type) << "," 
                     << item.value << "," << ss.str() << "\n";
            }
            
            return true;
        }
        else {
            add_error_message("Unsupported export format: " + format);
            return false;
        }
    }
    catch (const std::exception& e) {
        add_error_message("Failed to export results: " + std::string(e.what()));
        return false;
    }
}

size_t DataProcess::get_queue_size() const {
    std::lock_guard<std::mutex> lock(m_input_mutex);
    return m_input_queue.size();
}

double DataProcess::get_processing_rate() const {
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - m_start_time);
    
    if (duration.count() == 0) {
        return 0.0;
    }
    
    return static_cast<double>(m_processed_count) / duration.count();
}

std::vector<std::string> DataProcess::get_error_messages() const {
    std::lock_guard<std::mutex> lock(m_error_mutex);
    return m_error_messages;
}

void DataProcess::clear_error_messages() {
    std::lock_guard<std::mutex> lock(m_error_mutex);
    m_error_messages.clear();
}

void DataProcess::set_data_processed_callback(DataProcessedCallback callback) {
    std::lock_guard<std::mutex> lock(m_callback_mutex);
    m_data_processed_callback = callback;
}

void DataProcess::set_batch_processed_callback(BatchProcessedCallback callback) {
    std::lock_guard<std::mutex> lock(m_callback_mutex);
    m_batch_processed_callback = callback;
}

void DataProcess::set_error_callback(ErrorCallback callback) {
    std::lock_guard<std::mutex> lock(m_callback_mutex);
    m_error_callback = callback;
}

void DataProcess::worker_thread_function() {
    while (!m_should_stop) {
        DataProcessing::DataItem item;
        bool has_item = false;
        
        // 获取待处理的数据项
        {
            std::unique_lock<std::mutex> lock(m_input_mutex);
            m_input_cv.wait(lock, [this] { return !m_input_queue.empty() || m_should_stop; });
            
            if (m_should_stop) {
                break;
            }
            
            if (!m_input_queue.empty()) {
                item = m_input_queue.front();
                m_input_queue.pop();
                has_item = true;
            }
        }
        
        if (has_item) {
            // 等待暂停状态结束
            while (m_paused && !m_should_stop) {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
            
            if (m_should_stop) {
                break;
            }
            
            // 处理数据项
            if (process_data_item(item)) {
                // 将处理后的数据放入输出队列
                {
                    std::lock_guard<std::mutex> lock(m_output_mutex);
                    m_output_queue.push(item);
                }
                
                ++m_processed_count;
                notify_data_processed(item);
            }
        }
    }
}

bool DataProcess::process_data_item(DataProcessing::DataItem& item) {
    try {
        // 应用过滤器
        if (m_config.enable_filtering && !apply_filters(item)) {
            ++m_filtered_count;
            return false;
        }
        
        // 应用验证器
        if (m_config.enable_validation && !apply_validators(item)) {
            ++m_error_count;
            return false;
        }
        
        // 应用转换器
        if (m_config.enable_transformation) {
            item = apply_transformers(item);
        }
        
        // 应用聚合器
        if (m_config.enable_aggregation) {
            apply_aggregators(item);
        }
        
        return true;
    }
    catch (const std::exception& e) {
        add_error_message("Error processing data item '" + item.name + "': " + e.what());
        ++m_error_count;
        return false;
    }
}

bool DataProcess::apply_filters(const DataProcessing::DataItem& item) {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    
    for (const auto& filter : m_filters) {
        if (!filter->filter(item)) {
            return false;
        }
    }
    return true;
}

DataProcessing::DataItem DataProcess::apply_transformers(const DataProcessing::DataItem& item) {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    
    DataProcessing::DataItem result = item;
    for (const auto& transformer : m_transformers) {
        result = transformer->transform(result);
    }
    return result;
}

bool DataProcess::apply_validators(const DataProcessing::DataItem& item) {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    
    for (const auto& validator : m_validators) {
        if (!validator->validate(item)) {
            add_error_message("Validation failed for item '" + item.name + "': " + validator->get_error_message());
            return false;
        }
    }
    return true;
}

void DataProcess::apply_aggregators(const DataProcessing::DataItem& item) {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    
    for (const auto& aggregator : m_aggregators) {
        aggregator->add_data(item);
    }
}

void DataProcess::add_error_message(const std::string& message) {
    {
        std::lock_guard<std::mutex> lock(m_error_mutex);
        m_error_messages.push_back(message);
    }
    
    notify_error(message);
}

void DataProcess::notify_data_processed(const DataProcessing::DataItem& item) {
    std::lock_guard<std::mutex> lock(m_callback_mutex);
    if (m_data_processed_callback) {
        try {
            m_data_processed_callback(item);
        }
        catch (const std::exception& e) {
            // 回调函数异常不应影响主处理流程
            std::cerr << "Error in data processed callback: " << e.what() << std::endl;
        }
    }
}

void DataProcess::notify_error(const std::string& message) {
    std::lock_guard<std::mutex> lock(m_callback_mutex);
    if (m_error_callback) {
        try {
            m_error_callback(message);
        }
        catch (const std::exception& e) {
            // 回调函数异常不应影响主处理流程
            std::cerr << "Error in error callback: " << e.what() << std::endl;
        }
    }
}