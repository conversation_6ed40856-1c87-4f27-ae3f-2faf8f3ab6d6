# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\mingw64\bin\cmake.exe

# The command to remove a file.
RM = C:\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\KWL-Software\EVT\EngineFrameworkInterface

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\KWL-Software\EVT\EngineFrameworkInterface\build

# Include any dependencies generated for this target.
include CMakeFiles/EngineFrameworkInterface.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/EngineFrameworkInterface.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/EngineFrameworkInterface.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/EngineFrameworkInterface.dir/flags.make

CMakeFiles/EngineFrameworkInterface.dir/codegen:
.PHONY : CMakeFiles/EngineFrameworkInterface.dir/codegen

CMakeFiles/EngineFrameworkInterface.dir/src/data_process.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/flags.make
CMakeFiles/EngineFrameworkInterface.dir/src/data_process.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp
CMakeFiles/EngineFrameworkInterface.dir/src/data_process.cpp.obj: E:/KWL-Software/EVT/EngineFrameworkInterface/src/data_process.cpp
CMakeFiles/EngineFrameworkInterface.dir/src/data_process.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\KWL-Software\EVT\EngineFrameworkInterface\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/EngineFrameworkInterface.dir/src/data_process.cpp.obj"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/EngineFrameworkInterface.dir/src/data_process.cpp.obj -MF CMakeFiles\EngineFrameworkInterface.dir\src\data_process.cpp.obj.d -o CMakeFiles\EngineFrameworkInterface.dir\src\data_process.cpp.obj -c E:\KWL-Software\EVT\EngineFrameworkInterface\src\data_process.cpp

CMakeFiles/EngineFrameworkInterface.dir/src/data_process.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/EngineFrameworkInterface.dir/src/data_process.cpp.i"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\KWL-Software\EVT\EngineFrameworkInterface\src\data_process.cpp > CMakeFiles\EngineFrameworkInterface.dir\src\data_process.cpp.i

CMakeFiles/EngineFrameworkInterface.dir/src/data_process.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/EngineFrameworkInterface.dir/src/data_process.cpp.s"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\KWL-Software\EVT\EngineFrameworkInterface\src\data_process.cpp -o CMakeFiles\EngineFrameworkInterface.dir\src\data_process.cpp.s

CMakeFiles/EngineFrameworkInterface.dir/src/data_recorder.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/flags.make
CMakeFiles/EngineFrameworkInterface.dir/src/data_recorder.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp
CMakeFiles/EngineFrameworkInterface.dir/src/data_recorder.cpp.obj: E:/KWL-Software/EVT/EngineFrameworkInterface/src/data_recorder.cpp
CMakeFiles/EngineFrameworkInterface.dir/src/data_recorder.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\KWL-Software\EVT\EngineFrameworkInterface\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/EngineFrameworkInterface.dir/src/data_recorder.cpp.obj"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/EngineFrameworkInterface.dir/src/data_recorder.cpp.obj -MF CMakeFiles\EngineFrameworkInterface.dir\src\data_recorder.cpp.obj.d -o CMakeFiles\EngineFrameworkInterface.dir\src\data_recorder.cpp.obj -c E:\KWL-Software\EVT\EngineFrameworkInterface\src\data_recorder.cpp

CMakeFiles/EngineFrameworkInterface.dir/src/data_recorder.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/EngineFrameworkInterface.dir/src/data_recorder.cpp.i"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\KWL-Software\EVT\EngineFrameworkInterface\src\data_recorder.cpp > CMakeFiles\EngineFrameworkInterface.dir\src\data_recorder.cpp.i

CMakeFiles/EngineFrameworkInterface.dir/src/data_recorder.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/EngineFrameworkInterface.dir/src/data_recorder.cpp.s"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\KWL-Software\EVT\EngineFrameworkInterface\src\data_recorder.cpp -o CMakeFiles\EngineFrameworkInterface.dir\src\data_recorder.cpp.s

CMakeFiles/EngineFrameworkInterface.dir/src/device_manager.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/flags.make
CMakeFiles/EngineFrameworkInterface.dir/src/device_manager.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp
CMakeFiles/EngineFrameworkInterface.dir/src/device_manager.cpp.obj: E:/KWL-Software/EVT/EngineFrameworkInterface/src/device_manager.cpp
CMakeFiles/EngineFrameworkInterface.dir/src/device_manager.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\KWL-Software\EVT\EngineFrameworkInterface\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/EngineFrameworkInterface.dir/src/device_manager.cpp.obj"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/EngineFrameworkInterface.dir/src/device_manager.cpp.obj -MF CMakeFiles\EngineFrameworkInterface.dir\src\device_manager.cpp.obj.d -o CMakeFiles\EngineFrameworkInterface.dir\src\device_manager.cpp.obj -c E:\KWL-Software\EVT\EngineFrameworkInterface\src\device_manager.cpp

CMakeFiles/EngineFrameworkInterface.dir/src/device_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/EngineFrameworkInterface.dir/src/device_manager.cpp.i"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\KWL-Software\EVT\EngineFrameworkInterface\src\device_manager.cpp > CMakeFiles\EngineFrameworkInterface.dir\src\device_manager.cpp.i

CMakeFiles/EngineFrameworkInterface.dir/src/device_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/EngineFrameworkInterface.dir/src/device_manager.cpp.s"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\KWL-Software\EVT\EngineFrameworkInterface\src\device_manager.cpp -o CMakeFiles\EngineFrameworkInterface.dir\src\device_manager.cpp.s

CMakeFiles/EngineFrameworkInterface.dir/src/engine_core.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/flags.make
CMakeFiles/EngineFrameworkInterface.dir/src/engine_core.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp
CMakeFiles/EngineFrameworkInterface.dir/src/engine_core.cpp.obj: E:/KWL-Software/EVT/EngineFrameworkInterface/src/engine_core.cpp
CMakeFiles/EngineFrameworkInterface.dir/src/engine_core.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\KWL-Software\EVT\EngineFrameworkInterface\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/EngineFrameworkInterface.dir/src/engine_core.cpp.obj"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/EngineFrameworkInterface.dir/src/engine_core.cpp.obj -MF CMakeFiles\EngineFrameworkInterface.dir\src\engine_core.cpp.obj.d -o CMakeFiles\EngineFrameworkInterface.dir\src\engine_core.cpp.obj -c E:\KWL-Software\EVT\EngineFrameworkInterface\src\engine_core.cpp

CMakeFiles/EngineFrameworkInterface.dir/src/engine_core.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/EngineFrameworkInterface.dir/src/engine_core.cpp.i"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\KWL-Software\EVT\EngineFrameworkInterface\src\engine_core.cpp > CMakeFiles\EngineFrameworkInterface.dir\src\engine_core.cpp.i

CMakeFiles/EngineFrameworkInterface.dir/src/engine_core.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/EngineFrameworkInterface.dir/src/engine_core.cpp.s"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\KWL-Software\EVT\EngineFrameworkInterface\src\engine_core.cpp -o CMakeFiles\EngineFrameworkInterface.dir\src\engine_core.cpp.s

CMakeFiles/EngineFrameworkInterface.dir/src/engine_framework_interface.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/flags.make
CMakeFiles/EngineFrameworkInterface.dir/src/engine_framework_interface.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp
CMakeFiles/EngineFrameworkInterface.dir/src/engine_framework_interface.cpp.obj: E:/KWL-Software/EVT/EngineFrameworkInterface/src/engine_framework_interface.cpp
CMakeFiles/EngineFrameworkInterface.dir/src/engine_framework_interface.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\KWL-Software\EVT\EngineFrameworkInterface\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/EngineFrameworkInterface.dir/src/engine_framework_interface.cpp.obj"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/EngineFrameworkInterface.dir/src/engine_framework_interface.cpp.obj -MF CMakeFiles\EngineFrameworkInterface.dir\src\engine_framework_interface.cpp.obj.d -o CMakeFiles\EngineFrameworkInterface.dir\src\engine_framework_interface.cpp.obj -c E:\KWL-Software\EVT\EngineFrameworkInterface\src\engine_framework_interface.cpp

CMakeFiles/EngineFrameworkInterface.dir/src/engine_framework_interface.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/EngineFrameworkInterface.dir/src/engine_framework_interface.cpp.i"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\KWL-Software\EVT\EngineFrameworkInterface\src\engine_framework_interface.cpp > CMakeFiles\EngineFrameworkInterface.dir\src\engine_framework_interface.cpp.i

CMakeFiles/EngineFrameworkInterface.dir/src/engine_framework_interface.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/EngineFrameworkInterface.dir/src/engine_framework_interface.cpp.s"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\KWL-Software\EVT\EngineFrameworkInterface\src\engine_framework_interface.cpp -o CMakeFiles\EngineFrameworkInterface.dir\src\engine_framework_interface.cpp.s

CMakeFiles/EngineFrameworkInterface.dir/src/engine_manager.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/flags.make
CMakeFiles/EngineFrameworkInterface.dir/src/engine_manager.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp
CMakeFiles/EngineFrameworkInterface.dir/src/engine_manager.cpp.obj: E:/KWL-Software/EVT/EngineFrameworkInterface/src/engine_manager.cpp
CMakeFiles/EngineFrameworkInterface.dir/src/engine_manager.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\KWL-Software\EVT\EngineFrameworkInterface\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/EngineFrameworkInterface.dir/src/engine_manager.cpp.obj"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/EngineFrameworkInterface.dir/src/engine_manager.cpp.obj -MF CMakeFiles\EngineFrameworkInterface.dir\src\engine_manager.cpp.obj.d -o CMakeFiles\EngineFrameworkInterface.dir\src\engine_manager.cpp.obj -c E:\KWL-Software\EVT\EngineFrameworkInterface\src\engine_manager.cpp

CMakeFiles/EngineFrameworkInterface.dir/src/engine_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/EngineFrameworkInterface.dir/src/engine_manager.cpp.i"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\KWL-Software\EVT\EngineFrameworkInterface\src\engine_manager.cpp > CMakeFiles\EngineFrameworkInterface.dir\src\engine_manager.cpp.i

CMakeFiles/EngineFrameworkInterface.dir/src/engine_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/EngineFrameworkInterface.dir/src/engine_manager.cpp.s"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\KWL-Software\EVT\EngineFrameworkInterface\src\engine_manager.cpp -o CMakeFiles\EngineFrameworkInterface.dir\src\engine_manager.cpp.s

CMakeFiles/EngineFrameworkInterface.dir/src/engine_variable_system.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/flags.make
CMakeFiles/EngineFrameworkInterface.dir/src/engine_variable_system.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp
CMakeFiles/EngineFrameworkInterface.dir/src/engine_variable_system.cpp.obj: E:/KWL-Software/EVT/EngineFrameworkInterface/src/engine_variable_system.cpp
CMakeFiles/EngineFrameworkInterface.dir/src/engine_variable_system.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\KWL-Software\EVT\EngineFrameworkInterface\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/EngineFrameworkInterface.dir/src/engine_variable_system.cpp.obj"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/EngineFrameworkInterface.dir/src/engine_variable_system.cpp.obj -MF CMakeFiles\EngineFrameworkInterface.dir\src\engine_variable_system.cpp.obj.d -o CMakeFiles\EngineFrameworkInterface.dir\src\engine_variable_system.cpp.obj -c E:\KWL-Software\EVT\EngineFrameworkInterface\src\engine_variable_system.cpp

CMakeFiles/EngineFrameworkInterface.dir/src/engine_variable_system.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/EngineFrameworkInterface.dir/src/engine_variable_system.cpp.i"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\KWL-Software\EVT\EngineFrameworkInterface\src\engine_variable_system.cpp > CMakeFiles\EngineFrameworkInterface.dir\src\engine_variable_system.cpp.i

CMakeFiles/EngineFrameworkInterface.dir/src/engine_variable_system.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/EngineFrameworkInterface.dir/src/engine_variable_system.cpp.s"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\KWL-Software\EVT\EngineFrameworkInterface\src\engine_variable_system.cpp -o CMakeFiles\EngineFrameworkInterface.dir\src\engine_variable_system.cpp.s

CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_task.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/flags.make
CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_task.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp
CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_task.cpp.obj: E:/KWL-Software/EVT/EngineFrameworkInterface/src/global_variable_task.cpp
CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_task.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\KWL-Software\EVT\EngineFrameworkInterface\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_task.cpp.obj"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_task.cpp.obj -MF CMakeFiles\EngineFrameworkInterface.dir\src\global_variable_task.cpp.obj.d -o CMakeFiles\EngineFrameworkInterface.dir\src\global_variable_task.cpp.obj -c E:\KWL-Software\EVT\EngineFrameworkInterface\src\global_variable_task.cpp

CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_task.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_task.cpp.i"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\KWL-Software\EVT\EngineFrameworkInterface\src\global_variable_task.cpp > CMakeFiles\EngineFrameworkInterface.dir\src\global_variable_task.cpp.i

CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_task.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_task.cpp.s"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\KWL-Software\EVT\EngineFrameworkInterface\src\global_variable_task.cpp -o CMakeFiles\EngineFrameworkInterface.dir\src\global_variable_task.cpp.s

CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_service.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/flags.make
CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_service.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp
CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_service.cpp.obj: E:/KWL-Software/EVT/EngineFrameworkInterface/src/global_variable_service.cpp
CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_service.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\KWL-Software\EVT\EngineFrameworkInterface\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_service.cpp.obj"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_service.cpp.obj -MF CMakeFiles\EngineFrameworkInterface.dir\src\global_variable_service.cpp.obj.d -o CMakeFiles\EngineFrameworkInterface.dir\src\global_variable_service.cpp.obj -c E:\KWL-Software\EVT\EngineFrameworkInterface\src\global_variable_service.cpp

CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_service.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_service.cpp.i"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\KWL-Software\EVT\EngineFrameworkInterface\src\global_variable_service.cpp > CMakeFiles\EngineFrameworkInterface.dir\src\global_variable_service.cpp.i

CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_service.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_service.cpp.s"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\KWL-Software\EVT\EngineFrameworkInterface\src\global_variable_service.cpp -o CMakeFiles\EngineFrameworkInterface.dir\src\global_variable_service.cpp.s

CMakeFiles/EngineFrameworkInterface.dir/src/logger.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/flags.make
CMakeFiles/EngineFrameworkInterface.dir/src/logger.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp
CMakeFiles/EngineFrameworkInterface.dir/src/logger.cpp.obj: E:/KWL-Software/EVT/EngineFrameworkInterface/src/logger.cpp
CMakeFiles/EngineFrameworkInterface.dir/src/logger.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\KWL-Software\EVT\EngineFrameworkInterface\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/EngineFrameworkInterface.dir/src/logger.cpp.obj"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/EngineFrameworkInterface.dir/src/logger.cpp.obj -MF CMakeFiles\EngineFrameworkInterface.dir\src\logger.cpp.obj.d -o CMakeFiles\EngineFrameworkInterface.dir\src\logger.cpp.obj -c E:\KWL-Software\EVT\EngineFrameworkInterface\src\logger.cpp

CMakeFiles/EngineFrameworkInterface.dir/src/logger.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/EngineFrameworkInterface.dir/src/logger.cpp.i"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\KWL-Software\EVT\EngineFrameworkInterface\src\logger.cpp > CMakeFiles\EngineFrameworkInterface.dir\src\logger.cpp.i

CMakeFiles/EngineFrameworkInterface.dir/src/logger.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/EngineFrameworkInterface.dir/src/logger.cpp.s"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\KWL-Software\EVT\EngineFrameworkInterface\src\logger.cpp -o CMakeFiles\EngineFrameworkInterface.dir\src\logger.cpp.s

CMakeFiles/EngineFrameworkInterface.dir/src/report_process.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/flags.make
CMakeFiles/EngineFrameworkInterface.dir/src/report_process.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp
CMakeFiles/EngineFrameworkInterface.dir/src/report_process.cpp.obj: E:/KWL-Software/EVT/EngineFrameworkInterface/src/report_process.cpp
CMakeFiles/EngineFrameworkInterface.dir/src/report_process.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\KWL-Software\EVT\EngineFrameworkInterface\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/EngineFrameworkInterface.dir/src/report_process.cpp.obj"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/EngineFrameworkInterface.dir/src/report_process.cpp.obj -MF CMakeFiles\EngineFrameworkInterface.dir\src\report_process.cpp.obj.d -o CMakeFiles\EngineFrameworkInterface.dir\src\report_process.cpp.obj -c E:\KWL-Software\EVT\EngineFrameworkInterface\src\report_process.cpp

CMakeFiles/EngineFrameworkInterface.dir/src/report_process.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/EngineFrameworkInterface.dir/src/report_process.cpp.i"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\KWL-Software\EVT\EngineFrameworkInterface\src\report_process.cpp > CMakeFiles\EngineFrameworkInterface.dir\src\report_process.cpp.i

CMakeFiles/EngineFrameworkInterface.dir/src/report_process.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/EngineFrameworkInterface.dir/src/report_process.cpp.s"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\KWL-Software\EVT\EngineFrameworkInterface\src\report_process.cpp -o CMakeFiles\EngineFrameworkInterface.dir\src\report_process.cpp.s

CMakeFiles/EngineFrameworkInterface.dir/src/global_core.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/flags.make
CMakeFiles/EngineFrameworkInterface.dir/src/global_core.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp
CMakeFiles/EngineFrameworkInterface.dir/src/global_core.cpp.obj: E:/KWL-Software/EVT/EngineFrameworkInterface/src/global_core.cpp
CMakeFiles/EngineFrameworkInterface.dir/src/global_core.cpp.obj: CMakeFiles/EngineFrameworkInterface.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\KWL-Software\EVT\EngineFrameworkInterface\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object CMakeFiles/EngineFrameworkInterface.dir/src/global_core.cpp.obj"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/EngineFrameworkInterface.dir/src/global_core.cpp.obj -MF CMakeFiles\EngineFrameworkInterface.dir\src\global_core.cpp.obj.d -o CMakeFiles\EngineFrameworkInterface.dir\src\global_core.cpp.obj -c E:\KWL-Software\EVT\EngineFrameworkInterface\src\global_core.cpp

CMakeFiles/EngineFrameworkInterface.dir/src/global_core.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/EngineFrameworkInterface.dir/src/global_core.cpp.i"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\KWL-Software\EVT\EngineFrameworkInterface\src\global_core.cpp > CMakeFiles\EngineFrameworkInterface.dir\src\global_core.cpp.i

CMakeFiles/EngineFrameworkInterface.dir/src/global_core.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/EngineFrameworkInterface.dir/src/global_core.cpp.s"
	C:\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\KWL-Software\EVT\EngineFrameworkInterface\src\global_core.cpp -o CMakeFiles\EngineFrameworkInterface.dir\src\global_core.cpp.s

# Object files for target EngineFrameworkInterface
EngineFrameworkInterface_OBJECTS = \
"CMakeFiles/EngineFrameworkInterface.dir/src/data_process.cpp.obj" \
"CMakeFiles/EngineFrameworkInterface.dir/src/data_recorder.cpp.obj" \
"CMakeFiles/EngineFrameworkInterface.dir/src/device_manager.cpp.obj" \
"CMakeFiles/EngineFrameworkInterface.dir/src/engine_core.cpp.obj" \
"CMakeFiles/EngineFrameworkInterface.dir/src/engine_framework_interface.cpp.obj" \
"CMakeFiles/EngineFrameworkInterface.dir/src/engine_manager.cpp.obj" \
"CMakeFiles/EngineFrameworkInterface.dir/src/engine_variable_system.cpp.obj" \
"CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_task.cpp.obj" \
"CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_service.cpp.obj" \
"CMakeFiles/EngineFrameworkInterface.dir/src/logger.cpp.obj" \
"CMakeFiles/EngineFrameworkInterface.dir/src/report_process.cpp.obj" \
"CMakeFiles/EngineFrameworkInterface.dir/src/global_core.cpp.obj"

# External object files for target EngineFrameworkInterface
EngineFrameworkInterface_EXTERNAL_OBJECTS =

bin/libEngineFrameworkInterface.dll: CMakeFiles/EngineFrameworkInterface.dir/src/data_process.cpp.obj
bin/libEngineFrameworkInterface.dll: CMakeFiles/EngineFrameworkInterface.dir/src/data_recorder.cpp.obj
bin/libEngineFrameworkInterface.dll: CMakeFiles/EngineFrameworkInterface.dir/src/device_manager.cpp.obj
bin/libEngineFrameworkInterface.dll: CMakeFiles/EngineFrameworkInterface.dir/src/engine_core.cpp.obj
bin/libEngineFrameworkInterface.dll: CMakeFiles/EngineFrameworkInterface.dir/src/engine_framework_interface.cpp.obj
bin/libEngineFrameworkInterface.dll: CMakeFiles/EngineFrameworkInterface.dir/src/engine_manager.cpp.obj
bin/libEngineFrameworkInterface.dll: CMakeFiles/EngineFrameworkInterface.dir/src/engine_variable_system.cpp.obj
bin/libEngineFrameworkInterface.dll: CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_task.cpp.obj
bin/libEngineFrameworkInterface.dll: CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_service.cpp.obj
bin/libEngineFrameworkInterface.dll: CMakeFiles/EngineFrameworkInterface.dir/src/logger.cpp.obj
bin/libEngineFrameworkInterface.dll: CMakeFiles/EngineFrameworkInterface.dir/src/report_process.cpp.obj
bin/libEngineFrameworkInterface.dll: CMakeFiles/EngineFrameworkInterface.dir/src/global_core.cpp.obj
bin/libEngineFrameworkInterface.dll: CMakeFiles/EngineFrameworkInterface.dir/build.make
bin/libEngineFrameworkInterface.dll: CMakeFiles/EngineFrameworkInterface.dir/linkLibs.rsp
bin/libEngineFrameworkInterface.dll: CMakeFiles/EngineFrameworkInterface.dir/objects1.rsp
bin/libEngineFrameworkInterface.dll: CMakeFiles/EngineFrameworkInterface.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=E:\KWL-Software\EVT\EngineFrameworkInterface\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Linking CXX shared library bin\libEngineFrameworkInterface.dll"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\EngineFrameworkInterface.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/EngineFrameworkInterface.dir/build: bin/libEngineFrameworkInterface.dll
.PHONY : CMakeFiles/EngineFrameworkInterface.dir/build

CMakeFiles/EngineFrameworkInterface.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\EngineFrameworkInterface.dir\cmake_clean.cmake
.PHONY : CMakeFiles/EngineFrameworkInterface.dir/clean

CMakeFiles/EngineFrameworkInterface.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" E:\KWL-Software\EVT\EngineFrameworkInterface E:\KWL-Software\EVT\EngineFrameworkInterface E:\KWL-Software\EVT\EngineFrameworkInterface\build E:\KWL-Software\EVT\EngineFrameworkInterface\build E:\KWL-Software\EVT\EngineFrameworkInterface\build\CMakeFiles\EngineFrameworkInterface.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/EngineFrameworkInterface.dir/depend

