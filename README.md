# EngineManager 项目

## 项目概述

本项目是一个基于现代C++的引擎管理器系统，提供了完整的引擎监控、变量管理和数据记录功能。项目采用分层架构设计，通过DLL接口向外提供服务，支持实时系统监控、变量系统管理和数据持久化等核心功能。

## 主要特性

- **系统生命周期管理**：提供完整的系统初始化、启动、停止和重启功能
- **系统监控**：实时监控CPU、内存等系统资源使用情况
- **变量系统**：提供类型安全的变量存储和管理，支持数字、字符串、布尔值等类型
- **日志管理**：基于spdlog的高性能日志系统，支持动态日志级别调整
- **配置管理**：支持系统配置的动态设置和获取
- **单例模式**：引擎管理器采用单例模式，确保全局唯一性
- **错误处理**：完善的错误处理和异常管理机制
- **跨平台支持**：支持Windows和Linux平台

## 项目架构

### 分层架构设计

项目采用简化的分层架构设计，每层职责明确，依赖关系清晰：

#### 第1层 - 管理层
- **engine_manager**: 引擎管理器，核心管理模块，提供基本的系统生命周期管理

#### 第2层 - 核心功能层
- **system_monitor**: 系统监控模块，实时监测CPU、内存等系统指标
- **engine_variable_system**: 引擎变量系统，提供类型安全的变量存储和管理

#### 第3层 - 支撑服务层
- **logger**: 日志管理器，提供统一的日志记录功能

### 项目文件结构

```
EngineManager/
├── CMakeLists.txt                              # CMake 构建配置
├── README.md                                   # 项目说明文档
├── test_spdlog.cpp                            # 主程序入口
├── include/                                    # 头文件目录
│   ├── engine_manager.hpp                     # 引擎管理器头文件
│   ├── engine_variable_system.hpp             # 变量系统头文件
│   ├── logger.hpp                             # 日志管理器头文件
│   └── system_monitor.hpp                     # 系统监控头文件
├── src/                                        # 源文件目录
│   ├── engine_manager.cpp                     # 引擎管理器实现
│   ├── engine_variable_system.cpp             # 变量系统实现
│   ├── logger.cpp                             # 日志管理器实现
│   └── system_monitor.cpp                     # 系统监控实现
├── examples/                                   # 示例代码目录
│   └── main.cpp                               # 使用示例
├── third_library/                              # 第三方库目录
│   ├── nlohmann/                              # JSON 库
│   │   └── json.hpp                           # JSON 头文件
│   └── spdlog/                                # spdlog 库
│       └── include/                           # spdlog 头文件
└── DOC/                                       # 文档目录
    ├── Architecture.md                        # 架构设计文档
    └── engine_layer_reference_program/        # 参考实现
        ├── variable_system.hpp                # 变量系统参考头文件
        └── variable_system.cpp                # 变量系统参考实现
```

## 核心接口

### EngineManager 类

引擎管理器提供系统生命周期管理功能：

```cpp
class EngineManager {
public:
    static EngineManager& get_instance();
    
    // 系统生命周期管理
    bool initialize(const EngineManagement::SystemConfig& config);
    bool start();
    bool start_engine();  // 兼容接口
    bool stop();
    void shutdown();
    bool restart();
    
    // 状态查询
    EngineManagement::SystemStatus get_system_status() const;
    
    // 配置管理
    void set_config(const EngineManagement::SystemConfig& config);
    EngineManagement::SystemConfig get_config() const;
    
    // 日志管理
    void set_log_level(const std::string& level);
    std::string get_log_level() const;
    void log_message(const std::string& level, const std::string& message);
    
private:
    EngineManager();
    ~EngineManager();
};
```

### Logger 类

日志管理器提供统一的日志记录功能：

```cpp
class Logger {
public:
    static std::shared_ptr<spdlog::logger> get_root_logger();
    static void initialize();
    static void shutdown();
};
```

### SystemMonitor 类

系统监控器提供实时系统状态监控：

```cpp
class SystemMonitor {
public:
    SystemMonitor();
    ~SystemMonitor();
    
    void start();
    void stop();
    
    double get_cpu_usage() const;
    double get_memory_usage() const;
    
private:
    void monitor_thread_func();
    void update_system_status();
    void check_system_health();
};
```

### EngineVariableSystem 类

引擎变量系统提供类型安全的变量存储和管理功能：

```cpp
class EngineVariableSystem {
public:
    EngineVariableSystem();
    ~EngineVariableSystem() noexcept;
    
    // 统一的变量设置接口（模板方法）
    template<typename T>
    void set(const std::string& name, T&& value);
    
    // 统一的获取接口（模板方法）
    template<typename T>
    T get(const std::string& name, const T& defaultValue = T{}) const;
    
    // 类型特化的设置方法
    void set_number(const std::string& name, double value, const std::string& unit = "");
    void set_string(const std::string& name, const std::string& value, const std::string& unit = "");
    void set_bool(const std::string& name, bool value, const std::string& unit = "");
    void set_int(const std::string& name, int value, const std::string& unit = "");
    
    // 类型特化的获取方法
    double get_number(const std::string& name, double default_value = 0.0) const;
    std::string get_string(const std::string& name, const std::string& default_value = "") const;
    bool get_bool(const std::string& name, bool default_value = false) const;
    int get_int(const std::string& name, int default_value = 0) const;
    
    // 变量管理方法
    bool has_variable(const std::string& name) const;
    void remove_variable(const std::string& name);
    void clear_all_variables();
    
    // 单位和保存状态管理
    void set_variable_unit(const std::string& name, const std::string& unit);
    std::string get_variable_unit(const std::string& name) const;
    void set_variable_save_status(const std::string& name, bool should_save);
    bool get_variable_save_status(const std::string& name) const;
    
    // 调试和信息方法
    void print_all_variables() const;
    size_t get_variable_count() const noexcept;
    std::vector<std::string> get_variable_names() const;
};
```

### 初始化和清理

```cpp
// 初始化DLL管理器，加载指定目录下的所有驱动DLL
EXP_API ATE_EC initialize_dll(const char* dll_directory_path);

// 清理DLL管理器，卸载指定路径下的驱动DLL
EXP_API ATE_EC cleanup_dll(const char* dll_directory_path);
```

### 设备操作

```cpp
// 连接设备（通过实例名）
EXP_API ATE_EC connect_device(const char* instance_name);

// 断开设备连接
EXP_API ATE_EC disconnect_device(const char* instance_name);

// 获取设备描述符
EXP_API ATE_EC get_device_descriptor(const char* instance_name, 
                                    char* device_descriptor, 
                                    const int32_t buffer_size, 
                                    int32_t* device_descriptor_size);

// 执行统一命令接口
EXP_API ATE_EC execute_command_unified(const char* instance_name, 
                                      const E5000_CommandMode mode, 
                                      const char* command, 
                                      const int32_t command_length, 
                                      const int32_t timeout_ms, 
                                      char* response, 
                                      const int32_t buffer_size, 
                                      int32_t* response_size);

// 获取通道状态
EXP_API ATE_EC get_channel_state(const char* instance_name, 
                                char* channel_state, 
                                const int32_t buffer_size, 
                                int32_t* channel_state_size);
```

## 配置文件格式

项目支持JSON格式的配置文件，示例如下：

```json
{
    "system": {
        "name": "EngineManager",
        "version": "1.0.0",
        "log_level": "info"
    },
    "variables": {
        "engine.rpm": {
            "value": 1500.0,
            "type": "number",
            "unit": "RPM",
            "save_status": true,
            "description": "引擎转速"
        },
        "engine.temperature": {
            "value": 85.5,
            "type": "number",
            "unit": "°C",
            "save_status": true,
            "description": "引擎温度"
        },
        "engine.status": {
            "value": "running",
            "type": "string",
            "save_status": false,
            "description": "引擎状态"
        }
    },
    "monitoring": {
        "cpu_threshold": 80.0,
        "memory_threshold": 90.0,
        "update_interval": 1000
    }
}
```

## 编译要求

- **C++标准**: C++17 或更高版本
- **编译器**: 
  - Windows: Visual Studio 2019+ 或 MinGW-w64
  - Linux: GCC 7+ 或 Clang 6+
- **CMake**: 3.15 或更高版本
- **依赖库**:
  - spdlog (已包含在third_library目录)
  - nlohmann/json (已包含在third_library目录)
  - Windows平台: pdh.lib (性能数据助手库)

## 编译步骤

### Windows (MinGW)

```bash
# 创建构建目录
mkdir build
cd build

# 配置CMake
cmake .. -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release

# 编译
mingw32-make -j8

# 运行测试程序
.\test_spdlog.exe

# 运行示例程序
cd ..\examples
g++ -std=c++17 -I..\include -I..\third_library\spdlog\include -I..\third_library\nlohmann main.cpp ..\src\*.cpp -o main.exe
.\main.exe
```

### Linux

```bash
# 创建构建目录
mkdir build
cd build

# 配置CMake
cmake .. -DCMAKE_BUILD_TYPE=Release

# 编译
make -j$(nproc)

# 运行测试程序
./test_spdlog

# 运行示例程序
cd ../examples
g++ -std=c++17 -I../include -I../third_library/spdlog/include -I../third_library/nlohmann main.cpp ../src/*.cpp -o main
./main
```

## 使用示例

### 基本使用

```cpp
#include "engine_manager.hpp"
#include "logger.hpp"
#include "system_monitor.hpp"
#include "engine_variable_system.hpp"

int main() {
    // 获取引擎管理器实例
    auto& engine_manager = EngineManager::get_instance();
    
    // 配置系统
    EngineManagement::SystemConfig config;
    config.system_name = "TestEngine";
    config.version = "1.0.0";
    config.log_level = "info";
    
    // 初始化系统
    if (!engine_manager.initialize(config)) {
        std::cerr << "Failed to initialize engine manager" << std::endl;
        return -1;
    }
    
    // 启动引擎
    if (!engine_manager.start()) {
        std::cerr << "Failed to start engine" << std::endl;
        return -1;
    }
    
    // 获取日志记录器
    auto logger = Logger::get_root_logger();
    logger->info("Engine started successfully");
    
    // 创建系统监控器
    SystemMonitor monitor;
    monitor.start();
    
    // 创建变量系统
    EngineVariableSystem var_system;
    
    // 设置一些变量
    var_system.set_number("engine.rpm", 1500.0);
    var_system.set_number("engine.temperature", 85.5);
    
    // 获取系统状态
    logger->info("CPU使用率: {:.2f}%", monitor.get_cpu_usage());
    logger->info("内存使用率: {:.2f}%", monitor.get_memory_usage());
    
    // 获取变量值
    auto rpm = var_system.get_number("engine.rpm");
    logger->info("引擎转速: {} RPM", rpm);
    
    // 显示系统状态
    auto status = engine_manager.get_system_status();
    logger->info("系统运行状态: {}", status.is_running ? "运行中" : "已停止");
    
    // 清理资源
    monitor.stop();
    var_system.clear_all_variables();
    engine_manager.stop();
    
    // 关闭引擎管理器并检查结果
    if (engine_manager.shutdown()) {
        logger->info("引擎管理器成功关闭");
    } else {
        logger->error("引擎管理器关闭失败");
    }
    
    return 0;
}
```

### 高级功能

```cpp
// 引擎管理器配置管理
EngineManagement::SystemConfig config = engine_manager.get_config();
config.log_level = "debug";
engine_manager.set_config(config);

// 日志级别动态调整
engine_manager.set_log_level("debug");
engine_manager.log_message("info", "系统配置已更新");

// 变量系统高级操作
var_system.set_variable_unit("engine.temperature", "°C");
var_system.set_variable_save_status("engine.rpm", true);

// 批量设置变量
var_system.set_number("engine.max_rpm", 6000.0);
var_system.set_number("engine.min_temperature", -40.0);
var_system.set_number("engine.max_temperature", 120.0);

// 变量查询和管理
if (var_system.has_variable("engine.temperature")) {
    auto temp = var_system.get_number("engine.temperature");
    if (temp > 100.0) {
        Logger::get_root_logger()->warn("引擎温度过高: {:.1f}°C", temp);
    }
}

// 获取所有变量名
auto variable_names = var_system.get_variable_names();
Logger::get_root_logger()->info("当前变量数量: {}", var_system.get_variable_count());

// 系统重启
if (engine_manager.restart()) {
    Logger::get_root_logger()->info("系统重启成功");
}

// 打印所有变量（调试用）
var_system.print_all_variables();
```



## 错误处理

项目使用ATE_EC枚举类型进行错误处理，主要错误代码包括：

- `ATE_SUCCESS`: 操作成功
- `ATE_ERROR_INVALID_PARAMETER`: 无效参数
- `ATE_ERROR_NOT_INITIALIZED`: 未初始化
- `ATE_ERROR_DEVICE_NOT_FOUND`: 设备未找到
- `ATE_ERROR_LIBRARY_LOAD_FAILED`: 库加载失败
- `ATE_ERROR_DEVICE_ALREADY_EXISTS`: 设备已存在

## 设计原则

1. **无锁设计**: 管理器本身不使用全局锁，依赖底层驱动的锁机制
2. **RAII原则**: 使用智能指针和RAII管理资源
3. **异常安全**: 提供强异常安全保证
4. **接口兼容**: 保持与原始DLL接口的完全兼容
5. **模块化设计**: 清晰的模块分离和职责划分

## 注意事项

- 确保所有外设驱动DLL都提供相应的JSON配置文件
- 实例名在同一管理器中必须唯一
- 在多线程环境中，不同设备实例可以并发操作
- 建议在应用程序退出前调用cleanup_dll清理资源

## 许可证

本项目遵循公司内部许可证协议。

## 联系信息

如有问题或建议，请联系开发团队。