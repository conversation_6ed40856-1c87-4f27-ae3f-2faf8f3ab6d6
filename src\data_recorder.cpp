#include "data_recorder.hpp"
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <iostream>

// DataRecorder 实现
DataRecorder::DataRecorder(GlobalVariableTask& variable_system, const DataRecorderConfig& config)
    : m_variable_system(variable_system)
    , m_config(config)
    , m_initialized(false)
    , m_recording(false)
    , m_shutdown_requested(false)
    , m_current_file_records(0)
    , m_file_sequence(1) {
}

DataRecorder::~DataRecorder() {
    shutdown();
}

ATE_EC DataRecorder::initialize() {
    if (m_initialized.load()) {
        return ATE_ERROR_ALREADY_INITIALIZED;
    }
    
    if (!m_config.is_valid()) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    // 预分配缓冲区空间
    {
        std::lock_guard<std::mutex> lock(m_records_mutex);
        m_records.reserve(m_config.buffer_size);
    }
    
    m_initialized.store(true);
    return ATE_SUCCESS;
}

void DataRecorder::shutdown() {
    if (!m_initialized.load()) {
        return;
    }
    
    // 请求关闭
    m_shutdown_requested.store(true);
    
    // 停止记录
    if (m_recording.load()) {
        stop_recording();
    }
    
    // 等待线程结束
    if (m_recording_thread && m_recording_thread->joinable()) {
        m_recording_thread->join();
    }
    
    if (m_auto_save_thread && m_auto_save_thread->joinable()) {
        m_auto_save_thread->join();
    }
    
    // 清理资源
    {
        std::lock_guard<std::mutex> lock(m_records_mutex);
        m_records.clear();
    }
    
    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        m_recorded_variables.clear();
    }
    
    m_initialized.store(false);
    m_shutdown_requested.store(false);
}

ATE_EC DataRecorder::start_recording() {
    if (!m_initialized.load()) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    if (m_recording.load()) {
        return ATE_ERROR_ALREADY_INITIALIZED;
    }
    
    // 检查是否有要记录的变量
    {
        std::lock_guard<std::mutex> lock(m_variables_mutex);
        if (m_recorded_variables.empty()) {
            return ATE_ERROR_INVALID_PARAMETER;
        }
    }
    
    m_recording.store(true);
    
    // 启动记录线程
    try {
        m_recording_thread = std::make_unique<std::thread>(&DataRecorder::recording_thread_function, this);
        
        // 如果启用自动保存，启动自动保存线程
        if (m_config.enable_auto_save) {
            m_auto_save_thread = std::make_unique<std::thread>(&DataRecorder::auto_save_thread_function, this);
        }
    } catch (const std::exception&) {
        m_recording.store(false);
        return ATE_ERROR_THREAD_CREATE_FAILED;
    }
    
    return ATE_SUCCESS;
}

ATE_EC DataRecorder::stop_recording() {
    if (!m_recording.load()) {
        return ATE_ERROR_OPERATION_NOT_PERMITTED;
    }
    
    m_recording.store(false);
    
    // 等待记录线程结束
    if (m_recording_thread && m_recording_thread->joinable()) {
        m_recording_thread->join();
        m_recording_thread.reset();
    }
    
    // 等待自动保存线程结束
    if (m_auto_save_thread && m_auto_save_thread->joinable()) {
        m_auto_save_thread->join();
        m_auto_save_thread.reset();
    }
    
    return ATE_SUCCESS;
}

bool DataRecorder::is_recording() const {
    return m_recording.load();
}

ATE_EC DataRecorder::add_variable_to_record(const std::string& variable_name) {
    if (!m_initialized.load()) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    // 检查变量是否存在于变量系统中
    if (!m_variable_system.has_variable(variable_name)) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    
    // 检查是否已经存在
    auto it = std::find(m_recorded_variables.begin(), m_recorded_variables.end(), variable_name);
    if (it == m_recorded_variables.end()) {
        m_recorded_variables.push_back(variable_name);
    }
    
    return ATE_SUCCESS;
}

ATE_EC DataRecorder::remove_variable_from_record(const std::string& variable_name) {
    if (!m_initialized.load()) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    
    auto it = std::find(m_recorded_variables.begin(), m_recorded_variables.end(), variable_name);
    if (it != m_recorded_variables.end()) {
        m_recorded_variables.erase(it);
    }
    
    return ATE_SUCCESS;
}

std::vector<std::string> DataRecorder::get_recorded_variables() const {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    return m_recorded_variables;
}

void DataRecorder::clear_recorded_variables() {
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    m_recorded_variables.clear();
}

size_t DataRecorder::get_record_count() const {
    std::lock_guard<std::mutex> lock(m_records_mutex);
    return m_records.size();
}

void DataRecorder::clear_records() {
    std::lock_guard<std::mutex> lock(m_records_mutex);
    m_records.clear();
    m_current_file_records.store(0);
}

ATE_EC DataRecorder::save_to_file(const std::string& format, const std::string& filename) {
    if (!m_initialized.load()) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    std::string actual_filename = filename;
    if (actual_filename.empty()) {
        actual_filename = generate_filename(format, false);
    }
    
    return perform_save(format, actual_filename);
}

ATE_EC DataRecorder::set_config(const DataRecorderConfig& config) {
    if (!config.is_valid()) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    if (m_recording.load()) {
        return ATE_ERROR_DEVICE_BUSY;
    }
    
    m_config = config;
    
    // 重新分配缓冲区
    {
        std::lock_guard<std::mutex> lock(m_records_mutex);
        m_records.clear();
        m_records.reserve(m_config.buffer_size);
    }
    
    return ATE_SUCCESS;
}

const DataRecorderConfig& DataRecorder::get_config() const {
    return m_config;
}

double DataRecorder::get_buffer_usage() const {
    std::lock_guard<std::mutex> lock(m_records_mutex);
    if (m_config.buffer_size == 0) {
        return 0.0;
    }
    return static_cast<double>(m_records.size()) / static_cast<double>(m_config.buffer_size);
}

void DataRecorder::recording_thread_function() {
    while (m_recording.load() && !m_shutdown_requested.load()) {
        try {
            // 收集当前数据
            DataRecord record = collect_current_data();
            
            // 添加到缓冲区
            {
                std::lock_guard<std::mutex> lock(m_records_mutex);
                
                // 如果缓冲区满了，移除最旧的记录
                if (m_records.size() >= m_config.buffer_size) {
                    m_records.erase(m_records.begin());
                }
                
                m_records.push_back(std::move(record));
            }
            
            // 检查是否需要触发保存
            if (should_trigger_save()) {
                if (m_config.enable_csv_export) {
                    perform_save("csv", generate_filename("csv"));
                }
                if (m_config.enable_xml_export) {
                    perform_save("xml", generate_filename("xml"));
                }
            }
            
        } catch (const std::exception& e) {
            // 记录错误但继续运行
            std::cerr << "Recording thread error: " << e.what() << std::endl;
        }
        
        // 等待下一个记录间隔
        std::this_thread::sleep_for(m_config.recording_interval);
    }
}

void DataRecorder::auto_save_thread_function() {
    while (m_recording.load() && !m_shutdown_requested.load()) {
        std::this_thread::sleep_for(m_config.auto_save_interval);
        
        if (!m_recording.load() || m_shutdown_requested.load()) {
            break;
        }
        
        try {
            // 执行自动保存
            if (m_config.enable_csv_export) {
                perform_save("csv", generate_filename("csv"));
            }
            if (m_config.enable_xml_export) {
                perform_save("xml", generate_filename("xml"));
            }
        } catch (const std::exception& e) {
            std::cerr << "Auto save thread error: " << e.what() << std::endl;
        }
    }
}

DataRecord DataRecorder::collect_current_data() {
    DataRecord record(std::chrono::system_clock::now());
    
    std::lock_guard<std::mutex> lock(m_variables_mutex);
    
    for (const auto& variable_name : m_recorded_variables) {
        VariableValue value;
        if (m_variable_system.get_variable(variable_name, value) == ATE_SUCCESS) {
            record.variable_values[variable_name] = value.asString();
        }
    }
    
    return record;
}

bool DataRecorder::should_trigger_save() const {
    std::lock_guard<std::mutex> lock(m_records_mutex);
    return m_current_file_records.load() >= m_config.max_records_per_file;
}

std::string DataRecorder::generate_filename(const std::string& format, bool use_sequence) const {
    std::ostringstream oss;
    oss << m_config.base_filename;
    
    if (use_sequence) {
        oss << "_" << std::setfill('0') << std::setw(4) << m_file_sequence.load();
    }
    
    // 添加时间戳
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::localtime(&time_t);
    
    oss << "_" << std::put_time(&tm, "%Y%m%d_%H%M%S");
    oss << "." << format;
    
    return oss.str();
}

std::string DataRecorder::format_timestamp(const std::chrono::system_clock::time_point& time_point) const {
    auto time_t = std::chrono::system_clock::to_time_t(time_point);
    auto tm = *std::localtime(&time_t);
    
    // 获取毫秒部分
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(time_point.time_since_epoch()) % 1000;
    
    std::ostringstream oss;
    oss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S");
    oss << "." << std::setfill('0') << std::setw(3) << ms.count();
    
    return oss.str();
}

ATE_EC DataRecorder::save_records_to_csv(const std::vector<DataRecord>& records, const std::string& filename) const {
    if (records.empty()) {
        return ATE_SUCCESS;
    }
    
    std::ofstream file(filename);
    if (!file.is_open()) {
        return ATE_ERROR_FILE_ACCESS_DENIED;
    }
    
    try {
        // 写入CSV头部
        file << "Timestamp";
        
        // 获取所有变量名（从第一条记录）
        std::vector<std::string> variable_names;
        for (const auto& pair : records[0].variable_values) {
            variable_names.push_back(pair.first);
            file << "," << pair.first;
        }
        file << "\n";
        
        // 写入数据行
        for (const auto& record : records) {
            file << format_timestamp(record.timestamp);
            
            for (const auto& variable_name : variable_names) {
                file << ",";
                auto it = record.variable_values.find(variable_name);
                if (it != record.variable_values.end()) {
                    file << it->second;
                }
            }
            file << "\n";
        }
        
        file.close();
        return ATE_SUCCESS;
        
    } catch (const std::exception&) {
        file.close();
        return ATE_ERROR_FILE_CORRUPTED;
    }
}

ATE_EC DataRecorder::save_records_to_xml(const std::vector<DataRecord>& records, const std::string& filename) const {
    if (records.empty()) {
        return ATE_SUCCESS;
    }
    
    std::ofstream file(filename);
    if (!file.is_open()) {
        return ATE_ERROR_FILE_ACCESS_DENIED;
    }
    
    try {
        // 写入XML头部
        file << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        file << "<DataRecords>\n";
        
        // 写入数据记录
        for (const auto& record : records) {
            file << "  <Record timestamp=\"" << format_timestamp(record.timestamp) << "\">\n";
            
            for (const auto& pair : record.variable_values) {
                file << "    <Variable name=\"" << pair.first << "\">" 
                     << pair.second << "</Variable>\n";
            }
            
            file << "  </Record>\n";
        }
        
        file << "</DataRecords>\n";
        file.close();
        
        return ATE_SUCCESS;
        
    } catch (const std::exception&) {
        file.close();
        return ATE_ERROR_FILE_CORRUPTED;
    }
}

ATE_EC DataRecorder::perform_save(const std::string& format, const std::string& filename) {
    // 复制当前记录以避免长时间锁定
    std::vector<DataRecord> records_to_save;
    {
        std::lock_guard<std::mutex> lock(m_records_mutex);
        records_to_save = m_records;
        
        // 如果达到文件分割条件，清空缓冲区并增加序列号
        if (m_current_file_records.load() >= m_config.max_records_per_file) {
            m_records.clear();
            m_current_file_records.store(0);
            m_file_sequence.fetch_add(1);
        } else {
            m_current_file_records.store(records_to_save.size());
        }
    }
    
    if (records_to_save.empty()) {
        return ATE_SUCCESS;
    }
    
    ATE_EC result = ATE_ERROR_INVALID_FORMAT;
    
    if (format == "csv") {
        result = save_records_to_csv(records_to_save, filename);
    } else if (format == "xml") {
        result = save_records_to_xml(records_to_save, filename);
    }
    
    return result;
}