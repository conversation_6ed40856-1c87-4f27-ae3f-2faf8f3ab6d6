#ifndef DATA_PROCESS_HPP
#define DATA_PROCESS_HPP

#include <memory>
#include <vector>
#include <string>
#include <functional>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <atomic>
#include <map>
#include <chrono>

// 前向声明
class GlobalVariableTask;
class DataRecorder;

namespace DataProcessing {

// 数据类型枚举
enum class DataType {
    INTEGER,
    DOUBLE,
    STRING,
    BOOLEAN,
    BINARY,
    TIMESTAMP
};

// 数据处理状态
enum class ProcessStatus {
    IDLE,
    PROCESSING,
    COMPLETED,
    ERROR,
    CANCELLED
};

// 数据项结构
struct DataItem {
    std::string name;
    DataType type;
    std::string value;
    std::chrono::system_clock::time_point timestamp;
    std::map<std::string, std::string> metadata;
    
    DataItem() = default;
    DataItem(const std::string& n, DataType t, const std::string& v)
        : name(n), type(t), value(v), timestamp(std::chrono::system_clock::now()) {}
};

// 数据批次结构
struct DataBatch {
    std::string batch_id;
    std::vector<DataItem> items;
    std::chrono::system_clock::time_point created_time;
    std::map<std::string, std::string> batch_metadata;
    
    DataBatch() : created_time(std::chrono::system_clock::now()) {}
    explicit DataBatch(const std::string& id) 
        : batch_id(id), created_time(std::chrono::system_clock::now()) {}
};

// 数据处理配置
struct ProcessConfig {
    bool enable_filtering = true;
    bool enable_validation = true;
    bool enable_transformation = true;
    bool enable_aggregation = false;
    size_t max_batch_size = 1000;
    std::chrono::milliseconds batch_timeout{5000};
    size_t max_queue_size = 10000;
    int worker_thread_count = 2;
    std::string output_format = "json";
    std::map<std::string, std::string> custom_settings;
};

// 数据过滤器接口
class IDataFilter {
public:
    virtual ~IDataFilter() = default;
    virtual bool filter(const DataItem& item) = 0;
    virtual std::string get_name() const = 0;
};

// 数据转换器接口
class IDataTransformer {
public:
    virtual ~IDataTransformer() = default;
    virtual DataItem transform(const DataItem& item) = 0;
    virtual std::string get_name() const = 0;
};

// 数据验证器接口
class IDataValidator {
public:
    virtual ~IDataValidator() = default;
    virtual bool validate(const DataItem& item) = 0;
    virtual std::string get_error_message() const = 0;
    virtual std::string get_name() const = 0;
};

// 数据聚合器接口
class IDataAggregator {
public:
    virtual ~IDataAggregator() = default;
    virtual void add_data(const DataItem& item) = 0;
    virtual DataItem get_result() = 0;
    virtual void reset() = 0;
    virtual std::string get_name() const = 0;
};

} // namespace DataProcessing

// 数据处理核心类
class DataProcess {
public:
    DataProcess();
    ~DataProcess();

    // 禁用拷贝构造和赋值
    DataProcess(const DataProcess&) = delete;
    DataProcess& operator=(const DataProcess&) = delete;

    // 初始化和清理
    bool initialize(const DataProcessing::ProcessConfig& config);
    void shutdown();
    bool is_initialized() const { return m_initialized; }

    // 配置管理
    void set_config(const DataProcessing::ProcessConfig& config);
    DataProcessing::ProcessConfig get_config() const;
    void update_config_setting(const std::string& key, const std::string& value);

    // 数据输入接口
    bool add_data_item(const DataProcessing::DataItem& item);
    bool add_data_batch(const DataProcessing::DataBatch& batch);
    bool add_data_from_variable_system(GlobalVariableTask* var_system, const std::vector<std::string>& variable_names);

    // 处理控制
    bool start_processing();
    bool stop_processing();
    bool pause_processing();
    bool resume_processing();
    DataProcessing::ProcessStatus get_status() const { return m_status; }

    // 过滤器管理
    void add_filter(std::unique_ptr<DataProcessing::IDataFilter> filter);
    void remove_filter(const std::string& filter_name);
    void clear_filters();
    std::vector<std::string> get_filter_names() const;

    // 转换器管理
    void add_transformer(std::unique_ptr<DataProcessing::IDataTransformer> transformer);
    void remove_transformer(const std::string& transformer_name);
    void clear_transformers();
    std::vector<std::string> get_transformer_names() const;

    // 验证器管理
    void add_validator(std::unique_ptr<DataProcessing::IDataValidator> validator);
    void remove_validator(const std::string& validator_name);
    void clear_validators();
    std::vector<std::string> get_validator_names() const;

    // 聚合器管理
    void add_aggregator(std::unique_ptr<DataProcessing::IDataAggregator> aggregator);
    void remove_aggregator(const std::string& aggregator_name);
    void clear_aggregators();
    std::vector<std::string> get_aggregator_names() const;

    // 结果获取
    std::vector<DataProcessing::DataItem> get_processed_data();
    std::vector<DataProcessing::DataBatch> get_processed_batches();
    bool export_results(const std::string& file_path, const std::string& format = "json");

    // 统计信息
    size_t get_input_count() const { return m_input_count; }
    size_t get_processed_count() const { return m_processed_count; }
    size_t get_filtered_count() const { return m_filtered_count; }
    size_t get_error_count() const { return m_error_count; }
    size_t get_queue_size() const;
    double get_processing_rate() const; // 每秒处理数量

    // 错误处理
    std::vector<std::string> get_error_messages() const;
    void clear_error_messages();

    // 回调函数设置
    using DataProcessedCallback = std::function<void(const DataProcessing::DataItem&)>;
    using BatchProcessedCallback = std::function<void(const DataProcessing::DataBatch&)>;
    using ErrorCallback = std::function<void(const std::string&)>;
    
    void set_data_processed_callback(DataProcessedCallback callback);
    void set_batch_processed_callback(BatchProcessedCallback callback);
    void set_error_callback(ErrorCallback callback);

private:
    // 内部状态
    std::atomic<bool> m_initialized{false};
    std::atomic<DataProcessing::ProcessStatus> m_status{DataProcessing::ProcessStatus::IDLE};
    DataProcessing::ProcessConfig m_config;
    
    // 线程管理
    std::vector<std::thread> m_worker_threads;
    std::atomic<bool> m_should_stop{false};
    std::atomic<bool> m_paused{false};
    
    // 数据队列
    std::queue<DataProcessing::DataItem> m_input_queue;
    std::queue<DataProcessing::DataItem> m_output_queue;
    mutable std::mutex m_input_mutex;
    mutable std::mutex m_output_mutex;
    std::condition_variable m_input_cv;
    std::condition_variable m_output_cv;
    
    // 处理组件
    std::vector<std::unique_ptr<DataProcessing::IDataFilter>> m_filters;
    std::vector<std::unique_ptr<DataProcessing::IDataTransformer>> m_transformers;
    std::vector<std::unique_ptr<DataProcessing::IDataValidator>> m_validators;
    std::vector<std::unique_ptr<DataProcessing::IDataAggregator>> m_aggregators;
    mutable std::mutex m_components_mutex;
    
    // 统计信息
    std::atomic<size_t> m_input_count{0};
    std::atomic<size_t> m_processed_count{0};
    std::atomic<size_t> m_filtered_count{0};
    std::atomic<size_t> m_error_count{0};
    std::chrono::steady_clock::time_point m_start_time;
    
    // 错误处理
    std::vector<std::string> m_error_messages;
    mutable std::mutex m_error_mutex;
    
    // 回调函数
    DataProcessedCallback m_data_processed_callback;
    BatchProcessedCallback m_batch_processed_callback;
    ErrorCallback m_error_callback;
    mutable std::mutex m_callback_mutex;
    
    // 内部方法
    void worker_thread_function();
    bool process_data_item(DataProcessing::DataItem& item);
    bool apply_filters(const DataProcessing::DataItem& item);
    DataProcessing::DataItem apply_transformers(const DataProcessing::DataItem& item);
    bool apply_validators(const DataProcessing::DataItem& item);
    void apply_aggregators(const DataProcessing::DataItem& item);
    void add_error_message(const std::string& message);
    void notify_data_processed(const DataProcessing::DataItem& item);
    void notify_error(const std::string& message);
};

#endif // DATA_PROCESS_HPP