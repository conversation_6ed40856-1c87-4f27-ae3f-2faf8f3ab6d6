#include "data_recorder.hpp"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <fstream>

DataRecorder::DataRecorder(VariableSystem& var_system, size_t buffer_size)
    : m_variable_system(var_system)
    , m_using_buffer_a(true)
    , m_active_buffer(&m_buffer_a)
    , m_saving_buffer(&m_buffer_b)
    , m_recording(false)
    , m_data_save_interval(std::chrono::milliseconds(100))  // 默认100ms保存间隔
    , m_buffer_size(std::min(buffer_size, static_cast<size_t>(5000)))  // 限制最大缓冲区大小，防止内存过度分配
    , m_max_records_trigger(1000)  // 默认1000条记录触发保存
    , m_max_time_trigger(std::chrono::milliseconds(5000))  // 默认5秒触发保存
    , m_max_records_per_file(10000)  // 默认每个文件最多10000条记录
    , m_current_file_records(0)
    , m_file_sequence(1)
    , m_base_filename("data_records")
    , m_auto_save_enabled(false)
    , m_auto_save_interval(std::chrono::milliseconds(5000))
    , m_auto_save_running(false)
    , m_file_save_running(false) {
    
    try {
        // 初始化双缓冲区，使用更保守的内存分配策略
        m_buffer_a.reserve(m_buffer_size);
        m_buffer_b.reserve(m_buffer_size);
        
        // 初始化时间戳
        m_last_save_time = std::chrono::steady_clock::now();
        m_buffer_start_time = std::chrono::steady_clock::now();
        
        std::cout << "DataRecorder initialized with dual buffer size: " << m_buffer_size 
                  << ", trigger conditions: " << m_max_records_trigger << " records or " 
                  << m_max_time_trigger.count() << "ms" << std::endl;
                  
    } catch (const std::bad_alloc& e) {
        std::cerr << "Memory allocation failed during DataRecorder initialization: " << e.what() << std::endl;
        std::cerr << "Requested buffer size: " << m_buffer_size << " records" << std::endl;
        throw;
    } catch (const std::exception& e) {
        std::cerr << "Error during DataRecorder initialization: " << e.what() << std::endl;
        throw;
    }
}

DataRecorder::~DataRecorder() {
    stop_recording();
    
    // 停止文件保存线程
    if (m_file_save_running.load()) {
        m_file_save_running = false;
        m_save_condition.notify_all();
        if (m_file_save_thread && m_file_save_thread->joinable()) {
            m_file_save_thread->join();
        }
    }
    
    // 停止自动保存线程
    if (m_auto_save_enabled) {
        m_auto_save_running = false;
        if (m_auto_save_thread.joinable()) {
            m_auto_save_thread.join();
        }
    }
}

bool DataRecorder::set_variable_save_status(const std::string& variable_name, bool should_save) {
    // 检查变量是否存在
    auto variables = m_variable_system.list_variables();
    bool variable_exists = false;
    for (const auto& var : variables) {
        if (var.name == variable_name) {
            variable_exists = true;
            break;
        }
    }
    
    if (!variable_exists) {
        std::cerr << "Variable '" << variable_name << "' does not exist!" << std::endl;
        return false;
    }
    
    // 设置变量的保存状态
    bool result = m_variable_system.set_variable_save_flag(variable_name, should_save);
    
    if (result) {
        // 更新指针链表
        update_save_pointers();
        std::cout << "Variable '" << variable_name << "' save status set to: " 
                  << (should_save ? "true" : "false") << std::endl;
    }
    
    return result;
}

std::vector<std::string> DataRecorder::get_saveable_variables() const {
    std::vector<std::string> saveable_vars;
    auto variables = m_variable_system.list_variables();
    
    for (const auto& var : variables) {
        if (var.should_save) {
            saveable_vars.push_back(var.name);
        }
    }
    
    return saveable_vars;
}

void DataRecorder::start_recording(std::chrono::milliseconds interval) {
    if (m_recording.load()) {
        std::cout << "Recording is already in progress!" << std::endl;
        return;
    }
    
    // interval参数现在用作数据保存间隔，而不是采集间隔
    m_data_save_interval = interval;
    update_save_pointers();
    
    if (m_save_pointers.empty()) {
        std::cout << "No variables are set to be saved. Please set save status first." << std::endl;
        return;
    }
    
    // 重置时间戳
    m_last_save_time = std::chrono::steady_clock::now();
    m_buffer_start_time = std::chrono::steady_clock::now();
    
    try {
        // 启动文件保存线程
        m_file_save_running.store(true);
        m_file_save_thread = std::make_unique<std::thread>(&DataRecorder::file_save_thread_function, this);
        
        // 启动10ms高频采集线程（优化后的采样频率）
        m_recording.store(true);
        m_record_thread = std::make_unique<std::thread>(&DataRecorder::high_frequency_sampling_thread, this);
        
        std::cout << "Started high-frequency recording (10ms sampling) for " << m_save_pointers.size() 
                  << " variables with data save interval: " << interval.count() << "ms" << std::endl;
                  
    } catch (const std::system_error& e) {
        std::cerr << "Failed to create threads: " << e.what() << std::endl;
        std::cerr << "Error code: " << e.code() << std::endl;
        
        // 清理已创建的资源
        m_recording.store(false);
        m_file_save_running.store(false);
        
        if (m_record_thread && m_record_thread->joinable()) {
            m_record_thread->join();
            m_record_thread.reset();
        }
        
        if (m_file_save_thread && m_file_save_thread->joinable()) {
            m_file_save_thread->join();
            m_file_save_thread.reset();
        }
        
        throw;  // 重新抛出异常
    } catch (const std::exception& e) {
        std::cerr << "Unexpected error during thread creation: " << e.what() << std::endl;
        
        // 清理资源
        m_recording.store(false);
        m_file_save_running.store(false);
        
        throw;
    }
}

void DataRecorder::stop_recording() {
    if (!m_recording.load()) {
        return;
    }
    
    // 停止采集线程
    m_recording.store(false);
    if (m_record_thread && m_record_thread->joinable()) {
        m_record_thread->join();
    }
    
    // 停止文件保存线程
    if (m_file_save_running.load()) {
        m_file_save_running.store(false);
        m_save_condition.notify_all();
        if (m_file_save_thread && m_file_save_thread->joinable()) {
            m_file_save_thread->join();
        }
    }
    
    // 保存剩余的未保存数据
    size_t remaining_records = 0;
    {
        std::lock_guard<std::mutex> lock(m_buffer_mutex);
        remaining_records = m_active_buffer->size();
    }
    
    if (remaining_records > 0) {
        std::cout << "Saving remaining " << remaining_records << " records before stopping..." << std::endl;
        
        // 使用CSV格式保存剩余数据，保持文件名规则
        bool save_success = save_with_splitting("csv");
        
        if (save_success) {
            std::cout << "Successfully saved remaining records." << std::endl;
            // 清空已保存的数据
            std::lock_guard<std::mutex> lock(m_buffer_mutex);
            m_active_buffer->clear();
        } else {
            std::cout << "Warning: Failed to save some remaining records." << std::endl;
        }
    }
    
    std::cout << "Recording stopped. Total records in active buffer: " << m_active_buffer->size() << std::endl;
}

size_t DataRecorder::get_record_count() const {
    std::lock_guard<std::mutex> lock(m_buffer_mutex);
    return m_active_buffer->size();
}

void DataRecorder::clear_records() {
    std::lock_guard<std::mutex> lock(m_buffer_mutex);
    m_active_buffer->clear();
    m_saving_buffer->clear();
    
    // 清空保存队列
    {
        std::lock_guard<std::mutex> save_lock(m_file_save_mutex);
        while (!m_save_queue.empty()) {
            m_save_queue.pop();
        }
    }
    
    std::cout << "All records and save queue cleared." << std::endl;
}

bool DataRecorder::save_records_to_csv(const std::vector<TableRecord>& records, const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Failed to open file: " << filename << std::endl;
        return false;
    }
    
    // 获取所有变量名（表头）
    std::vector<std::string> column_names;
    column_names.push_back("timestamp");
    
    if (!records.empty()) {
        for (const auto& pair : records[0].variable_values) {
            column_names.push_back(pair.first);
        }
        // 按字母顺序排序变量名（除了timestamp）
        std::sort(column_names.begin() + 1, column_names.end());
    }
    
    // 写入表头
    for (size_t i = 0; i < column_names.size(); ++i) {
        file << column_names[i];
        if (i < column_names.size() - 1) {
            file << ",";
        }
    }
    file << "\n";
    
    // 写入数据行
    for (const auto& record : records) {
        // 写入时间戳
        file << format_timestamp(record.timestamp);
        
        // 写入变量值
        for (size_t i = 1; i < column_names.size(); ++i) {
            file << ",";
            auto it = record.variable_values.find(column_names[i]);
            if (it != record.variable_values.end()) {
                file << it->second;
            } else {
                file << "";
            }
        }
        file << "\n";
    }
    
    file.close();
    std::cout << "Table records saved to CSV: " << filename 
              << " (" << records.size() << " records)" << std::endl;
    return true;
}

bool DataRecorder::save_records_to_xml(const std::vector<TableRecord>& records, const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Failed to open file: " << filename << std::endl;
        return false;
    }
    
    // XML头部
    file << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    file << "<industrial_data_table>\n";
    
    // 写入每条记录
    for (const auto& record : records) {
        file << "  <record>\n";
        
        // 时间戳
        file << "    <timestamp>" << format_timestamp(record.timestamp) << "</timestamp>\n";
        
        // 变量值
        for (const auto& pair : record.variable_values) {
            file << "    <" << pair.first << ">" << pair.second << "</" << pair.first << ">\n";
        }
        
        file << "  </record>\n";
    }
    
    file << "</industrial_data_table>\n";
    file.close();
    
    std::cout << "Table records saved to XML: " << filename 
              << " (" << records.size() << " records)" << std::endl;
    return true;
}

std::string DataRecorder::format_timestamp(const std::chrono::system_clock::time_point& timestamp) {
    auto time_t = std::chrono::system_clock::to_time_t(timestamp);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        timestamp.time_since_epoch()) % 1000;
    
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << "." << std::setfill('0') << std::setw(3) << ms.count();
    return ss.str();
}

void DataRecorder::high_frequency_sampling_thread() {
    // 动态计算采样间隔，确保采样频率不高于保存频率
    // 采样间隔应该等于或大于数据保存间隔，避免无效的高频采样
    auto sampling_interval = std::max(m_data_save_interval, std::chrono::milliseconds(1));
    
    // 为了减少线程调度压力，设置最小采样间隔为1ms
    sampling_interval = std::max(sampling_interval, std::chrono::milliseconds(1));
    
    std::cout << "Sampling thread started with interval: " << sampling_interval.count() << "ms" << std::endl;
    
    // 设置线程栈大小和优先级（Windows特定）
    try {
        while (m_recording.load()) {
            auto cycle_start = std::chrono::steady_clock::now();
            
            // 检查是否到了数据保存时间（使用微秒精度以支持1ms高频采样）
            auto time_since_last_save = std::chrono::duration_cast<std::chrono::microseconds>(
                cycle_start - m_last_save_time);
            
            // 将保存间隔转换为微秒进行比较，确保1.x毫秒的精确判断
            auto save_interval_microseconds = std::chrono::duration_cast<std::chrono::microseconds>(m_data_save_interval);
                
            if (time_since_last_save >= save_interval_microseconds) {
                try {
                    // 收集当前数据
                    TableRecord record = collect_current_data();
                    
                    // 添加到活跃缓冲区
                    {
                        std::lock_guard<std::mutex> lock(m_buffer_mutex);
                        
                        // 如果缓冲区满了，移除最旧的记录
                        if (m_active_buffer->size() >= m_buffer_size) {
                            m_active_buffer->erase(m_active_buffer->begin());
                        }
                        
                        m_active_buffer->push_back(std::move(record));
                    }
                    
                    // 更新上次保存时间
                    m_last_save_time = cycle_start;
                    
                    // 检查是否需要触发文件保存
                    if (should_trigger_save()) {
                        switch_buffers();
                    }
                } catch (const std::exception& e) {
                    std::cerr << "Error in data collection: " << e.what() << std::endl;
                    // 继续运行，不中断线程
                }
            }
            
            // 精确控制采样周期，使用更精确的时间控制
            auto cycle_end = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::microseconds>(cycle_end - cycle_start);
            auto sleep_time = std::chrono::duration_cast<std::chrono::microseconds>(sampling_interval) - elapsed;
            
            if (sleep_time > std::chrono::microseconds(0)) {
                std::this_thread::sleep_for(sleep_time);
            } else {
                // 如果处理时间超过了采样间隔，让出CPU时间片
                std::this_thread::yield();
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "Critical error in sampling thread: " << e.what() << std::endl;
        m_recording.store(false);  // 停止记录以防止进一步错误
    }
}
 


DataRecorder::TableRecord DataRecorder::collect_current_data() {
    TableRecord record;
    record.timestamp = std::chrono::system_clock::now();
    
    std::lock_guard<std::mutex> lock(m_pointers_mutex);
    
    // 通过指针链表快速收集数据
    for (const auto& var_ptr : m_save_pointers) {
        std::string value = convert_value_to_string(var_ptr.data_ptr, var_ptr.type_name);
        record.variable_values[var_ptr.name] = value;
    }
    
    return record;
}

void DataRecorder::update_save_pointers() {
    std::lock_guard<std::mutex> lock(m_pointers_mutex);
    m_save_pointers.clear();
    
    auto variables = m_variable_system.list_variables();
    
    for (const auto& var : variables) {
        if (var.should_save) {
            void* data_ptr = m_variable_system.get_variable_data_pointer(var.name);
            if (data_ptr != nullptr) {
                m_save_pointers.emplace_back(var.name, data_ptr, var.type);
            }
        }
    }
    
    std::cout << "Updated save pointers: " << m_save_pointers.size() << " variables" << std::endl;
}

std::string DataRecorder::convert_value_to_string(void* ptr, const std::string& type_name) {
    if (ptr == nullptr) {
        return "null";
    }
    
    std::stringstream ss;
    
    try {
        if (type_name == "int") {
            ss << *static_cast<int*>(ptr);
        } else if (type_name == "double") {
            ss << std::fixed << std::setprecision(6) << *static_cast<double*>(ptr);
        } else if (type_name == "float") {
            ss << std::fixed << std::setprecision(3) << *static_cast<float*>(ptr);
        } else if (type_name == "bool") {
            ss << (*static_cast<bool*>(ptr) ? "true" : "false");
        } else if (type_name == "string") {
            // 对于string类型，ptr指向的是std::shared_ptr<std::string>的get()返回值
            // 即直接指向std::string对象，需要额外的安全检查
            std::string* str_ptr = static_cast<std::string*>(ptr);
            if (str_ptr != nullptr) {
                ss << *str_ptr;
            } else {
                ss << "null_string";
            }
        } else {
            ss << "unknown_type";
        }
    } catch (const std::exception& e) {
        ss << "conversion_error_" << type_name;
    } catch (...) {
        ss << "unknown_error_" << type_name;
    }
    
    return ss.str();
}



void DataRecorder::set_file_splitting(size_t max_records_per_file, const std::string& base_filename) {
    m_max_records_per_file = max_records_per_file;
    m_base_filename = base_filename;
    m_current_file_records = 0;
    m_file_sequence = 1;
    std::cout << "File splitting configured: max " << max_records_per_file 
              << " records per file, base name: " << base_filename << std::endl;
}

void DataRecorder::set_auto_save(bool enabled, int interval_ms) {
    if (m_auto_save_enabled && !enabled) {
        // 停止自动保存
        m_auto_save_running = false;
        if (m_auto_save_thread.joinable()) {
            m_auto_save_thread.join();
        }
    }
    
    m_auto_save_enabled = enabled;
    m_auto_save_interval = std::chrono::milliseconds(interval_ms);
    
    if (enabled && !m_auto_save_running) {
        // 启动自动保存线程
        m_auto_save_running = true;
        m_auto_save_thread = std::thread(&DataRecorder::auto_save_thread_function, this);
    }
    
    std::cout << "Auto save " << (enabled ? "enabled" : "disabled") 
              << " with interval: " << interval_ms << "ms" << std::endl;
}

bool DataRecorder::save_with_splitting(const std::string& format) {
    std::lock_guard<std::mutex> lock(m_buffer_mutex);
    
    if (m_active_buffer->empty()) {
        std::cout << "No records to save." << std::endl;
        return false;
    }
    
    // 检查是否需要分割文件
    size_t records_to_save = m_active_buffer->size();
    bool success = true;
    
    while (records_to_save > 0 && success) {
        size_t records_in_this_file = std::min(records_to_save, 
                                               m_max_records_per_file - m_current_file_records);
        
        // 生成文件名
        std::string filename = generate_filename(format);
        
        // 创建临时记录向量
        std::vector<TableRecord> temp_records;
        size_t start_index = m_active_buffer->size() - records_to_save;
        temp_records.assign(m_active_buffer->begin() + start_index, 
                           m_active_buffer->begin() + start_index + records_in_this_file);
        
        // 保存文件
        if (format == "xml") {
            success = save_records_to_xml(temp_records, filename);
        } else {
            success = save_records_to_csv(temp_records, filename);
        }
        
        if (success) {
            m_current_file_records += records_in_this_file;
            records_to_save -= records_in_this_file;
            
            // 如果当前文件已满，准备下一个文件
            if (m_current_file_records >= m_max_records_per_file) {
                m_file_sequence++;
                m_current_file_records = 0;
            }
        }
    }
    
    return success;
}

std::string DataRecorder::generate_filename(const std::string& format) {
    std::stringstream ss;
    ss << m_base_filename;
    
    if (m_file_sequence > 1) {
        ss << "_part" << std::setfill('0') << std::setw(3) << m_file_sequence;
    }
    
    // 添加时间戳
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    ss << "_" << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S");
    
    ss << "." << format;
    return ss.str();
}

void DataRecorder::set_high_frequency_sampling(int data_save_interval_ms) {
    m_data_save_interval = std::chrono::milliseconds(data_save_interval_ms);
    std::cout << "Data save interval set to: " << data_save_interval_ms << "ms" << std::endl;
}

void DataRecorder::set_buffer_trigger_conditions(size_t max_records, int max_time_ms) {
    m_max_records_trigger = max_records;
    m_max_time_trigger = std::chrono::milliseconds(max_time_ms);
    std::cout << "Buffer trigger conditions set to: " << max_records << " records or " 
              << max_time_ms << "ms" << std::endl;
}

bool DataRecorder::should_trigger_save() const {
    std::lock_guard<std::mutex> lock(m_buffer_mutex);
    
    // 检查记录数阈值
    if (m_active_buffer->size() >= m_max_records_trigger) {
        return true;
    }
    
    // 检查时间阈值
    auto current_time = std::chrono::steady_clock::now();
    auto buffer_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        current_time - m_buffer_start_time);
    
    if (buffer_duration >= m_max_time_trigger) {
        return true;
    }
    
    return false;
}

void DataRecorder::switch_buffers() {
    std::lock_guard<std::mutex> lock(m_buffer_mutex);
    
    // 如果当前活跃缓冲区为空，不需要切换
    if (m_active_buffer->empty()) {
        return;
    }
    
    // 将当前活跃缓冲区的数据加入保存队列
    {
        std::lock_guard<std::mutex> save_lock(m_file_save_mutex);
        m_save_queue.push(*m_active_buffer);
    }
    
    // 切换缓冲区
    if (m_using_buffer_a.load()) {
        m_active_buffer = &m_buffer_b;
        m_saving_buffer = &m_buffer_a;
        m_using_buffer_a.store(false);
    } else {
        m_active_buffer = &m_buffer_a;
        m_saving_buffer = &m_buffer_b;
        m_using_buffer_a.store(true);
    }
    
    // 清空新的活跃缓冲区
    m_active_buffer->clear();
    
    // 重置缓冲区开始时间
    m_buffer_start_time = std::chrono::steady_clock::now();
    
    // 通知文件保存线程
    m_save_condition.notify_one();
    
    std::cout << "Buffer switched. Queue size: " << m_save_queue.size() << std::endl;
}

void DataRecorder::file_save_thread_function() {
    while (m_file_save_running.load()) {
        std::unique_lock<std::mutex> lock(m_file_save_mutex);
        
        // 等待有数据需要保存
        m_save_condition.wait(lock, [this] { 
            return !m_save_queue.empty() || !m_file_save_running.load(); 
        });
        
        // 如果线程被要求停止，退出
        if (!m_file_save_running.load()) {
            break;
        }
        
        // 处理保存队列中的数据
        while (!m_save_queue.empty()) {
            auto data_to_save = m_save_queue.front();
            m_save_queue.pop();
            lock.unlock();
            
            // 执行文件保存（不持有锁）
            if (!data_to_save.empty()) {
                std::string filename = generate_filename("csv");
                save_records_to_csv(data_to_save, filename);
                std::cout << "Saved " << data_to_save.size() << " records to " << filename << std::endl;
            }
            
            lock.lock();
        }
    }
}

void DataRecorder::auto_save_thread_function() {
    while (m_auto_save_running.load()) {
        auto start_time = std::chrono::steady_clock::now();
        
        // 执行自动保存
        if (m_recording.load() && get_record_count() > 0) {
            save_with_splitting("csv");
        }
        
        // 计算睡眠时间
        auto end_time = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        auto sleep_time = m_auto_save_interval - elapsed;
        
        if (sleep_time > std::chrono::milliseconds(0)) {
            std::this_thread::sleep_for(sleep_time);
        }
    }
}