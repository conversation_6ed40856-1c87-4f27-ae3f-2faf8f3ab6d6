{"driver_info": {"name": "E5000_V2", "version": "2.0.0", "vendor": "KEWELL TECHNOLOGY CO.,LTD.", "description": "High precision electronic load driver with multi-instance support", "dll_filename": "E5000_V2.dll", "device_type": "ELECTRONIC_LOAD", "supports_multi_instance": true}, "capabilities": {"max_instances": 4, "supports_hot_plug": true, "supports_calibration": true, "auto_discovery": false}, "error_handling": {"auto_recovery": true, "error_logging_enabled": true}, "instances": [{"instance_id": "EthClient_01", "device_index": 0, "display_name": "Ethernet Client Unit 1 (TCP)", "communication": {"interface_type": "TCP", "ip_address": "*************", "port": 8001, "device_id": 1, "timeout_ms": 1000, "retry_count": 3}, "memory_base_offset": 0, "enabled": true, "custom_parameters": {"serial_number": "EthClient001", "calibration_date": "2024-01-15", "max_power": 6000.0, "max_current": 240.0, "max_voltage": 500.0}}, {"instance_id": "EthClient_02", "device_index": 1, "display_name": "Ethernet Client Unit 2 (TCP)", "communication": {"interface_type": "TCP", "ip_address": "*************", "port": 8002, "device_id": 2, "timeout_ms": 1000, "retry_count": 3}, "memory_base_offset": 256, "enabled": true, "custom_parameters": {"serial_number": "EthClient002", "calibration_date": "2024-01-16", "max_power": 6000.0, "max_current": 240.0, "max_voltage": 500.0}}, {"instance_id": "EthClient_03", "device_index": 2, "display_name": "Ethernet Client Unit 3 (UDP)", "communication": {"interface_type": "UDP", "ip_address": "*************", "port": 9001, "device_id": 3, "timeout_ms": 1000, "retry_count": 3}, "memory_base_offset": 512, "enabled": true, "custom_parameters": {"serial_number": "EthClient003", "calibration_date": "2024-01-17", "max_power": 6000.0, "max_current": 240.0, "max_voltage": 500.0}}, {"instance_id": "EthClient_04", "device_index": 3, "display_name": "Ethernet Client Unit 4 (UDP)", "communication": {"interface_type": "UDP", "ip_address": "*************", "port": 9002, "device_id": 4, "timeout_ms": 1000, "retry_count": 3}, "memory_base_offset": 768, "enabled": true, "custom_parameters": {"serial_number": "EthClient004", "calibration_date": "2024-01-18", "max_power": 6000.0, "max_current": 240.0, "max_voltage": 500.0}}], "command_system": {"categories": {"read_only_control": {"description": "系统只读指令", "access": "read-only", "commands": {"Read": {"function_name": "Read", "parameter_type": "string", "valid_values": ["ReadAll"], "description": "读取系统只读指令，常见于自由驱动的外部数据读取，内部获得数据库自行解析。", "response_time_ms": 100}}}, "write_only_control": {"description": "系统写入指令", "access": "write-only", "commands": {"Write": {"function_name": "Write", "parameter_type": "hexstring", "valid_values": "^[0-9A-Fa-f]+$", "description": "写入系统控制指令，常见于自由驱动的外部数据写入，DLL内部发送hexstring转16进制数据发送，（像串口调试助手）。", "response_time_ms": 100}}}}, "command_execution": {"timeout_ms": 5000, "retry_count": 3, "retry_delay_ms": 100}, "validation": {"parameter_checking": true, "range_validation": true, "type_validation": true}}}