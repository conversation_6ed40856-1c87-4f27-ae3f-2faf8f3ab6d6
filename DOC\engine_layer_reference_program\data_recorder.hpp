#ifndef DATA_RECORDER_HPP
#define DATA_RECORDER_HPP

#include <vector>
#include <string>
#include <chrono>
#include <memory>
#include <unordered_map>
#include <fstream>
#include <thread>
#include <atomic>
#include <mutex>
#include <queue>
#include <condition_variable>
#include "variable_system.hpp"

/**
 * @brief 数据记录器类 - 表格式数据记录
 * 
 * 该类负责按照数据库表格形式记录变量数据，支持CSV和XML格式导出。
 * 使用指针链表优化数据访问性能，只记录设置为保存状态的变量。
 */
class DataRecorder {
public:
    /**
     * @brief 表格记录结构
     */
    struct TableRecord {
        std::chrono::system_clock::time_point timestamp;
        std::unordered_map<std::string, std::string> variable_values; // 变量名 -> 值的字符串表示
        
        TableRecord() = default;
    };
    
    /**
     * @brief 构造函数
     * @param var_system 变量系统引用
     * @param buffer_size 数据缓冲区大小，默认10000条记录
     */
    explicit DataRecorder(VariableSystem& var_system, size_t buffer_size = 10000);
    
    /**
     * @brief 析构函数
     */
    ~DataRecorder();
    
    /**
     * @brief 设置变量的保存状态
     * @param variable_name 变量名
     * @param should_save 是否保存该变量
     * @return true 设置成功，false 变量不存在
     */
    bool set_variable_save_status(const std::string& variable_name, bool should_save);
    
    /**
     * @brief 获取当前设置为保存的变量列表
     * @return 保存变量名列表
     */
    std::vector<std::string> get_saveable_variables() const;
    
    /**
     * @brief 开始数据记录
     * @param interval 记录间隔
     */
    void start_recording(std::chrono::milliseconds interval);
    
    /**
     * @brief 停止数据记录
     */
    void stop_recording();
    
    /**
     * @brief 获取已记录的数据条数
     * @return 记录条数
     */
    size_t get_record_count() const;
    
    /**
     * @brief 清空所有记录
     */
    void clear_records();
    
    /**
     * @brief 收集当前时刻的变量数据
     * @return 表格记录
     */
    TableRecord collect_current_data();
    
    /**
     * @brief 设置文件分割参数
     * @param max_records_per_file 每个文件的最大记录数
     * @param base_filename 基础文件名（不含扩展名）
     */
    void set_file_splitting(size_t max_records_per_file, const std::string& base_filename);
    
    /**
     * @brief 启用/禁用自动保存
     * @param enabled 是否启用
     * @param interval_ms 自动保存间隔（毫秒）
     */
    void set_auto_save(bool enabled, int interval_ms = 5000);
    
    /**
     * @brief 设置高频采集参数
     * @param data_save_interval_ms 数据保存间隔（毫秒）
     */
    void set_high_frequency_sampling(int data_save_interval_ms);
    
    /**
     * @brief 设置缓冲区触发条件
     * @param max_records 最大记录数阈值
     * @param max_time_ms 最大时间阈值（毫秒）
     */
    void set_buffer_trigger_conditions(size_t max_records, int max_time_ms);
    
    /**
     * @brief 手动触发数据保存（支持文件分割）
     * @param format 保存格式（"csv" 或 "xml"）
     * @return 是否保存成功
     */
    bool save_with_splitting(const std::string& format = "csv");

private:
    /**
     * @brief 变量指针信息结构
     */
    struct VariablePointer {
        std::string name;
        void* data_ptr;
        std::string type_name;
        
        VariablePointer(const std::string& n, void* ptr, const std::string& type)
            : name(n), data_ptr(ptr), type_name(type) {}
    };
    
    VariableSystem& m_variable_system;           ///< 变量系统引用
    
    // 双缓冲区机制
    std::vector<TableRecord> m_buffer_a;         ///< 缓冲区A
    std::vector<TableRecord> m_buffer_b;         ///< 缓冲区B
    std::atomic<bool> m_using_buffer_a;          ///< 当前使用缓冲区A标志
    std::vector<TableRecord>* m_active_buffer;   ///< 当前活跃缓冲区指针
    std::vector<TableRecord>* m_saving_buffer;   ///< 当前保存缓冲区指针
    
    std::vector<VariablePointer> m_save_pointers; ///< 需要保存的变量指针链表

    // 线程控制
    std::atomic<bool> m_recording;               ///< 记录状态标志
    std::unique_ptr<std::thread> m_record_thread; ///< 1ms高频采集线程
    std::unique_ptr<std::thread> m_file_save_thread; ///< 文件保存线程
    
    // 采集参数
    std::chrono::milliseconds m_data_save_interval; ///< 数据保存间隔
    std::chrono::steady_clock::time_point m_last_save_time; ///< 上次保存时间
    
    // 互斥锁
    mutable std::mutex m_buffer_mutex;           ///< 缓冲区切换互斥锁
    mutable std::mutex m_pointers_mutex;         ///< 指针链表互斥锁
    mutable std::mutex m_file_save_mutex;        ///< 文件保存互斥锁

    size_t m_buffer_size;                       ///< 单个缓冲区大小
    
    // 触发条件
    size_t m_max_records_trigger;               ///< 记录数触发阈值
    std::chrono::milliseconds m_max_time_trigger; ///< 时间触发阈值
    std::chrono::steady_clock::time_point m_buffer_start_time; ///< 缓冲区开始时间
    
    // 文件分割和优化相关成员
    size_t m_max_records_per_file;     ///< 每个文件的最大记录数
    size_t m_current_file_records;     ///< 当前文件的记录数
    int m_file_sequence;               ///< 文件序列号
    std::string m_base_filename;       ///< 基础文件名
    bool m_auto_save_enabled;          ///< 是否启用自动保存
    std::chrono::milliseconds m_auto_save_interval; ///< 自动保存间隔
    
    /**
     * @brief 1ms高频采集线程函数
     */
    void high_frequency_sampling_thread();
    
    /**
     * @brief 文件保存线程函数
     */
    void file_save_thread_function();
    
    /**
     * @brief 切换缓冲区
     */
    void switch_buffers();
    
    /**
     * @brief 检查是否需要触发保存
     * @return true 需要保存，false 不需要保存
     */
    bool should_trigger_save() const;
    
    /**
     * @brief 更新保存变量的指针链表
     */
    void update_save_pointers();
    
    /**
     * @brief 将任意类型值转换为字符串
     * @param ptr 数据指针
     * @param type_name 类型名称
     * @return 字符串表示
     */
    std::string convert_value_to_string(void* ptr, const std::string& type_name);
    
    /**
     * @brief 格式化时间戳为字符串
     * @param time_point 时间点
     * @return 格式化的时间字符串
     */
    std::string format_timestamp(const std::chrono::system_clock::time_point& time_point);
    
    /**
     * @brief 生成带序列号的文件名
     * @param format 文件格式（"csv" 或 "xml"）
     * @return 完整的文件名
     */
    std::string generate_filename(const std::string& format);
    
    /**
     * @brief 自动保存线程函数
     */
    void auto_save_thread_function();
    
    /**
     * @brief 保存记录到CSV文件（内部方法）
     * @param records 要保存的记录
     * @param filename 文件名
     * @return 是否保存成功
     */
    bool save_records_to_csv(const std::vector<TableRecord>& records, const std::string& filename);
    
    /**
     * @brief 保存记录到XML文件（内部方法）
     * @param records 要保存的记录
     * @param filename 文件名
     * @return 是否保存成功
     */
    bool save_records_to_xml(const std::vector<TableRecord>& records, const std::string& filename);
    
    // 自动保存相关成员
    std::thread m_auto_save_thread;        ///< 自动保存线程
    std::atomic<bool> m_auto_save_running; ///< 自动保存线程运行标志
    std::atomic<bool> m_file_save_running; ///< 文件保存线程运行标志
    
    // 文件保存队列
    std::queue<std::vector<TableRecord>> m_save_queue; ///< 待保存数据队列
    std::condition_variable m_save_condition;          ///< 保存条件变量
};

#endif // DATA_RECORDER_HPP