C:\mingw64\bin\cmake.exe -E rm -f CMakeFiles\EngineFrameworkInterface.dir/objects.a
C:\mingw64\bin\ar.exe qc CMakeFiles\EngineFrameworkInterface.dir/objects.a @CMakeFiles\EngineFrameworkInterface.dir\objects1.rsp
C:\mingw64\bin\g++.exe -shared -o bin\libEngineFrameworkInterface.dll -Wl,--out-implib,lib\libEngineFrameworkInterface.dll.a -Wl,--major-image-version,0,--minor-image-version,0 -Wl,--whole-archive CMakeFiles\EngineFrameworkInterface.dir/objects.a -Wl,--no-whole-archive @CMakeFiles\EngineFrameworkInterface.dir\linkLibs.rsp
