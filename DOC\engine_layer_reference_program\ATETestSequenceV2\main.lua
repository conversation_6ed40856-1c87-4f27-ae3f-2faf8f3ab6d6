-- ============================================================================
-- ATE测试系统主序列 (一级序列)
-- 文件: main.lua
-- 描述: 主测试序列，采用step-based工步控制框架
-- ============================================================================

-- 工步控制变量
local step = 0
main_run_ctrl = { 1, 1, 1, 1, 1, 0, 0, 0, 0, 0 } -- 控制各工步是否执行
main_fail_ctrl = { 1, 1, 1, 1, 1, 0, 0, 0, 0, 0 } -- 控制各工步异常后执行状态 0 异常则停止 1 异常后继续后面
main_fail_cycle = { 1, 2, 2, 1, 0, 0, 0, 0, 0, 0 } -- 控制各工步异常后再尝试次数 0 表示不循环
main_fail_cycle_backup = { } -- 控制各工步异常后再尝试次数 备份用
main_fail_cycle_backup = {table.unpack(main_fail_cycle)}
local test_result = "UNKNOWN"
local error_status = {}

-- 全局变量定义 (通过Lua全局变量系统自动在C++中申请)
test_status = "IDLE" -- 测试状态
test_start_time = 0  -- 测试开始时间
test_end_time = 0    -- 测试结束时间
current_step = 0     -- 当前测试步骤
total_steps = 5      -- 总测试步骤数
error_count = 0      -- 错误计数
warning_count = 0    -- 警告计数

-- 设备状态变量
device_initialized = false          -- 设备初始化状态
device_connect_result = false       -- 设备连接结果
power_init_result = ""              -- 电源初始化结果
env_init_result = ""                -- 环境箱初始化结果
dll_init_result = false             -- DLL初始化结果
power_supply_ready = false          -- 电源准备状态
environmental_chamber_ready = false -- 环境箱准备状态

-- 设备通道配置 (全局变量，可被子序列访问)
device_channels = {
    power_supply = 1,          -- 电源通道
    electronic_load = 2,       -- 电子负载通道
    oscilloscope = 3,          -- 示波器通道
    multimeter = 4,            -- 万用表通道
    environmental_chamber = 5, -- 环境箱通道
    power_analyzer_input = 6,  -- 输入侧功率分析仪
    power_analyzer_output = 7, -- 输出侧功率分析仪
    thermal_sensor = 8,        -- 温度传感器
    v2g_communication = 9,     -- V2G通信模块
    plc_modem = 10             -- PLC调制解调器
}

-- 测试配置参数
test_config = {
    max_retry_count = 3,  -- 最大重试次数
    timeout_seconds = 30, -- 超时时间(秒)
    log_level = "INFO",   -- 日志级别
    auto_cleanup = true   -- 自动清理
}

-- 固定功能begin
function add_string(str)
    if not error_status[str] then
        error_status[str] = true
        return true
    end
    return false
end

-- 工步保护状态检查
function seq_protect_check()
    -- 检查系统状态和错误条件
    if error_count >= test_config.max_retry_count then
        add_string("ErrorCountExceeded")
        return false
    end

    -- 检查设备连接状态
    if device_initialized and not dll_check_device_status() then
        add_string("DeviceConnectionLost")
        return false
    end

    return true
end

-- 工步保护动作执行
function seq_protect_act()
    if next(error_status) ~= nil then
        for error_key, _ in pairs(error_status) do
            log_message("ERROR", "保护动作触发: " .. error_key)

            if error_key == "ErrorCountExceeded" then
                test_result = "ABORTED"
                return true -- 停止执行
            elseif error_key == "DeviceConnectionLost" then
                -- 尝试重新连接
                if dll_reconnect_devices() then
                    error_status[error_key] = nil -- 清除错误状态
                    return false                  -- 继续执行
                else
                    test_result = "FAILED"
                    return true -- 停止执行
                end
            end
        end
    end

    return false -- 默认继续执行
end

-- 恢复初始循环次数
function main_fail_cycle_reset(step)
    main_fail_cycle[step] = main_fail_cycle_backup[step]
end
-- 固定功能end

-- 工具函数定义
function log_message(level, message)
    local timestamp = os.date("%Y-%m-%d %H:%M:%S")
    print(string.format("[%s] %s: %s", timestamp, level, message))
end

function update_progress(step, total)
    current_step = step
    total_steps = total
    local progress = math.floor((step / total) * 100)
    log_message("INFO", string.format("测试进度: %d/%d (%d%%)", step, total, progress))
end

-- ============================================================================
-- INIT 标签 - 初始化阶段
-- ============================================================================
::init::
-- 做一些初始化动作，当前序列执行需要得到其他序列或者全局变量的一些值初始化等等
log_message("INFO", "=== 开始预处理阶段 ===")
test_status = "INITIALIZING"
test_start_time = os.time()
error_count = 0
warning_count = 0

-- 系统自检
log_message("INFO", "执行系统自检...")

-- 初始化DLL接口
log_message("INFO", "初始化DLL接口...")
dll_init_result = dll_initialize()
if not dll_init_result then
    log_message("ERROR", "DLL接口初始化失败")
    error_count = error_count + 1
    test_result = "FAILED"
    goto final
end

-- 连接设备
log_message("INFO", "连接测试设备...")
device_connect_result = dll_connect_all_devices()
if not device_connect_result then
    log_message("ERROR", "设备连接失败")
    error_count = error_count + 1
    test_result = "FAILED"
    goto final
end
device_initialized = true
-- 初始化电源系统
log_message("INFO", "初始化电源系统...")
power_init_result = dll_send_command(main.device_channels.power_supply, "INIT")
if power_init_result == "OK" then
    power_supply_ready = true
    log_message("INFO", "电源系统初始化成功")
else
    log_message("ERROR", "电源系统初始化失败")
    error_count = error_count + 1
end

-- 初始化环境箱
log_message("INFO", "初始化环境箱...")
env_init_result = dll_send_command(main.device_channels.environmental_chamber, "INIT")
if env_init_result == "OK" then
    environmental_chamber_ready = true
    log_message("INFO", "环境箱初始化成功")
else
    log_message("WARNING", "环境箱初始化失败，将跳过环境相关测试")
    warning_count = warning_count + 1
end

-- 检查初始化结果
if not device_initialized or not power_supply_ready then
    log_message("ERROR", "关键设备初始化失败，无法继续测试")
    test_result = "FAILED"
    goto final
end

log_message("INFO", "=== 预处理阶段完成 ===")

-- ============================================================================
-- MAIN 标签 - 主测试序列阶段
-- ============================================================================
step = 1
test_status = "RUNNING"
log_message("INFO", "=== 开始主测试序列阶段 ===")
::main::
-- 需要执行中通过公式二次计算的量在这里，通过公式计算再次生效

-- 工步1: 执行波形基测 (开机关机波形)
if main_run_ctrl[step] ~= 0 then -- 当前编号动作执行
    log_message("INFO", "工步1: 执行波形基测...")
    update_progress(1, total_steps)
    dofile("ATETestSequenceV2/waveform_basic_test.lua")

    -- 条件判断***固定格式***
    if waveform_basic_test.test_result == "PASS" then
        log_message("INFO", "工步1: 波形基测通过")
        step = step + 1
    else
        log_message("ERROR", "工步1: 波形基测失败")
        test_result = "FAILED"
        if main_fail_cycle[step] > 0 then -- 异常后有循环次数
            main_fail_cycle[step] = main_fail_cycle[step] - 1
            goto main
        elseif main_fail_cycle[step] <= 0 then
            main_fail_cycle_reset(step)
            if main_fail_ctrl[step] == 0 then -- 异常后停止
                goto final
            elseif main_fail_ctrl[step] == 1 then -- 异常后继续后面
                step = step + 1
            end
        end
    end
end

-- 工步2: 执行充电效率测试 (≥90%额定效率)
if main_run_ctrl[step] ~= 0 then -- 当前编号动作执行
    log_message("INFO", "工步2: 执行充电效率测试...")
    update_progress(2, total_steps)
    dofile("ATETestSequenceV2/charging_efficiency_test.lua")

    -- 条件判断
    if charging_efficiency_test.test_result == "PASS" then
        log_message("INFO", "工步2: 充电效率测试通过")
        step = step + 1
    else
        log_message("ERROR", "工步2: 充电效率测试失败")
        test_result = "FAILED"
            if main_fail_cycle[step] > 0 then -- 异常后有循环次数
            main_fail_cycle[step] = main_fail_cycle[step] - 1
            goto main
        elseif main_fail_cycle[step] <= 0 then
            main_fail_cycle_reset(step)
            if main_fail_ctrl[step] == 0 then -- 异常后停止
                goto final
            elseif main_fail_ctrl[step] == 1 then -- 异常后继续后面
                step = step + 1
            end
        end
    end
end

-- 工步3: 执行双向充放电 (V2G) 测试
if main_run_ctrl[step] ~= 0 then -- 当前编号动作执行
    log_message("INFO", "工步3: 执行双向充放电测试...")
    update_progress(3, total_steps)
    dofile("ATETestSequenceV2/bidirectional_charging_test.lua")
    delayms(1000) -- 等待1秒

    -- 条件判断
    if bidirectional_charging_test.test_result == "PASS" then
        log_message("INFO", "工步3: 双向充放电测试通过")
        step = step + 1
    else
        log_message("ERROR", "工步3: 双向充放电测试失败")
        test_result = "FAILED"
            if main_fail_cycle[step] > 0 then -- 异常后有循环次数
            main_fail_cycle[step] = main_fail_cycle[step] - 1
            goto main
        elseif main_fail_cycle[step] <= 0 then
            main_fail_cycle_reset(step)
            if main_fail_ctrl[step] == 0 then -- 异常后停止
                goto final
            elseif main_fail_ctrl[step] == 1 then -- 异常后继续后面
                step = step + 1
            end
        end
    end
end

-- 工步4: 执行温度循环测试
if main_run_ctrl[step] ~= 0 then     -- 当前编号动作执行
    log_message("INFO", "工步4: 执行温度循环测试...")
    update_progress(4, total_steps)
    dofile("ATETestSequenceV2/temperature_cycle_test.lua")
    delayms(1000)     -- 等待1秒

    -- 条件判断
    if temperature_cycle_test.test_result == "PASS" then
        log_message("INFO", "工步4: 温度循环测试通过")
        step = step + 1
    else
        log_message("ERROR", "工步4: 温度循环测试失败")
        test_result = "FAILED"
            if main_fail_cycle[step] > 0 then -- 异常后有循环次数
            main_fail_cycle[step] = main_fail_cycle[step] - 1
            goto main
        elseif main_fail_cycle[step] <= 0 then
            main_fail_cycle_reset(step)
            if main_fail_ctrl[step] == 0 then -- 异常后停止
                goto final
            elseif main_fail_ctrl[step] == 1 then -- 异常后继续后面
                step = step + 1
            end
        end
    end
end

-- 工步执行完成
log_message("INFO", "=== 主测试序列阶段完成 ===")

-- 后处理阶段
-- ============================================================================
-- FINAL 标签 - 后处理阶段
-- ============================================================================
::final::
-- 一些必要的关闭动作
-- 一些必要的报告数据计算
-- 当前序列测试结果的报告内容输出，输出到json
log_message("INFO", "=== 开始后处理阶段 ===")
test_status = "FINALIZING"
test_end_time = os.time()

-- 生成测试报告
log_message("INFO", "生成测试报告...")
local test_duration = test_end_time - test_start_time
local report = {
    test_name = "ATE主测试序列",
    test_result = test_result,
    test_duration = test_duration,
    error_count = error_count,
    warning_count = warning_count,
    total_steps = total_steps,
    completed_steps = step - 1,
    timestamp = os.date("%Y-%m-%d %H:%M:%S", test_end_time),
    error_status = next(error_status) and error_status or {},
    sub_test_results = {
        waveform_basic_test = main_run_ctrl[1] ~= 0 and waveform_basic_test.test_result or "SKIPPED",
        charging_efficiency_test = main_run_ctrl[2] ~= 0 and charging_efficiency_test.test_result or "SKIPPED",
        bidirectional_charging_test = main_run_ctrl[3] ~= 0 and bidirectional_charging_test.test_result or "SKIPPED",
        temperature_cycle_test = main_run_ctrl[4] ~= 0 and temperature_cycle_test.test_result or "SKIPPED"
    }
}

-- 写入测试报告到JSON文件
local function write_json_report()
    -- 构建错误状态列表字符串
    local error_list_str = ""
    if next(error_status) ~= nil then
        local error_list = {}
        for error_key, _ in pairs(error_status) do
            table.insert(error_list, error_key)
        end
        error_list_str = table.concat(error_list, ", ")
    end
    
    -- 构建子测试结果JSON字符串
    local sub_test_json_parts = {}
    for test_name, result in pairs(report.sub_test_results) do
        table.insert(sub_test_json_parts, string.format('\"%s\":\"%s\"', test_name, result))
    end
    local sub_test_json = "{" .. table.concat(sub_test_json_parts, ",") .. "}"
    
    -- 调用C++接口写入JSON文件
    write_test_report_json(
        -- json_file_path, 文件名由C++接口生成
        report.test_result,
        report.test_duration,
        report.completed_steps,
        report.total_steps,
        report.error_count,
        report.warning_count,
        report.timestamp,
        error_list_str,
        sub_test_json
    )
    
    log_message("INFO", "测试报告已写入JSON文件: " .. json_file_path)
end

-- 执行JSON报告写入
write_json_report()

-- 设备清理
if test_config.auto_cleanup and device_initialized then
    log_message("INFO", "执行设备清理...")

    -- 关闭电源输出
    if power_supply_ready then
        dll_send_command(main.device_channels.power_supply, "OUTPUT:OFF")
        log_message("INFO", "电源输出已关闭")
    end

    -- 停止环境箱
    if environmental_chamber_ready then
        dll_send_command(main.device_channels.environmental_chamber, "STOP")
        log_message("INFO", "环境箱已停止")
    end

    -- 断开设备连接
    dll_disconnect_all_devices()
    log_message("INFO", "设备连接已断开")
end

-- 清理DLL资源
dll_cleanup()
log_message("INFO", "DLL资源已清理")

test_status = "COMPLETED"
log_message("INFO", "=== 测试序列执行完成 ===")

-- 返回测试结果
return test_result
