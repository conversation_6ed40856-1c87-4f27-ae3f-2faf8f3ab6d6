cmake_minimum_required(VERSION 3.10)
project(EngineFrameworkInterface)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置Lua路径
set(LUA_DIR "D:/Portable/lua")
include_directories(${LUA_DIR}/include)
link_directories(${LUA_DIR}/lib)

# 创建spdlog interface库（header-only）
add_library(spdlog INTERFACE)
target_include_directories(spdlog INTERFACE third_library/spdlog/include)

# 添加所有源文件
set(SOURCES
    src/data_process.cpp
    src/data_recorder.cpp
    src/device_manager.cpp
    src/engine_core.cpp
    src/engine_framework_interface.cpp
    src/engine_manager.cpp
    src/engine_variable_system.cpp
    src/global_variable_task.cpp
    src/global_variable_service.cpp
    src/logger.cpp
    src/report_process.cpp
    src/global_core.cpp
)

# 创建动态链接库
add_library(EngineFrameworkInterface SHARED ${SOURCES})

# 定义DLL导出宏
target_compile_definitions(EngineFrameworkInterface PRIVATE DRIVER_MANAGER_DLL_EXPORTS)

# 添加头文件搜索路径
target_include_directories(EngineFrameworkInterface PRIVATE 
    include
    third_library/spdlog/include
    third_library
)

# 链接库
# target_link_libraries(EngineFrameworkInterface PRIVATE spdlog ${LUA_DIR}/lib/liblua.a)

target_link_libraries(EngineFrameworkInterface PRIVATE spdlog)

target_link_libraries(EngineFrameworkInterface PRIVATE lua)

# Windows平台特定库
if(WIN32)
    target_link_libraries(EngineFrameworkInterface PRIVATE pdh ws2_32 wsock32)
endif()

# 设置输出目录
set_target_properties(EngineFrameworkInterface PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
)

# Windows DLL导出设置
if(WIN32)
    set_target_properties(EngineFrameworkInterface PROPERTIES
        WINDOWS_EXPORT_ALL_SYMBOLS ON
    )
endif()