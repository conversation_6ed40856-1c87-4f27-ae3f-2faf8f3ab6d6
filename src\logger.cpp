#include "../include/logger.hpp"
#include <spdlog/spdlog.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/basic_file_sink.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/async.h>
#include <filesystem>
#include <iostream>
#include <chrono>
#include <algorithm>

// 静态成员定义
std::map<std::string, std::shared_ptr<Logger>> Logger::s_loggers;
std::mutex Logger::s_loggers_mutex;
Logging::LoggerConfig Logger::s_global_config;
Logging::LogLevel Logger::s_global_level = Logging::LogLevel::INFO;

// 构造函数
Logger::Logger() : m_name("default") {
    m_statistics.start_time = std::chrono::system_clock::now();
    m_last_stats_update = std::chrono::system_clock::now();
}

Logger::Logger(const std::string& name) : m_name(name) {
    m_statistics.start_time = std::chrono::system_clock::now();
    m_last_stats_update = std::chrono::system_clock::now();
}

// 析构函数
Logger::~Logger() {
    shutdown();
}

// 初始化方法
bool Logger::initialize(const Logging::LoggerConfig& config) {
    if (m_initialized) {
        return true;
    }
    
    if (!validate_config(config)) {
        return false;
    }
    
    m_config = config;
    m_name = config.logger_name;
    m_min_level = config.min_level;
    
    try {
        setup_spdlog_logger();
        m_initialized = true;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Logger initialization failed: " << e.what() << std::endl;
        return false;
    }
}

void Logger::shutdown() {
    if (!m_initialized) {
        return;
    }
    
    if (m_spdlog_logger) {
        m_spdlog_logger->flush();
        m_spdlog_logger.reset();
    }
    
    m_initialized = false;
}

// 基本日志记录方法
void Logger::trace(const std::string& message) {
    if (is_enabled(Logging::LogLevel::TRACE) && m_spdlog_logger) {
        std::string formatted_msg = (message);
        m_spdlog_logger->trace(formatted_msg);
        update_statistics(Logging::LogLevel::TRACE);
    }
}

void Logger::debug(const std::string& message) {
    if (is_enabled(Logging::LogLevel::DEBUG) && m_spdlog_logger) {
        std::string formatted_msg = (message);
        m_spdlog_logger->debug(formatted_msg);
        update_statistics(Logging::LogLevel::DEBUG);
    }
}

void Logger::info(const std::string& message) {
    if (is_enabled(Logging::LogLevel::INFO) && m_spdlog_logger) {
        std::string formatted_msg = (message);
        m_spdlog_logger->info(formatted_msg);
        update_statistics(Logging::LogLevel::INFO);
    }
}

void Logger::warn(const std::string& message) {
    if (is_enabled(Logging::LogLevel::WARN) && m_spdlog_logger) {
        std::string formatted_msg = (message);
        m_spdlog_logger->warn(formatted_msg);
        update_statistics(Logging::LogLevel::WARN);
    }
}

void Logger::error(const std::string& message) {
    if (is_enabled(Logging::LogLevel::ERROR) && m_spdlog_logger) {
        std::string formatted_msg = (message);
        m_spdlog_logger->error(formatted_msg);
        update_statistics(Logging::LogLevel::ERROR);
    }
}

void Logger::fatal(const std::string& message) {
    if (is_enabled(Logging::LogLevel::FATAL) && m_spdlog_logger) {
        std::string formatted_msg = (message);
        m_spdlog_logger->critical(formatted_msg);
        update_statistics(Logging::LogLevel::FATAL);
    }
}

// 带源码位置的日志记录方法
void Logger::trace(const std::string& message, const std::string& file, int line, const std::string& function) {
    if (is_enabled(Logging::LogLevel::TRACE) && m_spdlog_logger) {
        std::string formatted_msg = (message);
        formatted_msg += " [" + file + ":" + std::to_string(line) + " in " + function + "]";
        m_spdlog_logger->trace(formatted_msg);
        update_statistics(Logging::LogLevel::TRACE);
    }
}

void Logger::debug(const std::string& message, const std::string& file, int line, const std::string& function) {
    if (is_enabled(Logging::LogLevel::DEBUG) && m_spdlog_logger) {
        std::string formatted_msg = (message);
        formatted_msg += " [" + file + ":" + std::to_string(line) + " in " + function + "]";
        m_spdlog_logger->debug(formatted_msg);
        update_statistics(Logging::LogLevel::DEBUG);
    }
}

void Logger::info(const std::string& message, const std::string& file, int line, const std::string& function) {
    if (is_enabled(Logging::LogLevel::INFO) && m_spdlog_logger) {
        std::string formatted_msg = (message);
        formatted_msg += " [" + file + ":" + std::to_string(line) + " in " + function + "]";
        m_spdlog_logger->info(formatted_msg);
        update_statistics(Logging::LogLevel::INFO);
    }
}

void Logger::warn(const std::string& message, const std::string& file, int line, const std::string& function) {
    if (is_enabled(Logging::LogLevel::WARN) && m_spdlog_logger) {
        std::string formatted_msg = (message);
        formatted_msg += " [" + file + ":" + std::to_string(line) + " in " + function + "]";
        m_spdlog_logger->warn(formatted_msg);
        update_statistics(Logging::LogLevel::WARN);
    }
}

void Logger::error(const std::string& message, const std::string& file, int line, const std::string& function) {
    if (is_enabled(Logging::LogLevel::ERROR) && m_spdlog_logger) {
        std::string formatted_msg = (message);
        formatted_msg += " [" + file + ":" + std::to_string(line) + " in " + function + "]";
        m_spdlog_logger->error(formatted_msg);
        update_statistics(Logging::LogLevel::ERROR);
    }
}

void Logger::fatal(const std::string& message, const std::string& file, int line, const std::string& function) {
    if (is_enabled(Logging::LogLevel::FATAL) && m_spdlog_logger) {
        std::string formatted_msg = (message);
        formatted_msg += " [" + file + ":" + std::to_string(line) + " in " + function + "]";
        m_spdlog_logger->critical(formatted_msg);
        update_statistics(Logging::LogLevel::FATAL);
    }
}

// 通用日志记录方法
void Logger::log(Logging::LogLevel level, const std::string& message) {
    if (!is_enabled(level) || !m_spdlog_logger) {
        return;
    }
    
    std::string formatted_msg = (message);
    spdlog::level::level_enum spdlog_level;
    switch (level) {
        case Logging::LogLevel::TRACE: spdlog_level = spdlog::level::trace; break;
        case Logging::LogLevel::DEBUG: spdlog_level = spdlog::level::debug; break;
        case Logging::LogLevel::INFO: spdlog_level = spdlog::level::info; break;
        case Logging::LogLevel::WARN: spdlog_level = spdlog::level::warn; break;
        case Logging::LogLevel::ERROR: spdlog_level = spdlog::level::err; break;
        case Logging::LogLevel::FATAL: spdlog_level = spdlog::level::critical; break;
        default: spdlog_level = spdlog::level::info; break;
    }
    m_spdlog_logger->log(spdlog_level, formatted_msg);
    update_statistics(level);
}

void Logger::log(Logging::LogLevel level, const std::string& message, const std::string& file, int line, const std::string& function) {
    if (!is_enabled(level) || !m_spdlog_logger) {
        return;
    }
    
    std::string formatted_msg = (message);
    formatted_msg += " [" + file + ":" + std::to_string(line) + " in " + function + "]";
    
    spdlog::level::level_enum spdlog_level;
    switch (level) {
        case Logging::LogLevel::TRACE: spdlog_level = spdlog::level::trace; break;
        case Logging::LogLevel::DEBUG: spdlog_level = spdlog::level::debug; break;
        case Logging::LogLevel::INFO: spdlog_level = spdlog::level::info; break;
        case Logging::LogLevel::WARN: spdlog_level = spdlog::level::warn; break;
        case Logging::LogLevel::ERROR: spdlog_level = spdlog::level::err; break;
        case Logging::LogLevel::FATAL: spdlog_level = spdlog::level::critical; break;
        default: spdlog_level = spdlog::level::info; break;
    }
    m_spdlog_logger->log(spdlog_level, formatted_msg);
    update_statistics(level);
}

// 级别控制
void Logger::set_level(Logging::LogLevel level) {
    m_min_level = level;
    if (m_spdlog_logger) {
        spdlog::level::level_enum spdlog_level;
        switch (level) {
            case Logging::LogLevel::TRACE: spdlog_level = spdlog::level::trace; break;
            case Logging::LogLevel::DEBUG: spdlog_level = spdlog::level::debug; break;
            case Logging::LogLevel::INFO: spdlog_level = spdlog::level::info; break;
            case Logging::LogLevel::WARN: spdlog_level = spdlog::level::warn; break;
            case Logging::LogLevel::ERROR: spdlog_level = spdlog::level::err; break;
            case Logging::LogLevel::FATAL: spdlog_level = spdlog::level::critical; break;
            default: spdlog_level = spdlog::level::info; break;
        }
        m_spdlog_logger->set_level(spdlog_level);
    }
}

Logging::LogLevel Logger::get_level() const {
    return m_min_level;
}

bool Logger::is_enabled(Logging::LogLevel level) const {
    return level >= m_min_level && level >= s_global_level;
}

// 文件操作
bool Logger::flush() {
    if (m_spdlog_logger) {
        try {
            m_spdlog_logger->flush();
            return true;
        } catch (const std::exception&) {
            return false;
        }
    }
    return false;
}

// 调试支持
void Logger::dump_internal_state() const {
    std::cout << "=== Logger Internal State ===" << std::endl;
    std::cout << "Name: " << m_name << std::endl;
    std::cout << "Initialized: " << (m_initialized ? "true" : "false") << std::endl;
    std::cout << "Min Level: " << static_cast<int>(m_min_level) << std::endl;
    std::cout << "Config:" << std::endl;
    std::cout << "  - Console Output: " << (m_config.enable_console_output ? "true" : "false") << std::endl;
    std::cout << "  - File Output: " << (m_config.enable_file_output ? "true" : "false") << std::endl;
    std::cout << "  - Log Directory: " << m_config.log_directory << std::endl;
    std::cout << "  - Log File Name: " << m_config.log_file_name << std::endl;
    std::cout << "Statistics:" << std::endl;
    std::cout << "  - Total Messages: " << m_statistics.total_messages << std::endl;
    std::cout << "============================" << std::endl;
}

// 静态方法
std::shared_ptr<Logger> Logger::get_logger(const std::string& name) {
    std::lock_guard<std::mutex> lock(s_loggers_mutex);
    
    auto it = s_loggers.find(name);
    if (it != s_loggers.end()) {
        return it->second;
    }
    
    auto logger = std::make_shared<Logger>(name);
    s_loggers[name] = logger;
    return logger;
}

std::shared_ptr<Logger> Logger::get_root_logger() {
    return get_logger("root");
}

void Logger::shutdown_all_loggers() {
    std::lock_guard<std::mutex> lock(s_loggers_mutex);
    
    for (auto& pair : s_loggers) {
        if (pair.second) {
            pair.second->shutdown();
        }
    }
    
    s_loggers.clear();
}

// 私有方法实现
void Logger::update_statistics(Logging::LogLevel level) {
    std::lock_guard<std::mutex> lock(m_stats_mutex);
    
    m_statistics.total_messages++;
    increment_level_counter(level);
    m_statistics.logger_message_counts[m_name]++;
    m_last_stats_update = std::chrono::system_clock::now();
}

void Logger::increment_level_counter(Logging::LogLevel level) {
    int index = static_cast<int>(level);
    if (index >= 0 && index < 6) {
        m_statistics.messages_per_level[index]++;
    }
}

bool Logger::ensure_log_directory() const {
    if (!m_config.enable_file_output) {
        return true;
    }
    
    try {
        std::filesystem::path log_dir(m_config.log_directory);
        if (!std::filesystem::exists(log_dir)) {
            return std::filesystem::create_directories(log_dir);
        }
        return std::filesystem::is_directory(log_dir);
    } catch (const std::exception&) {
        return false;
    }
}

std::string Logger::generate_log_file_path() const {
    std::filesystem::path log_dir(m_config.log_directory);
    std::filesystem::path log_file(m_config.log_file_name);
    return (log_dir / log_file).string();
}

bool Logger::validate_config(const Logging::LoggerConfig& config) const {
    if (config.logger_name.empty()) {
        return false;
    }
    
    if (config.enable_file_output) {
        if (config.log_directory.empty() || config.log_file_name.empty()) {
            return false;
        }
        
        if (config.max_file_size == 0 || config.max_file_count == 0) {
            return false;
        }
    }
    
    if (config.enable_async_logging && config.async_queue_size == 0) {
        return false;
    }
    
    return true;
}

void Logger::setup_spdlog_logger() {
    // 检查是否已存在同名logger，如果存在则先删除
    if (spdlog::get(m_name)) {
        spdlog::drop(m_name);
    }
    
    // 使用sink方式创建logger，避免自动注册冲突
    std::vector<spdlog::sink_ptr> sinks;
    
    if (m_config.enable_console_output) {
        auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        sinks.push_back(console_sink);
    }
    
    if (m_config.enable_file_output) {
        if (!ensure_log_directory()) {
            throw std::runtime_error("Failed to create log directory: " + m_config.log_directory);
        }
        std::string log_file_path = generate_log_file_path();
        auto file_sink = std::make_shared<spdlog::sinks::basic_file_sink_mt>(log_file_path);
        sinks.push_back(file_sink);
    }
    
    // 如果没有配置任何输出，默认使用控制台
    if (sinks.empty()) {
        auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        sinks.push_back(console_sink);
    }
    
    // 创建logger
    m_spdlog_logger = std::make_shared<spdlog::logger>(m_name, sinks.begin(), sinks.end());
    
    if (!m_spdlog_logger) {
        throw std::runtime_error("Failed to create spdlog logger");
    }
    
    // 设置日志级别
    m_spdlog_logger->set_level(spdlog::level::info);
    
    // 注册到spdlog registry
    spdlog::register_logger(m_spdlog_logger);
}