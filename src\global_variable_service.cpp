#include "global_variable_service.hpp"
#include <algorithm>

// ========== 单例模式实现 ==========

GlobalVariableService& GlobalVariableService::get_instance() {
    static GlobalVariableService instance;
    return instance;
}

// ========== 服务生命周期管理 ==========

ATE_EC GlobalVariableService::initialize(const std::string& instance_id, const RedisConfig& redis_config) {
    std::lock_guard<std::mutex> lock(m_service_mutex);
    
    if (m_initialized.load()) {
        return ATE_ERROR_ALREADY_INITIALIZED;
    }
    
    try {
        // 创建底层全局变量系统实例
        m_global_variable_system = std::make_unique<GlobalVariableTask>(instance_id, redis_config);
        
        // 初始化底层系统
        ATE_EC result = m_global_variable_system->initialize();
        if (result != ATE_SUCCESS) {
            m_global_variable_system.reset();
            return result;
        }
        
        m_initialized.store(true);
        return ATE_SUCCESS;
        
    } catch (const std::exception&) {
        m_global_variable_system.reset();
        return ATE_ERROR_INITIALIZATION_FAILED;
    }
}

void GlobalVariableService::shutdown() {
    std::lock_guard<std::mutex> lock(m_service_mutex);
    
    if (!m_initialized.load()) {
        return;
    }
    
    // 清空所有监听器
    {
        std::lock_guard<std::mutex> listeners_lock(m_listeners_mutex);
        m_change_listeners.clear();
    }
    
    // 关闭底层系统
    if (m_global_variable_system) {
        m_global_variable_system->shutdown();
        m_global_variable_system.reset();
    }
    
    m_initialized.store(false);
}

bool GlobalVariableService::is_initialized() const {
    return m_initialized.load();
}

// ========== 基本变量操作接口 ==========

ATE_EC GlobalVariableService::set_variable(const std::string& key, const VariableValue& value) {
    std::lock_guard<std::mutex> lock(m_service_mutex);
    
    if (!m_initialized.load() || !m_global_variable_system) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    ATE_EC result = m_global_variable_system->set_variable(key, value);
    
    // 如果设置成功，通知所有监听器
    if (result == ATE_SUCCESS) {
        notify_variable_changed(key, value);
    }
    
    return result;
}

ATE_EC GlobalVariableService::get_variable(const std::string& key, VariableValue& value) const {
    std::lock_guard<std::mutex> lock(m_service_mutex);
    
    if (!m_initialized.load() || !m_global_variable_system) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    return m_global_variable_system->get_variable(key, value);
}

bool GlobalVariableService::has_variable(const std::string& key) const {
    std::lock_guard<std::mutex> lock(m_service_mutex);
    
    if (!m_initialized.load() || !m_global_variable_system) {
        return false;
    }
    
    return m_global_variable_system->has_variable(key);
}

ATE_EC GlobalVariableService::remove_variable(const std::string& key) {
    std::lock_guard<std::mutex> lock(m_service_mutex);
    
    if (!m_initialized.load() || !m_global_variable_system) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    return m_global_variable_system->remove_variable(key);
}

// ========== 类型特化的便捷接口 ==========

ATE_EC GlobalVariableService::set_double(const std::string& key, double value, const std::string& unit) {
    VariableValue var_value(value, unit);
    return set_variable(key, var_value);
}

ATE_EC GlobalVariableService::set_string(const std::string& key, const std::string& value, const std::string& unit) {
    VariableValue var_value(value, unit);
    return set_variable(key, var_value);
}

ATE_EC GlobalVariableService::set_bool(const std::string& key, bool value, const std::string& unit) {
    VariableValue var_value(value, unit);
    return set_variable(key, var_value);
}

ATE_EC GlobalVariableService::set_int(const std::string& key, int value, const std::string& unit) {
    VariableValue var_value(value, unit);
    return set_variable(key, var_value);
}

ATE_EC GlobalVariableService::get_double(const std::string& key, double& value) const {
    std::lock_guard<std::mutex> lock(m_service_mutex);
    
    if (!m_initialized.load() || !m_global_variable_system) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    return m_global_variable_system->get_double(key, value);
}

ATE_EC GlobalVariableService::get_string(const std::string& key, std::string& value) const {
    std::lock_guard<std::mutex> lock(m_service_mutex);
    
    if (!m_initialized.load() || !m_global_variable_system) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    return m_global_variable_system->get_string(key, value);
}

ATE_EC GlobalVariableService::get_bool(const std::string& key, bool& value) const {
    std::lock_guard<std::mutex> lock(m_service_mutex);
    
    if (!m_initialized.load() || !m_global_variable_system) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    return m_global_variable_system->get_bool(key, value);
}

ATE_EC GlobalVariableService::get_int(const std::string& key, int& value) const {
    std::lock_guard<std::mutex> lock(m_service_mutex);
    
    if (!m_initialized.load() || !m_global_variable_system) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    return m_global_variable_system->get_int(key, value);
}

// ========== 批量操作接口 ==========

std::vector<std::string> GlobalVariableService::get_all_keys() const {
    std::lock_guard<std::mutex> lock(m_service_mutex);
    
    if (!m_initialized.load() || !m_global_variable_system) {
        return std::vector<std::string>();
    }
    
    return m_global_variable_system->get_variable_names();
}

size_t GlobalVariableService::get_variable_count() const {
    std::lock_guard<std::mutex> lock(m_service_mutex);
    
    if (!m_initialized.load() || !m_global_variable_system) {
        return 0;
    }
    
    return m_global_variable_system->get_variable_count();
}

ATE_EC GlobalVariableService::clear_all_variables() {
    std::lock_guard<std::mutex> lock(m_service_mutex);
    
    if (!m_initialized.load() || !m_global_variable_system) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    return m_global_variable_system->clear_all_variables();
}

// ========== 监听器模式支持 ==========

ATE_EC GlobalVariableService::register_change_listener(const std::string& listener_id, VariableChangeCallback callback) {
    if (!callback) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    std::lock_guard<std::mutex> lock(m_listeners_mutex);
    
    // 检查监听器ID是否已存在
    if (m_change_listeners.find(listener_id) != m_change_listeners.end()) {
        return ATE_ERROR_ALREADY_EXISTS;
    }
    
    m_change_listeners[listener_id] = callback;
    return ATE_SUCCESS;
}

ATE_EC GlobalVariableService::unregister_change_listener(const std::string& listener_id) {
    std::lock_guard<std::mutex> lock(m_listeners_mutex);
    
    auto it = m_change_listeners.find(listener_id);
    if (it == m_change_listeners.end()) {
        return ATE_ERROR_NOT_FOUND;
    }
    
    m_change_listeners.erase(it);
    return ATE_SUCCESS;
}

size_t GlobalVariableService::get_listener_count() const {
    std::lock_guard<std::mutex> lock(m_listeners_mutex);
    return m_change_listeners.size();
}

void GlobalVariableService::notify_variable_changed(const std::string& key, const VariableValue& value) {
    std::lock_guard<std::mutex> lock(m_listeners_mutex);
    
    // 通知所有注册的监听器
    for (const auto& listener_pair : m_change_listeners) {
        try {
            listener_pair.second(key, value);
        } catch (const std::exception&) {
            // 忽略监听器回调中的异常，避免影响其他监听器
            // 在实际应用中，可以考虑记录日志
        }
    }
}

// ========== Redis同步接口 ==========

ATE_EC GlobalVariableService::sync_from_redis() {
    std::lock_guard<std::mutex> lock(m_service_mutex);
    
    if (!m_initialized.load() || !m_global_variable_system) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    return m_global_variable_system->sync_from_redis();
}

ATE_EC GlobalVariableService::sync_to_redis() {
    std::lock_guard<std::mutex> lock(m_service_mutex);
    
    if (!m_initialized.load() || !m_global_variable_system) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    return m_global_variable_system->sync_to_redis();
}

bool GlobalVariableService::is_redis_connected() const {
    std::lock_guard<std::mutex> lock(m_service_mutex);
    
    if (!m_initialized.load() || !m_global_variable_system) {
        return false;
    }
    
    return m_global_variable_system->is_connected();
}