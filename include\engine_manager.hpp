#ifndef ENGINE_MANAGER_HPP
#define ENGINE_MANAGER_HPP

#include <memory>
#include <vector>
#include <string>
#include <mutex>
#include <map>
#include "nlohmann/json.hpp"
#include "common_types.hpp"
#include "engine_core.hpp"
#include "global_core.hpp"

/**
 * @brief 全局设置结构体
 * 基于engine_manager_config.json中的global_settings字段
 */
struct GlobalSettings {
    std::string temp_directory = "";
    std::string backup_directory = "";
    bool auto_start_engines = false;
    bool auto_start_global_core = true;
    
    // 配置字符串保存
    std::string config_json_string = "";
    
    /**
     * @brief 检查配置是否有效
     * @return true 如果配置有效，false 否则
     */
    bool is_valid() const {
        return !temp_directory.empty() && 
               !backup_directory.empty();
    }
};

/**
 * @brief 引擎管理器类
 * 
 * 负责管理多个EngineCore实例和单例GlobalCore的核心管理类。
 * 提供引擎生命周期管理、状态监控、数据记录和报表输出等功能。
 * 采用单例模式设计，使用JSON格式配置参数，确保接口稳定性。
 * 
 * 主要功能：
 * - 引擎核心管理：启动、停止、暂停、调试多个引擎实例
 * - 系统监控管理：管理全局系统监控器的生命周期
 * - 状态查询：提供引擎和系统监控器的状态信息
 * - 数据记录：管理测试数据的记录功能
 * - 报表输出：管理测试报告的生成和输出
 * - 状态机回调：处理引擎和系统监控器的状态变化事件
 */
class EngineManager {
public:
    // 禁用拷贝构造和赋值
    EngineManager(const EngineManager&) = delete;
    EngineManager& operator=(const EngineManager&) = delete;
    
    // 析构函数
    ~EngineManager();
    
    // 获取单例实例
    static EngineManager& get_instance();
    
    // 核心管理函数
    ATE_EC initialize(const std::string& config); ///< 初始化通道测试引擎管理器和他的附属系统，初始化系统管理模块，初始化全局变量服务加载配置文件
    ATE_EC start();
    ATE_EC cleanup();
    std::string get_config() const;
    
    // 引擎核心管理
    ATE_EC start_engine_core(int engine_id);          // 启动指定ID的引擎核心
    ATE_EC stop_engine_core(int engine_id);           // 停止指定ID的引擎核心
    ATE_EC suspend_engine_core(int engine_id);        // 暂停指定ID的引擎核心
    ATE_EC resume_engine_core(int engine_id);         // 恢复指定ID的引擎核心
    ATE_EC debug_engine(int engine_id);               // 调试指定ID的引擎核心
    FSMState get_engine_core_status(int engine_id); // 获取指定引擎核心的状态
    
    // 全局核心管理接口
    ATE_EC start_global_core();                    // 启动全局核心
    ATE_EC stop_global_core();                     // 停止全局核心
    ATE_EC suspend_global_core();                  // 暂停全局核心
    ATE_EC resume_global_core();                   // 恢复全局核心
    ATE_EC debug_global_core();                    // 调试全局核心
    FSMState get_global_core_status();        // 获取全局核心状态
    
    // 状态机回调函数
    ATE_EC FSM_engine_callback(int engine_id);         // 引擎状态机回调处理
    ATE_EC FSM_global_core_callback();              // 全局核心状态机回调处理
    
    // 数据记录器管理
    ATE_EC start_data_recorder(int engine_id);         // 启动指定引擎的数据记录器
    ATE_EC stop_data_recorder(int engine_id);          // 停止指定引擎的数据记录器

    // 报表输出管理
    ATE_EC start_report_output(int engine_id);         // 启动指定引擎的报表输出
    
    // 全局设置管理
    const GlobalSettings& get_global_settings() const;  // 获取全局设置
    std::string get_global_settings_json_string() const; // 获取全局设置的JSON字符串

private:
     // 私有构造函数（单例模式）
     EngineManager();
    
    // 成员变量
    std::string m_config;
    bool m_initialized;
    std::map<int, std::unique_ptr<EngineCore>> m_engine_cores;
    std::mutex m_engines_mutex;
    
    // 全局核心实例
    GlobalCore* m_global_core;
    
    // 全局设置
    GlobalSettings m_global_settings;
    
    // 辅助函数
    bool validate_config(const std::string& config) const;  // 验证配置参数的有效性
    bool add_engine_core(int engine_id, const std::string& engine_config);  // 添加新的引擎核心实例
    bool remove_engine_core(int engine_id);                 // 移除指定的引擎核心实例
    std::unique_ptr<EngineCore> create_engine_core(int engine_id, const std::string& engine_config);  // 创建引擎核心实例
    EngineCore* find_engine_core(int engine_id);            // 查找指定ID的引擎核心实例
};

#endif // ENGINE_MANAGER_HPP