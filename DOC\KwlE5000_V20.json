{"driver_info": {"name": "E5000_V2", "version": "2.0.0", "vendor": "KEWELL TECHNOLOGY CO.,LTD.", "description": "High precision electronic load driver with multi-instance support", "dll_filename": "E5000_V2.dll", "device_type": "ELECTRONIC_LOAD", "supports_multi_instance": true}, "capabilities": {"max_instances": 4, "supports_hot_plug": true, "supports_calibration": true, "auto_discovery": false}, "error_handling": {"auto_recovery": true, "error_logging_enabled": true}, "instances": [{"instance_id": "E5000_01", "device_index": 0, "display_name": "E5000 Electronic Load Unit 1", "communication": {"interface_type": "TCP", "ip_address": "*************", "port": 502, "device_id": 1, "timeout_ms": 1000, "retry_count": 3}, "memory_base_offset": 0, "enabled": true, "custom_parameters": {"serial_number": "********", "calibration_date": "2024-01-15", "max_power": 6000.0, "max_current": 240.0, "max_voltage": 500.0}}, {"instance_id": "E5000_02", "device_index": 1, "display_name": "E5000 Electronic Load Unit 2", "communication": {"interface_type": "TCP", "ip_address": "*************", "port": 502, "device_id": 2, "timeout_ms": 1000, "retry_count": 3}, "memory_base_offset": 256, "enabled": true, "custom_parameters": {"serial_number": "********", "calibration_date": "2024-01-16", "max_power": 6000.0, "max_current": 240.0, "max_voltage": 500.0}}, {"instance_id": "E5000_03", "device_index": 2, "display_name": "E5000 Electronic Load Unit 3 (Serial)", "communication": {"interface_type": "SERIAL", "com_port": "COM3", "baud_rate": 9600, "data_bits": 8, "parity": "NONE", "stop_bits": 1, "device_id": 3, "timeout_ms": 2000, "retry_count": 5}, "memory_base_offset": 512, "enabled": false, "custom_parameters": {"serial_number": "E5000003", "calibration_date": "2024-01-17", "max_power": 6000.0, "max_current": 240.0, "max_voltage": 500.0}}, {"instance_id": "E5000_04", "device_index": 3, "display_name": "E5000 Electronic Load Unit 4 (USB)", "communication": {"interface_type": "USB", "vendor_id": "0x1234", "product_id": "0x5678", "device_id": 4, "timeout_ms": 1500, "retry_count": 3}, "memory_base_offset": 768, "enabled": false, "custom_parameters": {"serial_number": "E5000004", "calibration_date": "2024-01-18", "max_power": 6000.0, "max_current": 240.0, "max_voltage": 500.0}}, {"instance_id": "E5000_05", "device_index": 4, "display_name": "E5000 Electronic Load Unit 5 (SDK)", "communication": {"interface_type": "SDK", "sdk_name": "E5000_SDK", "sdk_version": "2.1.0", "connection_string": "device://E5000/unit5", "device_id": 5, "timeout_ms": 1000, "retry_count": 3}, "memory_base_offset": 1024, "enabled": false, "custom_parameters": {"serial_number": "E5000005", "calibration_date": "2024-01-19", "max_power": 6000.0, "max_current": 240.0, "max_voltage": 500.0}}], "data_storage": {"mode": "redis", "shared_memory": {"per_instance_size": 256, "total_pool_size": 1024, "alignment": 8, "update_frequency": 1000, "addressing_mode": "INSTANCE_OFFSET"}, "redis": {"host": "localhost", "port": 6379, "database": 0, "key_prefix": "e5000:", "instance_key_format": "e5000:instance:{id}", "data_structure": "HASH", "ttl_seconds": 3600, "connection_pool_size": 10, "update_frequency": 100}, "data_points": {"measurement_data": {"description": "Real-time measurement values from device", "access": "read-only", "fields": {"irms": {"address": 1000, "type": "double", "unit": "A", "scale_factor": 0.0001, "description": "RMS Current measurement", "offset": 0, "size": 8, "range": {"min": 0.0, "max": 1000.0}}, "urms": {"address": 1002, "type": "double", "unit": "V", "scale_factor": 0.0001, "description": "RMS Voltage measurement", "offset": 8, "size": 8, "range": {"min": 0.0, "max": 1000.0}}, "prms": {"address": 1004, "type": "double", "unit": "W", "scale_factor": 0.001, "description": "RMS Power measurement", "offset": 16, "size": 8, "range": {"min": 0.0, "max": 100000.0}}, "ipeak": {"address": 1006, "type": "double", "unit": "A", "scale_factor": 0.0001, "description": "Peak Current measurement", "offset": 24, "size": 8, "range": {"min": 0.0, "max": 1000.0}}, "upeak": {"address": 1008, "type": "double", "unit": "V", "scale_factor": 0.0001, "description": "Peak Voltage measurement", "offset": 32, "size": 8, "range": {"min": 0.0, "max": 1000.0}}, "ppeak": {"address": 1010, "type": "double", "unit": "W", "scale_factor": 0.001, "description": "Peak Power measurement", "offset": 40, "size": 8, "range": {"min": 0.0, "max": 100000.0}}, "ivalley": {"address": 1012, "type": "double", "unit": "A", "scale_factor": 0.0001, "description": "Valley Current measurement", "offset": 48, "size": 8, "range": {"min": 0.0, "max": 1000.0}}, "uvalley": {"address": 1014, "type": "double", "unit": "V", "scale_factor": 0.0001, "description": "Valley Voltage measurement", "offset": 56, "size": 8, "range": {"min": 0.0, "max": 1000.0}}, "pvalley": {"address": 1016, "type": "double", "unit": "W", "scale_factor": 0.001, "description": "Valley Power measurement", "offset": 64, "size": 8, "range": {"min": 0.0, "max": 100000.0}}}}, "status_data": {"description": "Device and load status information", "access": "read-only", "fields": {"load_run_state_01": {"address": 1100, "type": "uint64", "description": "<PERSON><PERSON> running state 01", "offset": 72, "size": 8, "bit_fields": {"run_stop_show": {"bit": 0, "description": "Device running status: 0=stopped, 1=running"}, "short_show": {"bit": 1, "description": "Short circuit status: 0=normal, 1=short circuit"}}}, "load_run_state_02": {"address": 1102, "type": "uint64", "description": "<PERSON><PERSON> running state 02", "offset": 80, "size": 8}, "load_run_state_03": {"address": 1104, "type": "uint64", "description": "<PERSON><PERSON> running state 03", "offset": 88, "size": 8}, "load_run_state_04": {"address": 1106, "type": "uint64", "description": "<PERSON><PERSON> running state 04", "offset": 96, "size": 8}, "system_run_state_01": {"address": 1108, "type": "uint64", "description": "System running state 01", "offset": 104, "size": 8}, "load_error_01": {"address": 1110, "type": "uint64", "description": "Load error status 01", "offset": 112, "size": 8}, "load_error_02": {"address": 1112, "type": "uint64", "description": "Load error status 02", "offset": 120, "size": 8}, "load_error_03": {"address": 1114, "type": "uint64", "description": "Load error status 03", "offset": 128, "size": 8}, "load_error_04": {"address": 1116, "type": "uint64", "description": "Load error status 04", "offset": 136, "size": 8}, "system_error_01": {"address": 1118, "type": "uint64", "description": "System error status 01", "offset": 144, "size": 8}, "run_stop_show": {"address": 1120, "type": "uint64", "description": "Device running status: 0=stopped, 1=running", "offset": 280, "size": 8}, "short_show": {"address": 1122, "type": "uint64", "description": "Short circuit status: 0=normal, 1=short circuit", "offset": 288, "size": 8}}}, "control_parameters": {"description": "Device control and setpoint parameters", "access": "read-write", "fields": {"operation_mode": {"address": 2001, "type": "uint16", "description": "Operation mode selection", "offset": 304, "size": 2, "values": {"1": "CC - Constant Current", "2": "CV - Constant Voltage", "3": "CP - Constant Power", "4": "CR - Constant Resistance"}}, "current_setpoint": {"address": 2002, "type": "double", "unit": "A", "scale_factor": 0.0001, "description": "Current setpoint for CC mode", "offset": 300, "size": 8, "range": {"min": 0.0, "max": 1000.0}}, "voltage_setpoint": {"address": 2003, "type": "double", "unit": "V", "scale_factor": 0.0001, "description": "Voltage setpoint for CV mode", "offset": 292, "size": 8, "range": {"min": 0.0, "max": 1000.0}}, "power_setpoint": {"address": 2004, "type": "double", "unit": "W", "scale_factor": 0.001, "description": "Power setpoint for CP mode", "offset": 308, "size": 8, "range": {"min": 0.0, "max": 100000.0}}, "resistance_setpoint": {"address": 2005, "type": "double", "unit": "Ω", "scale_factor": 0.0001, "description": "Resistance setpoint for CR mode", "offset": 284, "size": 8, "range": {"min": 0.001, "max": 100000.0}}, "range_settings": {"cc_current_range": {"address": 2006, "type": "uint16", "description": "CC mode current range selection", "values": {"1": "CCL - Low Range", "2": "CCM - Medium Range", "3": "CCH - High Range"}}, "cv_voltage_range": {"address": 2007, "type": "uint16", "description": "CV mode voltage range selection", "values": {"1": "CVL - Low Range", "2": "CVM - Medium Range", "3": "CVH - High Range"}}}, "output_control": {"output_enable": {"address": 2014, "type": "uint16", "description": "Output enable control", "values": {"0": "Output Disabled", "1": "Output Enabled"}}, "load_enable": {"address": 2015, "type": "uint16", "description": "Load enable control", "values": {"0": "Load Disabled", "1": "Load Enabled"}}, "alarm_reset": {"address": 2020, "type": "uint16", "description": "Alarm reset command", "values": {"1": "Reset Alarms"}}, "on_off_control": {"address": 2021, "type": "uint16", "description": "Device on/off control", "values": {"0": "Device Off", "1": "<PERSON><PERSON>"}}, "remote_control": {"address": 2022, "type": "uint16", "description": "Remote control mode", "values": {"0": "Local Control", "1": "Remote Control"}}}}}, "advanced_modes": {"description": "Advanced operation modes and parameters", "access": "read-write", "fields": {"ccd_parameters": {"description": "Constant Current Dynamic (CCD) mode parameters", "current_a": {"address": 2802, "type": "double", "unit": "A", "scale_factor": 0.0001, "description": "CCD Current A setpoint"}, "current_b": {"address": 2804, "type": "double", "unit": "A", "scale_factor": 0.0001, "description": "CCD Current B setpoint"}, "time_t1": {"address": 2810, "type": "double", "unit": "ms", "scale_factor": 0.001, "description": "CCD Time T1 duration"}, "time_t2": {"address": 2812, "type": "double", "unit": "ms", "scale_factor": 0.001, "description": "CCD Time T2 duration"}, "repeat_count": {"address": 2814, "type": "uint16", "description": "CCD Repeat count"}}}}, "range_data": {"description": "Current range settings for different operation modes", "access": "read-write", "fields": {"cc_ranges": {"cc_i_lv": {"address": 3001, "type": "uint64", "description": "CC current range level", "offset": 152, "size": 8}, "cc_v_lv": {"address": 3002, "type": "uint64", "description": "CC voltage range level", "offset": 160, "size": 8}}, "cv_ranges": {"cv_v_lv": {"address": 3003, "type": "uint64", "description": "CV voltage range level", "offset": 168, "size": 8}, "cv_i_lv": {"address": 3004, "type": "uint64", "description": "CV current range level", "offset": 176, "size": 8}}, "cp_ranges": {"cp_p_lv": {"address": 3005, "type": "uint64", "description": "CP power range level", "offset": 184, "size": 8}, "cp_v_lv": {"address": 3006, "type": "uint64", "description": "CP voltage range level", "offset": 192, "size": 8}}, "cr_ranges": {"cr_r_lv": {"address": 3007, "type": "uint64", "description": "CR resistance range level", "offset": 200, "size": 8}, "cr_i_lv": {"address": 3008, "type": "uint64", "description": "CR current range level", "offset": 208, "size": 8}}, "ccd_ranges": {"ccd_i_lv": {"address": 3009, "type": "uint64", "description": "CCD current range level", "offset": 216, "size": 8}, "ccd_v_lv": {"address": 3010, "type": "uint64", "description": "CCD voltage range level", "offset": 224, "size": 8}}, "crd_ranges": {"crd_r_lv": {"address": 3011, "type": "uint64", "description": "CRD resistance range level", "offset": 232, "size": 8}, "crd_i_lv": {"address": 3012, "type": "uint64", "description": "CRD current range level", "offset": 240, "size": 8}}, "swd_ranges": {"swd_i_lv": {"address": 3013, "type": "uint64", "description": "SWD current range level", "offset": 248, "size": 8}, "swd_v_lv": {"address": 3014, "type": "uint64", "description": "SWD voltage range level", "offset": 256, "size": 8}}, "swp_ranges": {"swp_i_lv": {"address": 3015, "type": "uint64", "description": "SWP current range level", "offset": 264, "size": 8}, "swp_v_lv": {"address": 3016, "type": "uint64", "description": "SWP voltage range level", "offset": 272, "size": 8}}}}, "system_info": {"description": "System information and validation", "access": "read-only", "fields": {"run_mode": {"address": 4001, "type": "uint64", "description": "Current load running mode", "offset": 316, "size": 8}, "data_valid": {"address": 4002, "type": "int32", "description": "Data validity flag: 0=invalid, non-zero=valid", "offset": 324, "size": 4}}}}}, "command_system": {"categories": {"read_only_control": {"description": "系统只读指令", "access": "read-only", "commands": {"ReadOnly": {"function_name": "Read<PERSON>nly", "parameter_type": "string", "valid_values": ["Read"], "description": "读取系统只读指令，常见于自由驱动的外部数据读取，内部获得数据库自行解析。", "response_time_ms": 100}}}, "system_control": {"description": "系统控制指令", "access": "write-only", "commands": {"Write": {"function_name": "Write", "parameter_type": "hexstring", "valid_values": "^[0-9A-Fa-f]+$", "description": "写入系统控制指令，常见于自由驱动的外部数据写入，DLL内部发送hexstring转16进制数据发送，（像串口调试助手）。", "response_time_ms": 100}, "AlmReset": {"function_name": "AlmReset", "parameter_type": "string", "valid_values": ["TRIGGER"], "description": "清理电子负载当前故障，如故障消失，复位后则无故障，否则依然会有故障信息被读取到", "response_time_ms": 100}, "ONOFF": {"function_name": "ONOFF", "parameter_type": "string", "valid_values": ["ON", "OFF"], "description": "设置开机关机状态", "response_time_ms": 200}, "LoadCCT": {"function_name": "LoadCCT", "parameter_type": "string", "valid_values": ["ON", "OFF"], "description": "设置负载开关状态", "response_time_ms": 150}}}, "mode_configuration": {"description": "模式配置指令", "access": "write-only", "commands": {"Cmod": {"function_name": "Cmod", "parameter_type": "string", "valid_values": ["CC", "CV", "CP", "CR", "CCD", "CRD"], "description": "设置负载工作模式", "response_time_ms": 100}, "RmtCtrl": {"function_name": "RmtCtrl", "parameter_type": "string", "valid_values": ["REM", "LOC"], "description": "设置远程/本地控制模式", "response_time_ms": 100}, "LoadVon": {"function_name": "LoadVon", "parameter_type": "double", "unit": "V", "description": "设置负载导通电压", "response_time_ms": 100, "validation": {"required": true, "precision": 0.1, "range": {"min": 0.0, "max": 500.0}}}}}, "range_configuration": {"description": "档位配置指令", "access": "write-only", "commands": {"CCSubmod": {"function_name": "CCSubmod", "parameter_type": "string", "valid_values": ["LOW", "MID", "HIGH"], "description": "设置CC模式电流档位", "response_time_ms": 100}, "CCSubVmod": {"function_name": "CCSubVmod", "parameter_type": "string", "valid_values": ["LOW", "HIGH"], "description": "设置CC模式电压档位", "response_time_ms": 100}, "CVSubmod": {"function_name": "CVSubmod", "parameter_type": "string", "valid_values": ["LOW", "HIGH"], "description": "设置CV模式电压档位", "response_time_ms": 100}, "CVSubImod": {"function_name": "CVSubImod", "parameter_type": "string", "valid_values": ["LOW", "MID", "HIGH"], "description": "设置CV模式电流档位", "response_time_ms": 100}, "CPSubVmod": {"function_name": "CPSubVmod", "parameter_type": "string", "valid_values": ["LOW", "HIGH"], "description": "设置CP模式电压档位", "response_time_ms": 100}, "CPSubImod": {"function_name": "CPSubImod", "parameter_type": "string", "valid_values": ["LOW", "MID", "HIGH"], "description": "设置CP模式电流档位", "response_time_ms": 100}, "CRSubVmod": {"function_name": "CRSubVmod", "parameter_type": "string", "valid_values": ["LOW", "HIGH"], "description": "设置CR模式电压档位", "response_time_ms": 100}, "CRSubImod": {"function_name": "CRSubImod", "parameter_type": "string", "valid_values": ["LOW", "MID", "HIGH"], "description": "设置CR模式电流档位", "response_time_ms": 100}, "CVAmpLoadMax": {"function_name": "CVAmpLoadMax", "parameter_type": "double", "unit": "A", "description": "设置CV模式最大带载电流", "response_time_ms": 100, "validation": {"required": true, "precision": 0.1, "range": {"min": 0.0, "max": 240.0}}}, "CVSpeedLv": {"function_name": "CVSpeedLv", "parameter_type": "string", "valid_values": ["SLOW", "FAST"], "description": "设置CV模式速度等级", "response_time_ms": 100}}}, "load_mode_control": {"description": "负载模式控制指令", "access": "write-only", "commands": {"CCmodeAmp": {"function_name": "CCmodeAmp", "parameter_type": "double", "unit": "A", "description": "设置CC模式电流值", "response_time_ms": 100, "validation": {"required": true, "precision": 0.001, "range": {"min": 0.0, "max": 240.0}}}, "CVmodeVol": {"function_name": "CVmodeVol", "parameter_type": "double", "unit": "V", "description": "设置CV模式电压值", "response_time_ms": 100, "validation": {"required": true, "precision": 0.001, "range": {"min": 0.0, "max": 500.0}}}, "CPmodePwr": {"function_name": "CPmodePwr", "parameter_type": "double", "unit": "W", "description": "设置CP模式功率值", "response_time_ms": 100, "validation": {"required": true, "precision": 0.1, "range": {"min": 0.0, "max": 6000.0}}}, "CRmodeR": {"function_name": "CRmodeR", "parameter_type": "double", "unit": "Ω", "description": "设置CR模式电阻值", "response_time_ms": 100, "validation": {"required": true, "precision": 0.001, "range": {"min": 0.001, "max": 99999.0}}}, "CCmodeAmpSlpUp": {"function_name": "CCmodeAmpSlpUp", "parameter_type": "double", "unit": "A/s", "description": "设置CC模式电流上升斜率", "response_time_ms": 100, "validation": {"required": true, "precision": 0.001, "range": {"min": 0.001, "max": 240.0}}}, "CCmodeAmpSlpDown": {"function_name": "CCmodeAmpSlpDown", "parameter_type": "double", "unit": "A/s", "description": "设置CC模式电流下降斜率", "response_time_ms": 100, "validation": {"required": true, "precision": 0.001, "range": {"min": 0.001, "max": 240.0}}}, "CPmodeAmpSlpUp": {"function_name": "CPmodeAmpSlpUp", "parameter_type": "double", "unit": "A/s", "description": "设置CP模式电流上升斜率", "response_time_ms": 100, "validation": {"required": true, "precision": 0.001, "range": {"min": 0.001, "max": 240.0}}}, "CPmodeAmpSlpDown": {"function_name": "CPmodeAmpSlpDown", "parameter_type": "double", "unit": "A/s", "description": "设置CP模式电流下降斜率", "response_time_ms": 100, "validation": {"required": true, "precision": 0.001, "range": {"min": 0.001, "max": 240.0}}}}}, "dynamic_mode_control": {"description": "动态模式控制指令", "access": "write-only", "commands": {"CCDVal": {"function_name": "CCDVal", "parameter_type": "string", "description": "设定CCD模式参数：A电流,B电流,T1时间,T2时间,重复次数", "unit": "A,A,ms,ms,次", "validation": {"format": "float,float,float,float,int", "range": "0-240,0-240,0.020-100000,0.020-100000,1-65535"}}, "CRDVal": {"function_name": "CRDVal", "parameter_type": "string", "description": "设定CRD模式参数：A电阻,B电阻,T1时间,T2时间,重复次数", "unit": "Ω,Ω,ms,ms,次", "validation": {"format": "float,float,float,float,int", "range": "0-999.9999,0-999.9999,0.020-100000,0.020-100000,1-65535"}}, "CCDmodeVal": {"function_name": "CCDmodeVal", "parameter_type": "double", "unit": "A", "description": "设置CCD模式电流值", "response_time_ms": 100, "validation": {"required": true, "precision": 0.001, "range": {"min": 0.0, "max": 240.0}}}, "CRDmodeVal": {"function_name": "CRDmodeVal", "parameter_type": "double", "unit": "Ω", "description": "设置CRD模式电阻值", "response_time_ms": 100, "validation": {"required": true, "precision": 0.001, "range": {"min": 0.001, "max": 999.9999}}}, "CCDmodeFre": {"function_name": "CCDmodeFre", "parameter_type": "double", "unit": "Hz", "description": "设置CCD模式频率", "response_time_ms": 100, "validation": {"required": true, "precision": 0.1, "range": {"min": 0.1, "max": 20000.0}}}, "CRDmodeFre": {"function_name": "CRDmodeFre", "parameter_type": "double", "unit": "Hz", "description": "设置CRD模式频率", "response_time_ms": 100, "validation": {"required": true, "precision": 0.1, "range": {"min": 0.1, "max": 20000.0}}}, "CCDmodeDuty": {"function_name": "CCDmodeDuty", "parameter_type": "double", "unit": "%", "description": "设置CCD模式占空比", "response_time_ms": 100, "validation": {"required": true, "precision": 0.1, "range": {"min": 1.0, "max": 99.0}}}, "CRDmodeDuty": {"function_name": "CRDmodeDuty", "parameter_type": "double", "unit": "%", "description": "设置CRD模式占空比", "response_time_ms": 100, "validation": {"required": true, "precision": 0.1, "range": {"min": 1.0, "max": 99.0}}}, "CCDmodeAmpSlpUp": {"function_name": "CCDmodeAmpSlpUp", "parameter_type": "double", "unit": "A/s", "description": "设置CCD模式电流上升斜率", "response_time_ms": 100, "validation": {"required": true, "precision": 0.001, "range": {"min": 0.001, "max": 240.0}}}, "CCDmodeAmpSlpDown": {"function_name": "CCDmodeAmpSlpDown", "parameter_type": "double", "unit": "A/s", "description": "设置CCD模式电流下降斜率", "response_time_ms": 100, "validation": {"required": true, "precision": 0.001, "range": {"min": 0.001, "max": 240.0}}}, "CRDmodeAmpSlpUp": {"function_name": "CRDmodeAmpSlpUp", "parameter_type": "double", "unit": "A/s", "description": "设置CRD模式电流上升斜率", "response_time_ms": 100, "validation": {"required": true, "precision": 0.001, "range": {"min": 0.001, "max": 240.0}}}, "CRDmodeAmpSlpDown": {"function_name": "CRDmodeAmpSlpDown", "parameter_type": "double", "unit": "A/s", "description": "设置CRD模式电流下降斜率", "response_time_ms": 100, "validation": {"required": true, "precision": 0.001, "range": {"min": 0.001, "max": 240.0}}}}}, "measurement_control": {"description": "测量控制指令", "access": "write-only", "commands": {"GetVol": {"function_name": "GetVol", "parameter_type": "none", "return_type": "double", "unit": "V", "description": "获取电压测量值", "response_time_ms": 50}, "GetAmp": {"function_name": "GetAmp", "parameter_type": "none", "return_type": "double", "unit": "A", "description": "获取电流测量值", "response_time_ms": 50}, "GetPwr": {"function_name": "GetPwr", "parameter_type": "none", "return_type": "double", "unit": "W", "description": "获取功率测量值", "response_time_ms": 50}, "GetR": {"function_name": "GetR", "parameter_type": "none", "return_type": "double", "unit": "Ω", "description": "获取电阻测量值", "response_time_ms": 50}}}, "protection_control": {"description": "保护控制指令", "access": "write-only", "commands": {"OVPVal": {"function_name": "OVPVal", "parameter_type": "double", "unit": "V", "description": "设置过压保护值", "response_time_ms": 100, "validation": {"required": true, "precision": 0.1, "range": {"min": 0.0, "max": 500.0}}}, "OCPVal": {"function_name": "OCPVal", "parameter_type": "double", "unit": "A", "description": "设置过流保护值", "response_time_ms": 100, "validation": {"required": true, "precision": 0.1, "range": {"min": 0.0, "max": 240.0}}}, "OPPVal": {"function_name": "OPPVal", "parameter_type": "double", "unit": "W", "description": "设置过功率保护值", "response_time_ms": 100, "validation": {"required": true, "precision": 1.0, "range": {"min": 0.0, "max": 6000.0}}}, "OTPVal": {"function_name": "OTPVal", "parameter_type": "double", "unit": "°C", "description": "设置过温保护值", "response_time_ms": 100, "validation": {"required": true, "precision": 1.0, "range": {"min": 0.0, "max": 100.0}}}}}}, "command_execution": {"timeout_ms": 5000, "retry_count": 3, "retry_delay_ms": 100, "queue_size": 100, "concurrent_commands": 4, "priority_scheduling": true}, "validation": {"parameter_checking": true, "range_validation": true, "type_validation": true, "authorization_required": ["Calibrate"]}}}