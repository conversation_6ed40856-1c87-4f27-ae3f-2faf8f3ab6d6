#pragma once

#include <unordered_map>
#include <string>
#include <memory>
#include <iostream>
#include <vector>
#include <mutex>
#include <thread>
#include <chrono>

#include <sol/sol.hpp>
#include "variable_types.hpp"
#include "common_types.hpp"
#include <nlohmann/json.hpp>
#include <condition_variable>
#include <atomic>
#include <set>

/**
 * C++和Lua之间的变量共享系统
 * 使用unordered_map存储变量，通过Sol2库实现透明访问
 */
class EngineVariableSystem {
public:
    EngineVariableSystem();
    ~EngineVariableSystem() noexcept;

    // 禁用拷贝构造和拷贝赋值（不可拷贝）
    EngineVariableSystem(const EngineVariableSystem&) = delete;
    EngineVariableSystem& operator=(const EngineVariableSystem&) = delete;

    // 禁用移动构造和移动赋值（包含线程，不可移动）
    EngineVariableSystem(EngineVariableSystem&&) = delete;
    EngineVariableSystem& operator=(EngineVariableSystem&&) = delete;

    // 变量信息结构体
    struct VariableInfo {
        std::string name;
        std::string type;
        bool should_save;
        std::string unit;
        
        VariableInfo(const std::string& n, const std::string& t, bool save, const std::string& u)
            : name(n), type(t), should_save(save), unit(u) {}
    };

    // 统一的变量设置接口（模板方法，自动推导类型）
    template<typename T>
    void set(const std::string& name, T&& value) {
        static_assert(std::is_same_v<std::decay_t<T>, double> ||
                     std::is_same_v<std::decay_t<T>, std::string> ||
                     std::is_same_v<std::decay_t<T>, bool> ||
                     std::is_same_v<std::decay_t<T>, int> ||
                     std::is_same_v<std::decay_t<T>, const char*>,
                     "Unsupported type for variable");

        if constexpr (std::is_same_v<std::decay_t<T>, const char*>) {
            set_variable(name, VariableValue(std::string(value)));
        } else {
            set_variable(name, VariableValue(std::forward<T>(value)));
        }
    }

    // 统一的获取接口（模板方法，自动推导返回类型）
    template<typename T>
    T get(const std::string& name, const T& defaultValue = T{}) const {
        static_assert(std::is_same_v<T, double> ||
                     std::is_same_v<T, std::string> ||
                     std::is_same_v<T, bool> ||
                     std::is_same_v<T, int>,
                     "Unsupported type for variable retrieval");

        if (!has_variable(name)) {
            return defaultValue;
        }

        auto value = get_variable(name);
        if constexpr (std::is_same_v<T, double>) {
            return value.asDouble();
        }
        else if constexpr (std::is_same_v<T, std::string>) {
            return value.asString();
        }
        else if constexpr (std::is_same_v<T, bool>) {
            return value.asBool();
        }
        else if constexpr (std::is_same_v<T, int>) {
            return value.asInt();
        }
        return defaultValue;
    }

    // 高性能的类型特化设置方法（避免variant构造开销）
    void set_number(const std::string& name, double value);
    void set_number(const std::string& name, double value, const std::string& unit);
    double get_number(const std::string& name, double default_value = 0.0) const;

    void set_string(const std::string& name, const std::string& value);
    void set_string(const std::string& name, const std::string& value, const std::string& unit);
    std::string get_string(const std::string& name, const std::string& default_value = "") const;

    void set_bool(const std::string& name, bool value);
    void set_bool(const std::string& name, bool value, const std::string& unit);
    bool get_bool(const std::string& name, bool default_value = false) const;

    void set_int(const std::string& name, int value);
    void set_int(const std::string& name, int value, const std::string& unit);
    int get_int(const std::string& name, int default_value = 0) const;
    
    // 设置和获取变量单位的方法
    void set_variable_unit(const std::string& name, const std::string& unit);
    std::string get_variable_unit(const std::string& name) const;
    
    // 设置和获取变量保存状态的方法
    bool set_variable_save_status(const std::string& name, bool should_save);
    bool get_variable_save_status(const std::string& name) const;
    std::vector<std::string> get_saveable_variable_names() const; ///< 获取所有设置为should_save=true的变量名
    
    // DataRecorder专用接口
    void* get_variable_data_pointer(const std::string& name);
    
    // 获取所有变量信息列表
    std::vector<VariableInfo> list_variables() const;

    // 变量管理方法
    bool set_variable(const std::string& name, const VariableValue& value);
    VariableValue get_variable(const std::string& name) const;
    bool has_variable(const std::string& name) const;
    void remove_variable(const std::string& name);
    void clear_all_variables();

    // Lua绑定相关
    void setup_MA_lua_binding();
    void setup_PR_lua_binding();

    // 获取Lua状态
    sol::state& get_MA_lua_state() { return m_MA_lua; }
    const sol::state& get_MA_lua_state() const { return m_MA_lua; }
    sol::state& get_PR_lua_state() { return m_PR_lua; }
    const sol::state& get_PR_lua_state() const { return m_PR_lua; }
    
    // ========================================================================
    // Lua脚本执行控制接口
    // ========================================================================
    
    /**
     * @brief 开始执行Lua脚本
     * @param script_content 脚本内容
     * @param script_name 脚本名称
     * @param script_type 脚本类型（MA或PR）
     * @return ATE_EC 错误代码
     */
    ATE_EC start_script_execution(const std::string& script_content, 
                                  const std::string& script_name,
                                  LuaScriptType script_type = LuaScriptType::MA_SCRIPT);
    
    /**
     * @brief 停止脚本执行
     * @param script_type 脚本类型
     * @return ATE_EC 错误代码
     */
    ATE_EC stop_script_execution(LuaScriptType script_type = LuaScriptType::MA_SCRIPT);
    
    /**
     * @brief 挂起脚本执行
     * @param script_type 脚本类型
     * @return ATE_EC 错误代码
     */
    ATE_EC suspend_script_execution(LuaScriptType script_type = LuaScriptType::MA_SCRIPT);
    
    /**
     * @brief 恢复脚本执行
     * @param script_type 脚本类型
     * @return ATE_EC 错误代码
     */
    ATE_EC resume_script_execution(LuaScriptType script_type = LuaScriptType::MA_SCRIPT);
    
    /**
     * @brief 在指定行设置调试断点并执行到断点
     * @param line_number 断点行号
     * @param script_type 脚本类型
     * @return ATE_EC 错误代码
     */
    ATE_EC debug_script_to_line(int line_number, LuaScriptType script_type = LuaScriptType::MA_SCRIPT);
    
    /**
     * @brief 添加调试断点
     * @param breakpoint 断点信息
     * @param script_type 脚本类型
     * @return ATE_EC 错误代码
     */
    ATE_EC add_debug_breakpoint(const DebugBreakpoint& breakpoint, LuaScriptType script_type = LuaScriptType::MA_SCRIPT);
    
    /**
     * @brief 移除调试断点
     * @param line_number 断点行号
     * @param script_type 脚本类型
     * @return ATE_EC 错误代码
     */
    ATE_EC remove_debug_breakpoint(int line_number, LuaScriptType script_type = LuaScriptType::MA_SCRIPT);
    
    /**
     * @brief 清除所有调试断点
     */
    void clear_all_breakpoints();
    
    /**
     * @brief 获取脚本执行状态
     * @param script_type 脚本类型
     * @return LuaExecutionState 执行状态
     */
    LuaExecutionState get_script_execution_state(LuaScriptType script_type = LuaScriptType::MA_SCRIPT) const;
    
    /**
     * @brief 处理Lua钩子事件（公有方法，供钩子函数调用）
     * @param L Lua状态机
     * @param ar Lua调试信息
     * @param script_type 脚本类型
     */
    static void handle_ma_lua_hook(lua_State* L, lua_Debug* ar);
    
    /**
     * @brief 获取当前执行上下文信息
     * @param script_type 脚本类型
     * @return LuaExecutionContext 执行上下文
     */
    LuaExecutionContext get_execution_context(LuaScriptType script_type = LuaScriptType::MA_SCRIPT) const;
    
    /**
     * @brief 获取所有变量的JSON格式数据（用于调试）
     * @return std::string JSON格式的变量数据
     */
    std::string get_all_variables_json() const;
   
    // 调试和信息方法
    std::string get_all_variables() const;
    size_t get_variable_count() const noexcept;

    // 钩子函数需要访问的公有方法
    /**
     * @brief 获取对应脚本类型的停止请求标志引用
     * @param script_type 脚本类型
     * @return std::atomic<bool>& 停止请求标志引用
     */
    std::atomic<bool>& get_stop_requested_ref(LuaScriptType script_type);
    
    /**
     * @brief 获取对应脚本类型的挂起请求标志引用
     * @param script_type 脚本类型
     * @return std::atomic<bool>& 挂起请求标志引用
     */
    std::atomic<bool>& get_suspend_requested_ref(LuaScriptType script_type);
    
    /**
     * @brief 获取对应脚本类型的执行互斥锁引用
     * @param script_type 脚本类型
     * @return std::mutex& 执行互斥锁引用
     */
    std::mutex& get_execution_mutex_ref(LuaScriptType script_type);
    
    /**
     * @brief 获取对应脚本类型的执行条件变量引用
     * @param script_type 脚本类型
     * @return std::condition_variable& 执行条件变量引用
     */
    std::condition_variable& get_execution_cv_ref(LuaScriptType script_type);

    /**
     * @brief 获取对应脚本类型的执行上下文引用
     * @param script_type 脚本类型
     * @return LuaExecutionContext& 执行上下文引用
     */
    LuaExecutionContext& get_execution_context_ref(LuaScriptType script_type);

    /**
     * @brief 检查指定行号是否存在启用的断点
     * @param line_number 行号
     * @param script_type 脚本类型
     * @return bool 是否存在启用的断点
     */
    bool should_break_at_line(int line_number, LuaScriptType script_type) const;

    /**
     * @brief 静态Lua钩子包装函数
     * @param lua_state Lua状态机
     * @param debug_info 调试信息
     */
    // 为MA脚本优化的静态钩子函数
    static void lua_hook_call_MA(lua_State* lua_state, lua_Debug* debug_info);
    
    // 为PR脚本优化的静态钩子函数  
    static void lua_hook_call_PR(lua_State* lua_state, lua_Debug* debug_info);

private:
    // 私有成员变量
    std::unordered_map<std::string, VariableValue> m_variables; // 通道变量系统容器
    sol::state m_MA_lua, m_PR_lua; // Lua状态机，用于执行Lua脚本，这里有2个，一个是通道执行的工艺使用的状态机，一个是通道执行的通道保护工艺的状态机
    mutable std::mutex m_variables_mutex;
    
    // ========================================================================
    // Lua脚本执行控制相关成员变量
    // ========================================================================
    
    // 执行上下文
    LuaExecutionContext m_MA_execution_context;    ///< MA脚本执行上下文
    LuaExecutionContext m_PR_execution_context;    ///< PR脚本执行上下文
    
    // 控制变量
    std::atomic<bool> m_MA_stop_requested{false};   ///< MA脚本停止请求
    std::atomic<bool> m_PR_stop_requested{false};   ///< PR脚本停止请求
    std::atomic<bool> m_MA_suspend_requested{false}; ///< MA脚本挂起请求
    std::atomic<bool> m_PR_suspend_requested{false}; ///< PR脚本挂起请求
    
    // 同步原语
    mutable std::mutex m_MA_execution_mutex;        ///< MA脚本执行互斥锁
    mutable std::mutex m_PR_execution_mutex;        ///< PR脚本执行互斥锁
    std::condition_variable m_MA_execution_cv;      ///< MA脚本执行条件变量
    std::condition_variable m_PR_execution_cv;      ///< PR脚本执行条件变量
    
    // 断点管理
    std::set<DebugBreakpoint> m_MA_breakpoints;     ///< MA脚本断点集合
    std::set<DebugBreakpoint> m_PR_breakpoints;     ///< PR脚本断点集合
    mutable std::mutex m_breakpoints_mutex;         ///< 断点互斥锁
    
    // 执行线程
    std::unique_ptr<std::thread> m_MA_execution_thread; ///< MA脚本执行线程
    std::unique_ptr<std::thread> m_PR_execution_thread; ///< PR脚本执行线程

    // 内部辅助方法
    sol::object push_variable_to_MA_sol(const VariableValue& value) const;
    sol::object push_variable_to_PR_sol(const VariableValue& value) const;
    VariableValue get_variable_from_MA_sol(const sol::object& obj) const;
    VariableValue get_variable_from_PR_sol(const sol::object& obj) const;
    std::string get_type_name(const VariableValue& value) const;
    
    // 用户脚本变量过滤函数
    bool is_user_script_variable(const std::string& variable_name) const;
    
    // ========================================================================
    // Lua钩子函数相关私有方法
    // ========================================================================
    
    /**
     * @brief 设置Lua钩子函数
     * @param lua_state Lua状态机引用
     * @param script_type 脚本类型
     */
    void setup_lua_hook(sol::state& lua_state, LuaScriptType script_type);
    
    /**
     * @brief 脚本执行线程函数
     * @param script_content 脚本内容
     * @param script_name 脚本名称
     * @param script_type 脚本类型
     */
    void script_execution_thread(const std::string& script_content, 
                                const std::string& script_name,
                                LuaScriptType script_type);
    
    /**
     * @brief 获取对应脚本类型的Lua状态机引用
     * @param script_type 脚本类型
     * @return sol::state& Lua状态机引用
     */
    sol::state& get_lua_state_ref(LuaScriptType script_type);
    
    /**
     * @brief 获取对应脚本类型的断点集合引用
     * @param script_type 脚本类型
     * @return std::set<DebugBreakpoint>& 断点集合引用
     */
    std::set<DebugBreakpoint>& get_breakpoints_ref(LuaScriptType script_type);
    
    /**
     * @brief 获取对应脚本类型的执行上下文常量引用
     * @param script_type 脚本类型
     * @return const LuaExecutionContext& 执行上下文常量引用
     */
    const LuaExecutionContext& get_execution_context_ref_const(LuaScriptType script_type) const;
};