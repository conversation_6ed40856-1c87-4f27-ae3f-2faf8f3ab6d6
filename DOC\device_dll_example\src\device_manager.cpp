#include "device_manager.hpp"
#include "eth_client_interface.hpp"
#include <atomic>
#include <cstdint>
#include <iostream>
#include <algorithm>
#include <chrono>
#include <thread>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <cstring>
#include <windows.h>

namespace EthClient {

// 设备管理器实现
DeviceManager::DeviceManager(std::unique_ptr<ConfigManager> config_manager)
    : m_config_manager(std::move(config_manager))
    , m_initialized(false)
    , m_shutdown_requested(false)
    , m_connection_timeout_ms(5000)
    , m_command_timeout_ms(3000)
    , m_total_commands_sent(0)
    , m_total_errors(0)
    , m_total_reconnections(0) {
    
    if (!m_config_manager) {
        throw std::invalid_argument("ConfigManager cannot be null");
    }
}

DeviceManager::~DeviceManager() {
    shutdown();
}

DeviceManager::DeviceManager(DeviceManager&& other) noexcept
    : m_config_manager(std::move(other.m_config_manager))
    , m_devices(std::move(other.m_devices))
    , m_device_index(std::move(other.m_device_index))
    , m_initialized(other.m_initialized.load())
    , m_shutdown_requested(other.m_shutdown_requested.load())
    , m_connection_timeout_ms(other.m_connection_timeout_ms.load())
    , m_command_timeout_ms(other.m_command_timeout_ms.load())
    , m_total_commands_sent(other.m_total_commands_sent.load())
    , m_total_errors(other.m_total_errors.load())
    , m_total_reconnections(other.m_total_reconnections.load())
    , m_last_error(std::move(other.m_last_error)) {
    
    other.m_shutdown_requested = true;
}

DeviceManager& DeviceManager::operator=(DeviceManager&& other) noexcept {
    if (this != &other) {
        shutdown();
        
        m_config_manager = std::move(other.m_config_manager);
    
        m_devices = std::move(other.m_devices);
        m_device_index = std::move(other.m_device_index);
        m_initialized = other.m_initialized.load();
        m_shutdown_requested = other.m_shutdown_requested.load();
        m_connection_timeout_ms = other.m_connection_timeout_ms.load();
        m_command_timeout_ms = other.m_command_timeout_ms.load();
        m_total_commands_sent = other.m_total_commands_sent.load();
        m_total_errors = other.m_total_errors.load();
        m_total_reconnections = other.m_total_reconnections.load();
        m_last_error = std::move(other.m_last_error);
        
        other.m_shutdown_requested = true;
    }
    return *this;
}

ATE_EC DeviceManager::initialize() {
    std::lock_guard<std::mutex> lock(m_devices_mutex);
    
    // 加载配置文件
    ATE_EC result = m_config_manager->load_config();
    if (result != ATE_SUCCESS) {
        m_set_last_error("Failed to load config: " + m_last_error);
        return result;
    }

    // 从配置初始化设备
    std::vector<DeviceConfig> device_configs = m_config_manager->get_device_configs();
    for (const auto& config : device_configs) {
        // 设备配置检查 - 假设所有设备都已启用
        ATE_EC result = add_device(config);
        if (result != ATE_SUCCESS) {
            m_set_last_error("Failed to add device " + config.instance_id + ": " + m_last_error);
            // 继续处理其他设备
        }
    }
    
    return ATE_SUCCESS;
}

ATE_EC DeviceManager::shutdown() {
    // 全局监控功能已移除，改为设备独立线程管理
    
    // 停止所有设备线程并断开设备连接
    {
        std::lock_guard<std::mutex> lock(m_devices_mutex);
        for (auto& [instance_id, device] : m_devices) {
            // 停止设备工作线程
            stop_device_worker_thread(*device);
            
            // 断开modbus客户端连接
            if (device->tcp_udp_client) {
        device->tcp_udp_client->disconnect();
            }
        }
        m_devices.clear();
        m_device_index.clear();
    }
    

    
    return ATE_SUCCESS;
}

ATE_EC DeviceManager::remove_device(const std::string& instance_id) {
    std::lock_guard<std::mutex> lock(m_devices_mutex);
    
    auto it = m_device_index.find(instance_id);
    if (it == m_device_index.end()) {
        m_set_last_error("Device not found: " + instance_id);
        return ATE_ERROR_DEVICE_NOT_FOUND;
    }
    
    auto device_it = m_devices.find(instance_id);
    if (device_it != m_devices.end()) {
        // 停止设备专用工作线程
        stop_device_worker_thread(*device_it->second);
        
        // 断开设备连接
        if (device_it->second->tcp_udp_client) {
            device_it->second->tcp_udp_client->disconnect();
        }
        
        m_devices.erase(device_it);
    }
    
    m_device_index.erase(it);
    
    return ATE_SUCCESS;
}

ATE_EC DeviceManager::connect_device(const std::string& instance_id) {
    // 使用共享锁查找设备，允许并行查找
    std::shared_ptr<ManagedDevice> device_ptr;
    {
        std::shared_lock<std::shared_mutex> shared_lock(m_devices_shared_mutex);
        auto it = m_devices.find(instance_id);
        if (it == m_devices.end()) {
            m_set_last_error("Device not found: " + instance_id);
            return ATE_ERROR_DEVICE_NOT_FOUND;
        }
        device_ptr = std::shared_ptr<ManagedDevice>(it->second.get(), [](ManagedDevice*){});
    }
    
    // 使用设备级锁进行连接操作，实现设备并行
    std::lock_guard<std::mutex> device_lock(device_ptr->device_mutex);
    auto& device = *device_ptr;
    if (!device.tcp_udp_client) {
        m_set_last_error("Device has no Modbus client: " + instance_id);
        return ATE_ERROR_INVALID_PARAMETER;
    }
    // 1.链接设备
    ATE_EC result = device.tcp_udp_client->connect();
    if (result == ATE_SUCCESS) {
        device.connection_status = ConnectionStatus::CONNECTED;
        device.last_activity_timestamp = get_timestamp_ms();
        
        // 2.启动设备专用工作线程
        ATE_EC thread_result = start_device_worker_thread(device);
        if (thread_result != ATE_SUCCESS) {
            // 如果线程启动失败，断开连接
            device.tcp_udp_client->disconnect();
            device.connection_status = ConnectionStatus::DISCONNECTED;
            m_set_last_error("Failed to start device worker thread for: " + instance_id);
            return thread_result;
        }
        

    } else {
        device.connection_status = ConnectionStatus::DISCONNECTED;
        {
            std::lock_guard<std::mutex> stats_lock(device.statistics_mutex);
            device.statistics.failed_commands++;
        }
        add_device_error_log(const_cast<ManagedDevice&>(device), device.tcp_udp_client->get_last_error());
        m_set_last_error(device.tcp_udp_client->get_last_error());
    }
    
    return result;
}

ATE_EC DeviceManager::disconnect_device(const std::string& instance_id) {
    // 使用共享锁查找设备，允许并行查找
    std::shared_ptr<ManagedDevice> device_ptr;
    {
        std::shared_lock<std::shared_mutex> shared_lock(m_devices_shared_mutex);
        auto it = m_devices.find(instance_id);
        if (it == m_devices.end()) {
            m_set_last_error("Device not found: " + instance_id);
            return ATE_ERROR_DEVICE_NOT_FOUND;
        }
        device_ptr = std::shared_ptr<ManagedDevice>(it->second.get(), [](ManagedDevice*){});
    }
    
    // 使用设备级锁进行断开操作，实现设备并行
    std::lock_guard<std::mutex> device_lock(device_ptr->device_mutex);
    auto& device = *device_ptr;
    
    // 停止设备专用工作线程
    stop_device_worker_thread(device);
    
    if (device.tcp_udp_client) {
        device.tcp_udp_client->disconnect();
    }
        
    device.connection_status = ConnectionStatus::DISCONNECTED;
    

    
    return ATE_SUCCESS;
}

std::string DeviceManager::read_channel_state(const std::string& instance_id, ChannelId channel_id) {
    std::shared_lock<std::shared_mutex> lock(m_devices_shared_mutex);
    
    auto it = m_devices.find(instance_id);
    if (it == m_devices.end()) {
        m_set_last_error("Device not found: " + instance_id);
        return "{\"error\": \"Device not found\", \"instance_id\": \"" + instance_id + "\"}";
    }
    
    const auto& device = *it->second;
    if (!device.tcp_udp_client || device.connection_status != ConnectionStatus::CONNECTED) {
        m_set_last_error("Device not connected: " + instance_id);
        return "{\"error\": \"Device not connected\", \"instance_id\": \"" + instance_id + "\"}";
    }
    
    // 直接使用设备的全局数据块
    E5000DataBlockV2 data;
    {
        std::lock_guard<std::mutex> lock(device.global_data_mutex);
        data = device.global_data; // 复制全局数据
    }
    
    // 构建完整的JSON状态字符串，包含E5000DataBlockV2的所有字段
    std::string json_state = "{";
    
    // 基本信息
    json_state += "\"channel_id\": " + std::to_string(channel_id) + ",";
    json_state += "\"instance_id\": \"" + instance_id + "\",";
    
    // 测量数据 - Measurement data
    json_state += "\"measurement\": {";
    json_state += "\"irms\": " + std::to_string(data.irms) + ",";
    json_state += "\"urms\": " + std::to_string(data.urms) + ",";
    json_state += "\"prms\": " + std::to_string(data.prms) + ",";
    json_state += "\"ipeak\": " + std::to_string(data.ipeak) + ",";
    json_state += "\"upeak\": " + std::to_string(data.upeak) + ",";
    json_state += "\"ppeak\": " + std::to_string(data.ppeak) + ",";
    json_state += "\"ivalley\": " + std::to_string(data.ivalley) + ",";
    json_state += "\"uvalley\": " + std::to_string(data.uvalley) + ",";
    json_state += "\"pvalley\": " + std::to_string(data.pvalley);
    json_state += "},";
    
    // 运行状态 - Operating status
    json_state += "\"status\": {";
    json_state += "\"load_run_state_01\": " + std::to_string(data.load_run_state_01) + ",";
    json_state += "\"load_run_state_02\": " + std::to_string(data.load_run_state_02) + ",";
    json_state += "\"load_run_state_03\": " + std::to_string(data.load_run_state_03) + ",";
    json_state += "\"load_run_state_04\": " + std::to_string(data.load_run_state_04) + ",";
    json_state += "\"system_run_state_01\": " + std::to_string(data.system_run_state_01) + ",";
    json_state += "\"run_stop_show\": " + std::to_string(data.run_stop_show) + ",";
    json_state += "\"short_show\": " + std::to_string(data.short_show) + ",";
    json_state += "\"run_mode\": " + std::to_string(data.run_mode) + ",";
    json_state += "\"data_valid\": " + std::to_string(data.data_valid);
    json_state += "},";
    
    // 错误状态 - Error status
    json_state += "\"errors\": {";
    json_state += "\"load_error_01\": " + std::to_string(data.load_error_01) + ",";
    json_state += "\"load_error_02\": " + std::to_string(data.load_error_02) + ",";
    json_state += "\"load_error_03\": " + std::to_string(data.load_error_03) + ",";
    json_state += "\"load_error_04\": " + std::to_string(data.load_error_04) + ",";
    json_state += "\"system_error_01\": " + std::to_string(data.system_error_01);
    json_state += "},";
    
    // 设定值 - Setpoint values
    json_state += "\"setpoints\": {";
    json_state += "\"voltage_setpoint\": " + std::to_string(data.voltage_setpoint) + ",";
    json_state += "\"current_setpoint\": " + std::to_string(data.current_setpoint) + ",";
    json_state += "\"power_setpoint\": " + std::to_string(data.power_setpoint);
    json_state += "},";
    
    // 模式档位 - Mode levels
    json_state += "\"mode_levels\": {";
    json_state += "\"cc_i_lv\": " + std::to_string(data.cc_i_lv) + ",";
    json_state += "\"cc_v_lv\": " + std::to_string(data.cc_v_lv) + ",";
    json_state += "\"cv_v_lv\": " + std::to_string(data.cv_v_lv) + ",";
    json_state += "\"cv_i_lv\": " + std::to_string(data.cv_i_lv) + ",";
    json_state += "\"cp_p_lv\": " + std::to_string(data.cp_p_lv) + ",";
    json_state += "\"cp_v_lv\": " + std::to_string(data.cp_v_lv) + ",";
    json_state += "\"cr_r_lv\": " + std::to_string(data.cr_r_lv) + ",";
    json_state += "\"cr_i_lv\": " + std::to_string(data.cr_i_lv) + ",";
    json_state += "\"ccd_i_lv\": " + std::to_string(data.ccd_i_lv) + ",";
    json_state += "\"ccd_v_lv\": " + std::to_string(data.ccd_v_lv) + ",";
    json_state += "\"crd_r_lv\": " + std::to_string(data.crd_r_lv) + ",";
    json_state += "\"crd_i_lv\": " + std::to_string(data.crd_i_lv) + ",";
    json_state += "\"swd_i_lv\": " + std::to_string(data.swd_i_lv) + ",";
    json_state += "\"swd_v_lv\": " + std::to_string(data.swd_v_lv) + ",";
    json_state += "\"swp_i_lv\": " + std::to_string(data.swp_i_lv) + ",";
    json_state += "\"swp_v_lv\": " + std::to_string(data.swp_v_lv);
    json_state += "},";
    
    // 设备连接状态
    json_state += "\"connection_status\": " + std::to_string(static_cast<int>(device.connection_status.load())) + ",";
    json_state += "\"last_activity\": " + std::to_string(device.last_activity_timestamp.load());
    
    json_state += "}";
    
    return json_state;
}

ATE_EC DeviceManager::write_command_data(const std::string& instance_id, const std::string& hex_string) {
    std::shared_lock<std::shared_mutex> lock(m_devices_shared_mutex);
    
    auto it = m_devices.find(instance_id);
    if (it == m_devices.end()) {
        m_set_last_error("Device not found: " + instance_id);
        return ATE_ERROR_DEVICE_NOT_FOUND;
    }
    
    auto& device = *it->second;
    if (!device.tcp_udp_client || device.connection_status != ConnectionStatus::CONNECTED) {
        m_set_last_error("Device not connected: " + instance_id);
        return ATE_ERROR_DEVICE_OFFLINE;
    }
    // 将hex字符串转换为字节数组
    std::vector<uint8_t> command_data;
    for (size_t i = 0; i < hex_string.length(); i += 2) {
        if (i + 1 < hex_string.length()) {
            std::string byte_string = hex_string.substr(i, 2);
            uint8_t byte = static_cast<uint8_t>(std::stoul(byte_string, nullptr, 16));
            command_data.push_back(byte);
        }
    }

    ATE_EC result = device.tcp_udp_client->add_write_command(command_data, "Raw hex command");
    if (result != ATE_SUCCESS) {
        {
            std::lock_guard<std::mutex> stats_lock(device.statistics_mutex);
            device.statistics.failed_commands++;
        }
        add_device_error_log(device, device.tcp_udp_client->get_last_error());
        m_set_last_error(device.tcp_udp_client->get_last_error());
        return result;
    }
    
    {
        std::lock_guard<std::mutex> stats_lock(device.statistics_mutex);
        device.statistics.successful_commands++;
        device.statistics.total_commands_sent++;
    }
    
    return ATE_SUCCESS;
}

std::vector<std::string> DeviceManager::get_device_list() const {
    std::shared_lock<std::shared_mutex> lock(m_devices_shared_mutex);
    std::vector<std::string> device_list;
    device_list.reserve(m_devices.size());
    
    for (const auto& pair : m_devices) {
        device_list.push_back(pair.first);
    }
    
    return device_list;
}

bool DeviceManager::get_device_config(const std::string& instance_id, DeviceConfig& config) const {
    std::shared_lock<std::shared_mutex> lock(m_devices_shared_mutex);
    auto it = m_devices.find(instance_id);
    if (it != m_devices.end()) {
        config = it->second->config;
        return true;
    }
    return false;
}

// get_device_descriptor函数已删除 - 使用DeviceConfig.config_json字段

bool DeviceManager::get_device_statistics(const std::string& instance_id, DeviceStatistics& stats) const {
    std::shared_lock<std::shared_mutex> lock(m_devices_shared_mutex);
    
    auto it = m_devices.find(instance_id);
    if (it == m_devices.end()) {
        return false;
    }
    
    const auto& device = *it->second;
    {
        std::lock_guard<std::mutex> stats_lock(device.statistics_mutex);
        stats = device.statistics;
    }
    
    return true;
}

void DeviceManager::reset_device_statistics(const std::string& instance_id) {
    std::shared_lock<std::shared_mutex> lock(m_devices_shared_mutex);
    
    auto it = m_devices.find(instance_id);
    if (it != m_devices.end()) {
        auto& device = *it->second;
        std::lock_guard<std::mutex> stats_lock(device.statistics_mutex);
        device.statistics.total_commands_sent = 0;
        device.statistics.successful_commands = 0;
        device.statistics.failed_commands = 0;
        device.statistics.connection_attempts = 0;
        device.statistics.successful_connections = 0;
        device.statistics.last_communication_timestamp = 0;
        device.statistics.average_response_time_ms = 0.0;
    }
}

void DeviceManager::reset_all_statistics() {
    std::shared_lock<std::shared_mutex> lock(m_devices_shared_mutex);
    
    m_total_commands_sent = 0;
    m_total_errors = 0;
    m_total_reconnections = 0;
    
    for (auto& [instance_id, device_ptr] : m_devices) {
        auto& device = *device_ptr;
        {
            std::lock_guard<std::mutex> stats_lock(device.statistics_mutex);
            device.statistics.total_commands_sent = 0;
            device.statistics.successful_commands = 0;
            device.statistics.failed_commands = 0;
            device.statistics.connection_attempts = 0;
            device.statistics.successful_connections = 0;
            device.statistics.last_communication_timestamp = 0;
            device.statistics.average_response_time_ms = 0.0;
        }
        {
            std::lock_guard<std::mutex> device_lock(device.device_mutex);
            device.error_log.clear();
        }
    }
}

std::string DeviceManager::get_last_error() const {
    return m_last_error;
}

// 私有方法
ATE_EC DeviceManager::add_device(const DeviceConfig& config) {
    // 检查设备是否已存在
    if (m_device_index.find(config.instance_id) != m_device_index.end()) {
        m_set_last_error("Device already exists: " + config.instance_id);
        return ATE_ERROR_DEVICE_ALREADY_EXISTS;
    }
    
    // 创建托管设备
    auto device = std::make_unique<ManagedDevice>(config);
    device->tcp_udp_client = std::make_unique<TcpUdpClient>(config);
    device->connection_status = ConnectionStatus::DISCONNECTED;
    device->thread_state = ThreadState::STOPPED;
    device->last_activity_timestamp = 0;
    
    // 初始化统计信息
    device->statistics.total_commands_sent = 0;
    device->statistics.successful_commands = 0;
    device->statistics.failed_commands = 0;
    device->statistics.connection_attempts = 0;
    device->statistics.successful_connections = 0;
    device->statistics.last_communication_timestamp = 0;
    device->statistics.average_response_time_ms = 0.0;
    
    // 设置超时
    device->tcp_udp_client->set_timeout(config.communication.timeout_ms);
    
    // 添加到集合中
    m_device_index[config.instance_id] = m_devices.size();
    m_devices[config.instance_id] = std::move(device);
    
    return ATE_SUCCESS;
}

void DeviceManager::m_set_last_error(const std::string& error) {
    m_last_error = error;
}



// ManagedDevice实现
// ManagedDevice构造函数已在头文件中内联定义

DeviceManager::ManagedDevice::ManagedDevice(ManagedDevice&& other) noexcept
    : config(std::move(other.config))
    , tcp_udp_client(std::move(other.tcp_udp_client))
    , connection_status(other.connection_status.load())
    , thread_state(other.thread_state.load())
    , statistics(std::move(other.statistics))
    , error_log(std::move(other.error_log))
    , last_activity_timestamp(other.last_activity_timestamp.load()) {
}

DeviceManager::ManagedDevice& DeviceManager::ManagedDevice::operator=(ManagedDevice&& other) noexcept {
    if (this != &other) {
        config = std::move(other.config);
        tcp_udp_client = std::move(other.tcp_udp_client);
        connection_status = other.connection_status.load();
        thread_state = other.thread_state.load();
        statistics = std::move(other.statistics);
        error_log = std::move(other.error_log);
        last_activity_timestamp = other.last_activity_timestamp.load();
    }
    return *this;
}

// Device thread management functions
ATE_EC DeviceManager::start_device_thread(const std::string& instance_id) {
    std::shared_lock<std::shared_mutex> lock(m_devices_shared_mutex);
    auto it = m_devices.find(instance_id);
    if (it == m_devices.end()) {
        m_set_last_error("Device not found: " + instance_id);
        return ATE_ERROR_DEVICE_NOT_FOUND;
    }
    
    return start_device_worker_thread(*it->second);
}

ATE_EC DeviceManager::stop_device_thread(const std::string& instance_id) {
    std::shared_lock<std::shared_mutex> lock(m_devices_shared_mutex);
    auto it = m_devices.find(instance_id);
    if (it == m_devices.end()) {
        m_set_last_error("Device not found: " + instance_id);
        return ATE_ERROR_DEVICE_NOT_FOUND;
    }
    
    stop_device_worker_thread(*it->second);
    return ATE_SUCCESS;
}

ATE_EC DeviceManager::pause_device_thread(const std::string& instance_id) {
    std::shared_lock<std::shared_mutex> lock(m_devices_shared_mutex);
    auto it = m_devices.find(instance_id);
    if (it == m_devices.end()) {
        m_set_last_error("Device not found: " + instance_id);
        return ATE_ERROR_DEVICE_NOT_FOUND;
    }
    
    std::lock_guard<std::mutex> device_lock(it->second->device_mutex);
    it->second->thread_paused = true;
    it->second->thread_state = ThreadState::PAUSED;
    return ATE_SUCCESS;
}

ATE_EC DeviceManager::resume_device_thread(const std::string& instance_id) {
    std::shared_lock<std::shared_mutex> lock(m_devices_shared_mutex);
    auto it = m_devices.find(instance_id);
    if (it == m_devices.end()) {
        m_set_last_error("Device not found: " + instance_id);
        return ATE_ERROR_DEVICE_NOT_FOUND;
    }
    
    std::lock_guard<std::mutex> device_lock(it->second->device_mutex);
    it->second->thread_paused = false;
    it->second->thread_state = ThreadState::RUNNING;
    it->second->thread_cv.notify_one();
    return ATE_SUCCESS;
}

ATE_EC DeviceManager::start_device_worker_thread(ManagedDevice& device) {
    // Stop existing thread if running
    stop_device_worker_thread(device);
    
    // Reset thread control flags
    device.thread_stop_requested = false;
    device.thread_paused = false;
    device.thread_state = ThreadState::RUNNING;
    
    // Start new worker thread
    try {
        device.worker_thread = std::make_unique<std::thread>(
            &DeviceManager::device_worker_loop, this, device.config.instance_id
        );
        return ATE_SUCCESS;
    } catch (const std::exception& e) {
        m_set_last_error("Failed to start device worker thread: " + std::string(e.what()));
        device.thread_state = ThreadState::STOPPED;
        return ATE_ERROR_THREAD_CREATE_FAILED;
    }
}

void DeviceManager::stop_device_worker_thread(ManagedDevice& device) {
    if (device.worker_thread && device.worker_thread->joinable()) {
        // Signal thread to stop
        device.thread_stop_requested = true;
        device.thread_state = ThreadState::STOPPED;
        
        // Wake up thread if it's waiting
        device.thread_cv.notify_all();
        
        // Wait for thread to finish
        device.worker_thread->join();
        device.worker_thread.reset();
    }
}

void DeviceManager::device_worker_loop(const std::string& instance_id) {
    auto last_data_read = std::chrono::steady_clock::now();
    auto last_connection_check = std::chrono::steady_clock::now();
    const auto data_read_interval = std::chrono::milliseconds(10);   // 10ms数据收发间隔
    const auto connection_check_interval = std::chrono::seconds(5);   // 5秒连接检查间隔
    
    while (true) {
        // 查找设备（线程安全访问）
        ManagedDevice* device = nullptr;
        {
            std::shared_lock<std::shared_mutex> lock(m_devices_shared_mutex);
            auto it = m_devices.find(instance_id);
            if (it == m_devices.end()) {
                break; // 设备已移除
            }
            device = it->second.get();
        }
        
        // 检查线程是否应该停止
        if (device->thread_stop_requested) {
            break;
        }
        
        // 处理暂停状态
        if (device->thread_paused) {
            std::unique_lock<std::mutex> lock(device->thread_mutex);
            device->thread_cv.wait(lock, [&device] {
                return !device->thread_paused || device->thread_stop_requested;
            });
            continue;
        }
        
        auto now = std::chrono::steady_clock::now();
        
        // 检查设备连接状态（每5秒一次）
        if (now - last_connection_check >= connection_check_interval) {
            if (device->connection_status == ConnectionStatus::DISCONNECTED) {
                // 尝试重新连接
                if (device->tcp_udp_client) {
            ATE_EC result = device->tcp_udp_client->connect();
                    if (result == ATE_SUCCESS) {
                        device->connection_status = ConnectionStatus::CONNECTED;
                        device->last_activity_timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                            std::chrono::steady_clock::now().time_since_epoch()
                        ).count();
                        m_total_reconnections++;
                        

                    } else {
                        add_device_error_log(*device, device->tcp_udp_client->get_last_error());
                    }
                }
            }
            last_connection_check = now;
        }
        
        // 处理待发送的写入命令（仅当设备连接时）
        if (device->connection_status == ConnectionStatus::CONNECTED && 
            device->thread_state == ThreadState::RUNNING &&
            device->tcp_udp_client &&
            now - last_data_read >= data_read_interval) {
            
            try {
                // 处理TcpUdpClient中接收到的数据
                device->tcp_udp_client->process_received_data();    
                // 处理TcpUdpClient中的待发送命令
                device->tcp_udp_client->process_pending_commands();
            } catch (const std::exception& e) {
                add_device_error_log(*device, "Write command processing exception: " + std::string(e.what()));
            }
            last_data_read = now;
        }
    }
    
    // 线程正在停止
    std::shared_lock<std::shared_mutex> lock(m_devices_shared_mutex);
    auto it = m_devices.find(instance_id);
    if (it != m_devices.end()) {
        it->second->thread_state = ThreadState::STOPPED;
    }
}

const ConfigManager* DeviceManager::get_config_manager() const {
    return m_config_manager.get();
}

void DeviceManager::add_device_error_log(ManagedDevice& device, const std::string& error_message) {
    std::lock_guard<std::mutex> lock(device.device_mutex);
    
    // 添加时间戳到错误消息
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;
    
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count();
    ss << " [" << device.config.instance_id << "] " << error_message;
    
    std::string timestamped_error = ss.str();
    
    // 添加到内存日志
    device.error_log.push_back(timestamped_error);
    
    // 如果超过100条，写入文件并清理内存
    if (device.error_log.size() > 100) {
        write_error_log_to_file(device);
        device.error_log.clear();
    }
}

void DeviceManager::write_error_log_to_file(const ManagedDevice& device) {
    try {
        // 获取DLL所在目录
        char dll_path[MAX_PATH];
        HMODULE hModule = GetModuleHandle("KwlEthClient_V01.dll");
        if (hModule == nullptr) {
            hModule = GetModuleHandle(nullptr); // 获取当前进程模块
        }
        
        if (GetModuleFileNameA(hModule, dll_path, MAX_PATH) == 0) {
            return; // 无法获取DLL路径
        }
        
        // 提取目录路径
        std::string dll_dir = dll_path;
        size_t last_slash = dll_dir.find_last_of("\\");
        if (last_slash != std::string::npos) {
            dll_dir = dll_dir.substr(0, last_slash);
        }
        
        // 构造设备专用日志文件路径
        std::string log_file_path = dll_dir + "/device_error_" + device.config.instance_id + ".log";
#ifdef _WIN32
        // Windows系统：将正斜杠替换为反斜杠
        std::replace(log_file_path.begin(), log_file_path.end(), '/', '\\');
#endif
        
        // 以追加模式打开文件
        std::ofstream log_file(log_file_path, std::ios::app);
        if (!log_file.is_open()) {
            return; // 无法打开文件
        }
        
        // 写入所有错误日志
        for (const auto& error : device.error_log) {
            log_file << error << std::endl;
        }
        
        log_file.close();
    } catch (const std::exception&) {
        // 忽略文件写入错误，避免影响主要功能
    }
}

// 读取接收缓冲区数据
ATE_EC DeviceManager::read_received_buffer_data(const std::string& instance_id, uint8_t* buffer, int32_t buffer_size, int32_t* response_size) {
    if (!buffer || buffer_size == 0) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    std::shared_lock<std::shared_mutex> lock(m_devices_shared_mutex);
    
    auto it = m_devices.find(instance_id);
    if (it == m_devices.end()) {
        m_set_last_error("Device not found: " + instance_id);
        return ATE_ERROR_DEVICE_NOT_FOUND;
    }
    
    auto& device = it->second;
    if (!device->tcp_udp_client) {
        m_set_last_error("TCP/UDP client not initialized for device: " + instance_id);
        return ATE_ERROR_DEVICE_OFFLINE;
    }
    
    // 获取接收缓冲区中的所有数据
    std::vector<uint8_t> received_data = device->tcp_udp_client->get_received_data();
    
    if (received_data.empty()) {
        if (response_size) {
            *response_size = 0;
        }
        return ATE_SUCCESS;
    }
    
    int32_t data_size = static_cast<int32_t>(received_data.size());
    int32_t copy_size = std::min(data_size, buffer_size);
    
    // 按照FIFO原则复制数据到外部缓冲区
    std::memcpy(buffer, received_data.data(), copy_size);
    
    // 清空已读取的数据
    device->tcp_udp_client->clear_received_data();
    
    // 如果数据超过缓冲区大小，前面超出的部分已经被丢弃（通过只复制copy_size字节实现）
    if (response_size) {
        *response_size = copy_size;
    }
    return ATE_SUCCESS;
}

// 工厂函数
std::shared_ptr<TcpUdpClient> DeviceManager::get_modbus_client(const std::string& instance_id) const {
    std::shared_lock<std::shared_mutex> lock(m_devices_shared_mutex);
    
    auto it = m_devices.find(instance_id);
    if (it == m_devices.end()) {
        return nullptr;
    }
    
    // 返回TcpUdpClient的shared_ptr
    // 注意：这里需要小心处理生命周期
    // 由于TcpUdpClient是通过unique_ptr管理的，我们需要小心处理
    return std::shared_ptr<TcpUdpClient>(it->second->tcp_udp_client.get(), [](TcpUdpClient*){});
}

std::unique_ptr<DeviceManager> create_device_manager(std::unique_ptr<ConfigManager> config_manager) {
    return std::make_unique<DeviceManager>(std::move(config_manager));
}

} // namespace EthClient