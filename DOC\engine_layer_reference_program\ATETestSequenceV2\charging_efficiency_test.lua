-- ============================================================================
-- 充电效率测试序列 (二级子序列) - Step-Based Framework
-- 文件: charging_efficiency_test.lua
-- 描述: 充电效率测试，包含pre、seq、post三个标签分区
-- 版本: V2.1 (标准化step-based工步控制框架)
-- ============================================================================

-- 工步控制变量
local step = 0
local run_flag = {1, 1, 1, 1, 1, 1, 1, 0, 0, 0}  -- 前7个工步启用
local test_result = "UNKNOWN"
local error_status = {}

-- 测试数据记录
local test_data_points = {}
local measurement_count = 0

-- 固定功能begin
function add_string(str)
    if not error_status[str] then
        error_status[str] = true
        return true
    end
    return false
end

-- 工步保护状态检查
function seq_protect_check()
    add_string("SomeThingError")
end

-- 工步保护动作执行
function seq_protect_act()
    if (next(error_status) ~= nil) then
        -- 通过故障保护动作的组合返回做3种情况，只记录+不动作+继续执行，记录+动作+继续执行，记录+动作+停止执行
        --无函数体 不动作
        -- return 1 动作后返回1  则动作了 后面停止执行
        -- return 2 动作了返回2  则动作了 后面继续执行
    end
end
-- 固定功能end

-- 子序列全局变量定义
charging_efficiency_test = {
    test_result = "UNKNOWN",
    test_name = "充电效率测试",
    start_time = 0,
    end_time = 0,
    step_count = 0,
    error_messages = {},
    efficiency_data = {},
    final_efficiency = 0.0
}

-- 测试参数配置
local test_params = {
    input_voltage_nominal = 220.0,      -- 标称输入电压(V)
    input_voltage_tolerance = 0.05,     -- 输入电压容差(5%)
    output_voltage_levels = {12.0, 24.0, 48.0}, -- 输出电压等级(V)
    load_current_levels = {1.0, 2.5, 5.0, 7.5, 10.0}, -- 负载电流等级(A)
    min_efficiency_requirement = 0.90,  -- 最小效率要求(90%)
    measurement_duration = 10.0,        -- 每个测试点测量时长(s)
    stabilization_time = 5.0,          -- 稳定时间(s)
    measurement_interval = 1.0          -- 测量间隔(s)
}

-- 固定功能begin
function add_string(str)
    if not error_status[str] then
        error_status[str] = true
        return true
    end
    return false
end

-- 工步保护状态检查
function seq_protect_check()
    -- 检查设备连接状态
    if device_initialized and not dll_check_device_status() then
        add_string("DeviceConnectionLost")
        return false
    end
    
    -- 检查交流电源状态
    if main.device_channels.ac_power_source then
        local ac_status = dll_query_command(main.device_channels.ac_power_source, "STATUS?")
        if ac_status == "ERROR" then
            add_string("ACPowerSourceError")
            return false
        end
    end
    
    -- 检查功率分析仪状态
    if main.device_channels.power_analyzer then
        local analyzer_status = dll_query_command(main.device_channels.power_analyzer, "STATUS?")
        if analyzer_status == "ERROR" then
            add_string("PowerAnalyzerError")
            return false
        end
    end
    
    return true
end

-- 工步保护动作执行
function seq_protect_act()
    if (next(error_status) ~= nil) then
        -- 通过故障保护动作的组合返回做3种情况，只记录+不动作+继续执行，记录+动作+继续执行，记录+动作+停止执行
        for error_key, _ in pairs(error_status) do
            log_message("ERROR", "[充电效率] 保护动作触发: " .. error_key)
            
            if error_key == "DeviceConnectionLost" then
                add_efficiency_error("设备连接丢失")
                test_result = "FAILED"
                return 1  -- 停止执行
            elseif error_key == "ACPowerSourceError" then
                add_efficiency_error("交流电源设备故障")
                test_result = "FAILED"
                return 1  -- 停止执行
            elseif error_key == "PowerAnalyzerError" then
                add_efficiency_error("功率分析仪设备故障")
                test_result = "FAILED"
                return 1  -- 停止执行
            end
        end
    end
    
    return 0  -- 默认继续执行
end

-- 自定精确延时函数（毫秒级）
-- @param x 总延时时间（毫秒）
function delayms(x)
    local sleepms = 10  -- 每次循环基础延时
    local loops = math.floor(x / sleepms)  -- 完整循环次数
    local remainder = x % sleepms  -- 剩余延时
    
    -- 执行完整循环延时
    for i = 1, loops do
        -- 休眠循环基础延时 sleep_ms 采用C++注册的方式，注册一个通用的接口给lua
        sleep_ms(sleepms)
        if seq_protect_check() == false then
            return
        end
    end
    
    -- 执行剩余延时
    if remainder > 0 then
        -- 休眠毫秒剩余
        sleep_ms(remainder)
        if seq_protect_check() == false then
            return
        end
    end
end
-- 固定功能end

-- 工具函数
-- function log_efficiency_message(level, message)
--     local timestamp = os.date("%Y-%m-%d %H:%M:%S")
--     print(string.format("[%s] [充电效率] %s: %s", timestamp, level, message))
-- end

function add_efficiency_error(message)
    table.insert(charging_efficiency_test.error_messages, message)
    log_message("ERROR", "[充电效率] " .. message)
end

function record_efficiency_data(voltage, current, input_power, output_power, efficiency)
    table.insert(charging_efficiency_test.efficiency_data, {
        output_voltage = voltage,
        output_current = current,
        input_power = input_power,
        output_power = output_power,
        efficiency = efficiency,
        timestamp = os.time()
    })
end

function calculate_efficiency(input_power, output_power)
    if input_power <= 0 then
        return 0.0
    end
    return (output_power / input_power) * 100.0
end

-- JSON报告写入函数
function write_efficiency_json_report()
    local test_duration = charging_efficiency_test.end_time - charging_efficiency_test.start_time
    local completed_steps = step
    
    local json_report = {
        test_name = charging_efficiency_test.test_name,
        test_result = charging_efficiency_test.test_result,
        test_duration = test_duration,
        completed_steps = completed_steps,
        step_count = charging_efficiency_test.step_count,
        start_time = charging_efficiency_test.start_time,
        end_time = charging_efficiency_test.end_time,
        final_efficiency = charging_efficiency_test.final_efficiency,
        error_status = next(error_status) and error_status or {},
        error_messages = charging_efficiency_test.error_messages,
        efficiency_data = charging_efficiency_test.efficiency_data,
        test_params = test_params
    }
    
    -- 调用C++接口写入JSON文件
    local timestamp = os.date("%Y%m%d_%H%M%S")
    local filename = string.format("efficiency_test_report_%s.json", timestamp)
    write_test_report_json(filename, json_report)
    log_message("INFO", "[充电效率] JSON报告已写入: " .. filename)
end

-- ============================================================================
-- PRE 标签 - 预处理阶段
-- ============================================================================
::pre::
log_message("INFO", "[充电效率] === 开始充电效率测试预处理阶段 ===")
charging_efficiency_test.start_time = os.time()
charging_efficiency_test.step_count = 0
charging_efficiency_test.error_messages = {}
charging_efficiency_test.efficiency_data = {}

-- 检查设备状态
if not device_initialized then
    add_efficiency_error("设备未初始化，无法执行充电效率测试")
    charging_efficiency_test.test_result = "FAILED"
    goto post
end

if not power_supply_ready then
    add_efficiency_error("电源设备未就绪，无法执行充电效率测试")
    charging_efficiency_test.test_result = "FAILED"
    goto post
end

-- 检查电子负载是否可用
local load_status = dll_query_command(main.device_channels.electronic_load, "STATUS?")
if load_status == "ERROR" then
    add_efficiency_error("电子负载设备不可用")
    charging_efficiency_test.test_result = "FAILED"
    goto post
end

-- 检查功率分析仪是否可用
local analyzer_status = dll_query_command(main.device_channels.power_analyzer, "STATUS?")
if analyzer_status == "ERROR" then
    add_efficiency_error("功率分析仪设备不可用")
    charging_efficiency_test.test_result = "FAILED"
    goto post
end

-- 配置交流电源
log_efficiency_message("INFO", "配置交流电源...")
dll_send_command(main.device_channels.ac_power_source, "OUTPUT:OFF")
    dll_send_command(main.device_channels.ac_power_source, string.format("VOLTAGE:%f", test_params.input_voltage_nominal))
    dll_send_command(main.device_channels.ac_power_source, "FREQUENCY:50.0")
    dll_send_command(main.device_channels.ac_power_source, "CURRENT:20.0")

-- 配置功率分析仪
log_efficiency_message("INFO", "配置功率分析仪...")
dll_send_command(main.device_channels.power_analyzer, "RESET")
    dll_send_command(main.device_channels.power_analyzer, "VOLTAGE:RANGE:AUTO")
    dll_send_command(main.device_channels.power_analyzer, "CURRENT:RANGE:AUTO")
    dll_send_command(main.device_channels.power_analyzer, "POWER:RANGE:AUTO")
    dll_send_command(main.device_channels.power_analyzer, "INTEGRATION:MODE:CONTINUOUS")

-- 配置电子负载
log_efficiency_message("INFO", "配置电子负载...")
dll_send_command(main.device_channels.electronic_load, "INPUT:OFF")
    dll_send_command(main.device_channels.electronic_load, "MODE:CURRENT")
    dll_send_command(main.device_channels.electronic_load, "CURRENT:RANGE:HIGH")

-- 配置直流电源(被测设备电源)
log_efficiency_message("INFO", "配置直流电源...")
dll_send_command(main.device_channels.power_supply, "OUTPUT:OFF")
    dll_send_command(main.device_channels.power_supply, "VOLTAGE:12.0")
    dll_send_command(main.device_channels.power_supply, "CURRENT:15.0")

-- 等待设备稳定
log_efficiency_message("INFO", "等待设备稳定...")
os.execute("timeout /t 3 /nobreak > nul")

log_efficiency_message("INFO", "=== 充电效率测试预处理阶段完成 ===")

-- ============================================================================
-- SEQ 标签 - 测试序列阶段
-- ============================================================================
::seq::
-- 工步1: 启动测试设备
if run_flag[1] ~= 0 then
    step = 1
    log_efficiency_message("INFO", "工步1: 启动测试设备")
    charging_efficiency_test.step_count = charging_efficiency_test.step_count + 1
    
    -- 启动交流电源
    log_efficiency_message("INFO", "启动交流电源...")
    local ac_result = dll_send_command(main.device_channels.ac_power_source, "OUTPUT:ON")
    if ac_result == "OK" then
        -- 等待2秒稳定
        local delay_time = 2000
        local sleepms = 10
        local loops = math.floor(delay_time / sleepms)
        local remainder = delay_time % sleepms
        
        for i = 1, loops do
            sleep_ms(sleepms)
            seq_protect_check()
            if seq_protect_act() == 1 then
                goto post
            end
        end
        
        if remainder > 0 then
            sleep_ms(remainder)
            seq_protect_check()
            if seq_protect_act() == 1 then
                goto post
            end
        end
                
        -- 启动功率分析仪
        log_efficiency_message("INFO", "启动功率分析仪...")
        local analyzer_result = dll_send_command(main.device_channels.power_analyzer, "INTEGRATION:START")
        if analyzer_result == "OK" then
            test_result = "PASSED"
        else
            add_efficiency_error("功率分析仪启动失败")
            test_result = "FAILED"
        end
    else
        add_efficiency_error("交流电源启动失败")
        test_result = "FAILED"
    end
    
    -- 工步1故障保护检查
    seq_protect_check()
    if seq_protect_act() == 1 then
        goto post
    end
end
    
-- 工步2: 12V电压等级效率测试
if run_flag[2] ~= 0 then
    step = 2
    log_efficiency_message("INFO", "工步2: 12V电压等级效率测试")
    charging_efficiency_test.step_count = charging_efficiency_test.step_count + 1
    
    local output_voltage = test_params.output_voltage_levels[1]  -- 12V
    local voltage_test_passed = true
    
    -- 设置被测设备输出电压
    dll_send_command(main.device_channels.power_supply, string.format("VOLTAGE:%.1f", output_voltage))
    dll_send_command(main.device_channels.power_supply, "OUTPUT:ON")
    
    -- 等待电压稳定
    local delay_time = test_params.stabilization_time * 1000
    local sleepms = 10
    local loops = math.floor(delay_time / sleepms)
    local remainder = delay_time % sleepms
    
    for i = 1, loops do
        sleep_ms(sleepms)
        seq_protect_check()
        if seq_protect_act() == 1 then
            goto post
        end
    end
    
    if remainder > 0 then
        sleep_ms(remainder)
        seq_protect_check()
        if seq_protect_act() == 1 then
            goto post
        end
    end
            
            -- 遍历所有负载电流等级
            for current_index, load_current in ipairs(test_params.load_current_levels) do
                log_efficiency_message("INFO", string.format("测试负载电流: %.1fA", load_current))
                
                -- 设置电子负载电流
                dll_send_command(main.device_channels.electronic_load, string.format("CURRENT:%.1f", load_current))
                dll_send_command(main.device_channels.electronic_load, "INPUT:ON")
                
                -- 等待负载稳定
                local delay_time = test_params.stabilization_time * 1000
                local sleepms = 10
                local loops = math.floor(delay_time / sleepms)
                local remainder = delay_time % sleepms
                
                for i = 1, loops do
                    sleep_ms(sleepms)
                    seq_protect_check()
                    if seq_protect_act() == 1 then
                        goto post
                    end
                end
                
                if remainder > 0 then
                    sleep_ms(remainder)
                    seq_protect_check()
                    if seq_protect_act() == 1 then
                        goto post
                    end
                end
                
                -- 调用三级子序列进行详细效率测量
                log_efficiency_message("INFO", "调用效率测量详细子序列...")
                dofile("ATETestSequenceV2/efficiency_measurement_detail.lua")
                
                -- 从三级子序列获取测量结果
                local avg_input_power = efficiency_measurement_detail.avg_input_power or 0.0
                local avg_output_power = efficiency_measurement_detail.avg_output_power or 0.0
                local efficiency = efficiency_measurement_detail.calculated_efficiency or 0.0
                
                -- 记录测试数据
                record_efficiency_data(output_voltage, load_current, avg_input_power, avg_output_power, efficiency)
                
                -- 检查效率要求
                if efficiency < (test_params.min_efficiency_requirement * 100.0) then
                    add_efficiency_error(string.format("效率不达标: %.1fV/%.1fA, 效率=%.2f%% (要求≥%.1f%%)", 
                                        output_voltage, load_current, efficiency, test_params.min_efficiency_requirement * 100.0))
                    voltage_test_passed = false
                end
                
                log_efficiency_message("INFO", string.format("测量完成: %.1fV/%.1fA, 输入=%.2fW, 输出=%.2fW, 效率=%.2f%%", 
                                      output_voltage, load_current, avg_input_power, avg_output_power, efficiency))
                
                -- 关闭电子负载
                dll_send_command(main.device_channels.electronic_load, "INPUT:OFF")
                -- 等待1秒
                local delay_time = 1000
                local sleepms = 10
                local loops = math.floor(delay_time / sleepms)
                local remainder = delay_time % sleepms
                
                for i = 1, loops do
                    sleep_ms(sleepms)
                    seq_protect_check()
                    if seq_protect_act() == 1 then
                        goto post
                    end
                end
                
                if remainder > 0 then
                    sleep_ms(remainder)
                    seq_protect_check()
                    if seq_protect_act() == 1 then
                        goto post
                    end
                end
            end
            
            -- 关闭被测设备输出
            dll_send_command(main.device_channels.power_supply, "OUTPUT:OFF")
            -- 等待2秒
            local delay_time = 2000
            local sleepms = 10
            local loops = math.floor(delay_time / sleepms)
            local remainder = delay_time % sleepms
            
            for i = 1, loops do
                sleep_ms(sleepms)
                seq_protect_check()
                if seq_protect_act() == 1 then
                    goto post
                end
            end
            
            if remainder > 0 then
                sleep_ms(remainder)
                seq_protect_check()
                if seq_protect_act() == 1 then
                    goto post
                end
            end
            
            if voltage_test_passed then
                test_result = "PASSED"
            else
                test_result = "FAILED"
            end
    
    -- 工步2故障保护检查
    seq_protect_check()
    if seq_protect_act() == 1 then
        goto post
    end
end
    
-- 工步3: 24V电压等级效率测试
if run_flag[3] ~= 0 then
    step = 3
    log_efficiency_message("INFO", "工步3: 24V电压等级效率测试")
    charging_efficiency_test.step_count = charging_efficiency_test.step_count + 1
    
    local output_voltage = test_params.output_voltage_levels[2]  -- 24V
    local voltage_test_passed = true
    
    -- 设置被测设备输出电压
    dll_send_command(main.device_channels.power_supply, string.format("VOLTAGE:%.1f", output_voltage))
    dll_send_command(main.device_channels.power_supply, "OUTPUT:ON")
    
    -- 等待电压稳定
    local delay_time = test_params.stabilization_time * 1000
    local sleepms = 10
    local loops = math.floor(delay_time / sleepms)
    local remainder = delay_time % sleepms
    
    for i = 1, loops do
        sleep_ms(sleepms)
        seq_protect_check()
        if seq_protect_act() == 1 then
            goto post
        end
    end
    
    if remainder > 0 then
        sleep_ms(remainder)
        seq_protect_check()
        if seq_protect_act() == 1 then
            goto post
        end
    end
            
            -- 遍历所有负载电流等级
            for current_index, load_current in ipairs(test_params.load_current_levels) do
                log_efficiency_message("INFO", string.format("测试负载电流: %.1fA", load_current))
                
                -- 设置电子负载电流
                dll_send_command(main.device_channels.electronic_load, string.format("CURRENT:%.1f", load_current))
                dll_send_command(main.device_channels.electronic_load, "INPUT:ON")
                
                -- 等待负载稳定
                local delay_time = test_params.stabilization_time * 1000
                local sleepms = 10
                local loops = math.floor(delay_time / sleepms)
                local remainder = delay_time % sleepms
                
                for i = 1, loops do
                    sleep_ms(sleepms)
                    seq_protect_check()
                    if seq_protect_act() == 1 then
                        goto post
                    end
                end
                
                if remainder > 0 then
                    sleep_ms(remainder)
                    seq_protect_check()
                    if seq_protect_act() == 1 then
                        goto post
                    end
                end
                
                -- 调用三级子序列进行详细效率测量
                log_efficiency_message("INFO", "调用效率测量详细子序列...")
                dofile("ATETestSequenceV2/efficiency_measurement_detail.lua")
                
                -- 从三级子序列获取测量结果
                local avg_input_power = efficiency_measurement_detail.avg_input_power or 0.0
                local avg_output_power = efficiency_measurement_detail.avg_output_power or 0.0
                local efficiency = efficiency_measurement_detail.calculated_efficiency or 0.0
                
                -- 记录测试数据
                record_efficiency_data(output_voltage, load_current, avg_input_power, avg_output_power, efficiency)
                
                -- 检查效率要求
                if efficiency < (test_params.min_efficiency_requirement * 100.0) then
                    add_efficiency_error(string.format("效率不达标: %.1fV/%.1fA, 效率=%.2f%% (要求≥%.1f%%)", 
                                        output_voltage, load_current, efficiency, test_params.min_efficiency_requirement * 100.0))
                    voltage_test_passed = false
                end
                
                log_efficiency_message("INFO", string.format("测量完成: %.1fV/%.1fA, 输入=%.2fW, 输出=%.2fW, 效率=%.2f%%", 
                                      output_voltage, load_current, avg_input_power, avg_output_power, efficiency))
                
                -- 关闭电子负载
                dll_send_command(main.device_channels.electronic_load, "INPUT:OFF")
                -- 等待1秒
                local delay_time = 1000
                local sleepms = 10
                local loops = math.floor(delay_time / sleepms)
                local remainder = delay_time % sleepms
                
                for i = 1, loops do
                    sleep_ms(sleepms)
                    seq_protect_check()
                    if seq_protect_act() == 1 then
                        goto post
                    end
                end
                
                if remainder > 0 then
                    sleep_ms(remainder)
                    seq_protect_check()
                    if seq_protect_act() == 1 then
                        goto post
                    end
                end
            end
            
            -- 关闭被测设备输出
            dll_send_command(main.device_channels.power_supply, "OUTPUT:OFF")
            -- 等待2秒
            local delay_time = 2000
            local sleepms = 10
            local loops = math.floor(delay_time / sleepms)
            local remainder = delay_time % sleepms
            
            for i = 1, loops do
                sleep_ms(sleepms)
                seq_protect_check()
                if seq_protect_act() == 1 then
                    goto post
                end
            end
            
            if remainder > 0 then
                sleep_ms(remainder)
                seq_protect_check()
                if seq_protect_act() == 1 then
                    goto post
                end
            end
            
            if voltage_test_passed then
                test_result = "PASSED"
            else
                test_result = "FAILED"
            end
    
    -- 工步3故障保护检查
    seq_protect_check()
    if seq_protect_act() == 1 then
        goto post
    end
end

-- 工步4: 48V电压等级效率测试和结果计算
if run_flag[4] ~= 0 then
    step = 4
            log_efficiency_message("INFO", "工步4: 48V电压等级效率测试和结果计算")
            charging_efficiency_test.step_count = charging_efficiency_test.step_count + 1
            
            local output_voltage = test_params.output_voltage_levels[3]  -- 48V
            local voltage_test_passed = true
            
            -- 设置被测设备输出电压
            dll_send_command(main.device_channels.power_supply, string.format("VOLTAGE:%.1f", output_voltage))
            dll_send_command(main.device_channels.power_supply, "OUTPUT:ON")
            
            -- 等待电压稳定
            local delay_time = test_params.stabilization_time * 1000
            local sleepms = 10
            local loops = math.floor(delay_time / sleepms)
            local remainder = delay_time % sleepms
            
            for i = 1, loops do
                sleep_ms(sleepms)
                seq_protect_check()
                if seq_protect_act() == 1 then
                    goto post
                end
            end
            
            if remainder > 0 then
                sleep_ms(remainder)
                seq_protect_check()
                if seq_protect_act() == 1 then
                    goto post
                end
            end
            
            -- 遍历所有负载电流等级
            for current_index, load_current in ipairs(test_params.load_current_levels) do
                log_efficiency_message("INFO", string.format("测试负载电流: %.1fA", load_current))
                
                -- 设置电子负载电流
                dll_send_command(main.device_channels.electronic_load, string.format("CURRENT:%.1f", load_current))
                dll_send_command(main.device_channels.electronic_load, "INPUT:ON")
                
                -- 等待负载稳定
                local delay_time = test_params.stabilization_time * 1000
                local sleepms = 10
                local loops = math.floor(delay_time / sleepms)
                local remainder = delay_time % sleepms
                
                for i = 1, loops do
                    sleep_ms(sleepms)
                    seq_protect_check()
                    if seq_protect_act() == 1 then
                        goto post
                    end
                end
                
                if remainder > 0 then
                    sleep_ms(remainder)
                    seq_protect_check()
                    if seq_protect_act() == 1 then
                        goto post
                    end
                end
                
                -- 调用三级子序列进行详细效率测量
                log_efficiency_message("INFO", "调用效率测量详细子序列...")
                dofile("ATETestSequenceV2/efficiency_measurement_detail.lua")
                
                -- 从三级子序列获取测量结果
                local avg_input_power = efficiency_measurement_detail.avg_input_power or 0.0
                local avg_output_power = efficiency_measurement_detail.avg_output_power or 0.0
                local efficiency = efficiency_measurement_detail.calculated_efficiency or 0.0
                
                -- 记录测试数据
                record_efficiency_data(output_voltage, load_current, avg_input_power, avg_output_power, efficiency)
                
                -- 检查效率要求
                if efficiency < (test_params.min_efficiency_requirement * 100.0) then
                    add_efficiency_error(string.format("效率不达标: %.1fV/%.1fA, 效率=%.2f%% (要求≥%.1f%%)", 
                                        output_voltage, load_current, efficiency, test_params.min_efficiency_requirement * 100.0))
                    voltage_test_passed = false
                end
                
                log_efficiency_message("INFO", string.format("测量完成: %.1fV/%.1fA, 输入=%.2fW, 输出=%.2fW, 效率=%.2f%%", 
                                      output_voltage, load_current, avg_input_power, avg_output_power, efficiency))
                
                -- 关闭电子负载
                dll_send_command(main.device_channels.electronic_load, "INPUT:OFF")
                -- 等待1秒
                local delay_time = 1000
                local sleepms = 10
                local loops = math.floor(delay_time / sleepms)
                local remainder = delay_time % sleepms
                
                for i = 1, loops do
                    sleep_ms(sleepms)
                    seq_protect_check()
                    if seq_protect_act() == 1 then
                        goto post
                    end
                end
                
                if remainder > 0 then
                    sleep_ms(remainder)
                    seq_protect_check()
                    if seq_protect_act() == 1 then
                        goto post
                    end
                end
            end
            
            -- 关闭被测设备输出
            dll_send_command(main.device_channels.power_supply, "OUTPUT:OFF")
            -- 等待2秒
            local delay_time = 2000
            local sleepms = 10
            local loops = math.floor(delay_time / sleepms)
            local remainder = delay_time % sleepms
            
            for i = 1, loops do
                sleep_ms(sleepms)
                seq_protect_check()
                if seq_protect_act() == 1 then
                    goto post
                end
            end
            
            if remainder > 0 then
                sleep_ms(remainder)
                seq_protect_check()
                if seq_protect_act() == 1 then
                    goto post
                end
            end
            
            -- 计算总体效率
            local total_efficiency_sum = 0.0
            local valid_measurements = 0
            
            for _, data in ipairs(charging_efficiency_test.efficiency_data) do
                if data.efficiency > 0 then
                    total_efficiency_sum = total_efficiency_sum + data.efficiency
                    valid_measurements = valid_measurements + 1
                end
            end
            
            if valid_measurements > 0 then
                charging_efficiency_test.final_efficiency = total_efficiency_sum / valid_measurements
            else
                charging_efficiency_test.final_efficiency = 0.0
                add_efficiency_error("没有有效的效率测量数据")
                voltage_test_passed = false
            end
            
            -- 判断最终测试结果
            local final_test_passed = voltage_test_passed and 
                                    (#charging_efficiency_test.error_messages == 0) and 
                                    (charging_efficiency_test.final_efficiency >= (test_params.min_efficiency_requirement * 100.0))
            
            if final_test_passed then
                log_efficiency_message("INFO", string.format("充电效率测试通过，平均效率: %.2f%%", charging_efficiency_test.final_efficiency))
                test_result = "PASSED"
            else
                log_efficiency_message("ERROR", string.format("充电效率测试失败，平均效率: %.2f%%", charging_efficiency_test.final_efficiency))
                test_result = "FAILED"
            end
    
    -- 工步4故障保护检查
    seq_protect_check()
    if seq_protect_act() == 1 then
        goto post
    end
end

-- ============================================================================
-- POST 标签 - 后处理阶段
-- ============================================================================
::post::
log_efficiency_message("INFO", "=== 开始充电效率测试后处理阶段 ===")
charging_efficiency_test.end_time = os.time()

-- 关闭所有设备输出
log_efficiency_message("INFO", "关闭所有设备输出...")
dll_send_command(main.device_channels.ac_power_source, "OUTPUT:OFF")
dll_send_command(main.device_channels.power_supply, "OUTPUT:OFF")
dll_send_command(main.device_channels.electronic_load, "INPUT:OFF")
log_message("INFO", "[充电效率] 所有设备输出已关闭")

-- 停止功率分析仪
log_message("INFO", "[充电效率] 停止功率分析仪...")
dll_send_command(main.device_channels.power_analyzer, "INTEGRATION:STOP")
log_message("INFO", "[充电效率] 功率分析仪已停止")

-- 计算完成的工步数
local completed_steps = step - 1
if test_result == "FAILED" then
    completed_steps = step  -- 失败时包含当前工步
end

-- 生成测试报告
local test_duration = charging_efficiency_test.end_time - charging_efficiency_test.start_time
local total_measurements = #charging_efficiency_test.efficiency_data

-- 确定最终测试结果
if #charging_efficiency_test.error_messages == 0 and 
   charging_efficiency_test.final_efficiency >= (test_params.min_efficiency_requirement * 100.0) then
    charging_efficiency_test.test_result = "PASS"
else
    charging_efficiency_test.test_result = "FAIL"
end

-- 生成step-based测试报告
local test_report = {
    test_name = charging_efficiency_test.test_name,
    test_result = test_result,
    start_time = charging_efficiency_test.start_time,
    end_time = charging_efficiency_test.end_time,
    duration_seconds = test_duration,
    total_steps = 4,  -- 总共4个工步
    completed_steps = completed_steps,
    measurement_count = total_measurements,
    average_efficiency = charging_efficiency_test.final_efficiency,
    efficiency_requirement = test_params.min_efficiency_requirement * 100.0,
    error_status = error_status,
    error_count = #charging_efficiency_test.error_messages,
    error_messages = charging_efficiency_test.error_messages,
    detailed_efficiency_data = charging_efficiency_test.efficiency_data
}

-- 输出测试结果
print("\n" .. string.rep("-", 60))
print("充电效率测试结果报告 (Step-Based Framework)")
print(string.rep("-", 60))
print(string.format("测试名称: %s", test_report.test_name))
print(string.format("测试结果: %s", test_report.test_result))
print(string.format("测试时长: %d 秒", test_report.duration_seconds))
print(string.format("完成工步: %d/%d", test_report.completed_steps, test_report.total_steps))
print(string.format("测量点数: %d", test_report.measurement_count))
print(string.format("平均效率: %.2f%%", test_report.average_efficiency))
print(string.format("效率要求: ≥%.1f%%", test_report.efficiency_requirement))
print(string.format("错误状态: %s", test_report.error_status or "None"))
print(string.format("错误数量: %d", test_report.error_count))

if test_report.error_count > 0 then
    print("错误信息:")
    for i, error_msg in ipairs(test_report.error_messages) do
        print(string.format("  %d. %s", i, error_msg))
    end
end

-- 输出详细效率数据
if test_report.measurement_count > 0 then
    print("\n详细效率数据:")
    print("电压(V)  电流(A)  输入功率(W)  输出功率(W)  效率(%)")
    print(string.rep("-", 60))
    for _, data in ipairs(test_report.detailed_efficiency_data) do
        print(string.format("%6.1f  %6.1f  %10.2f  %10.2f  %7.2f", 
              data.output_voltage, data.output_current, 
              data.input_power, data.output_power, data.efficiency))
    end
end
print(string.rep("-", 60))

-- 写入JSON报告
write_efficiency_json_report()

log_message("INFO", "[充电效率] === 充电效率测试后处理阶段完成 ===")
log_message("INFO", string.format("[充电效率] 充电效率测试最终结果: %s，完成工步: %d/%d", test_result, completed_steps, 4))

-- 返回测试结果
return test_result