-- ============================================================================
-- 温度循环测试序列 (二级子测试序列) - Step-based框架
-- 测试充电桩在不同温度条件下的性能表现
-- 基于工步控制的测试序列框架，支持保护检查和精确延时
-- 版本: V2.1 (标准化step-based工步控制框架)
-- ============================================================================

-- 工步控制变量
local step = 0
local run_flag = {1, 1, 1, 1, 1, 0, 0, 0, 0, 0}  -- 前5个工步启用
local test_result = "UNKNOWN"
local error_status = {}

-- 测试数据记录
local test_data_points = {}
local measurement_count = 0

-- 全局变量定义 (二级子序列变量，可被main.lua访问)
temperature_cycle_test = {
    test_name = "温度循环测试",
    test_result = "UNKNOWN",
    start_time = 0,
    end_time = 0,
    step_count = 0,
    test_data = {},
    error_messages = {},
    cycle_count = 0,
    max_temperature_reached = 0.0,
    min_temperature_reached = 0.0
}

-- 固定功能begin
function add_string(str)
    if not error_status[str] then
        error_status[str] = true
        return true
    end
    return false
end

-- 工步保护状态检查
function seq_protect_check()
    -- 检查设备状态和温度异常
    local chamber_status = dll_query_command(main.device_channels.thermal_chamber, "STATUS?")
    if chamber_status == "ERROR" then
        add_string("温度箱设备故障")
        return false
    end
    
    local power_status = dll_query_command(main.device_channels.power_supply, "STATUS?")
    if power_status == "ERROR" then
        add_string("电源设备故障")
        return false
    end
    
    -- 检查温度是否超出安全范围
    local current_temp_str = dll_query_command(main.device_channels.thermal_chamber, "TEMPERATURE?")
    local current_temp = tonumber(current_temp_str) or 0.0
    if current_temp > 100.0 or current_temp < -50.0 then
        add_string(string.format("温度超出安全范围: %.1f°C", current_temp))
        return false
    end
    
    return true
end

-- 工步保护动作执行
function seq_protect_act()
    -- 发生故障时的保护动作
    dll_send_command(main.device_channels.thermal_chamber, "STOP")
    dll_send_command(main.device_channels.power_supply, "OUTPUT:OFF")
    
    if next(error_status) ~= nil then
        for error_key, _ in pairs(error_status) do
            log_message("ERROR", "[温度循环] 执行保护动作: " .. error_key)
        end
    end
    -- return true 动作后返回true 则动作了 后面停止
    -- return false 动作了返回false 则表示 后面 继续执行
    return true
end

-- 自定精确延时函数（毫秒级）
-- @param x 总延时时间（毫秒）
function delayms(x)
    local sleepms = 10  -- 每次循环基础延时
    local loops = math.floor(x / sleepms)  -- 完整循环次数
    local remainder = x % sleepms  -- 剩余延时
    
    -- 执行完整循环延时
    for i = 1, loops do
        -- 休眠循环基础延时 sleep_ms 采用C++注册的方式，注册一个通用的接口给lua
        sleep_ms(sleepms)
        if seq_protect_check() == false then
            return
        end
    end
    
    -- 执行剩余延时
    if remainder > 0 then
        -- 休眠毫秒剩余
        sleep_ms(remainder)
        if seq_protect_check() == false then
            return
        end
    end
end
-- 固定功能end

-- 测试参数配置
local test_params = {
    temperature_high = 85.0,        -- 高温点(°C)
    temperature_low = -40.0,        -- 低温点(°C)
    temperature_ambient = 25.0,     -- 环境温度(°C)
    temperature_tolerance = 2.0,    -- 温度容差(°C)
    ramp_rate = 5.0,               -- 升降温速率(°C/min)
    hold_time_high = 30.0,         -- 高温保持时间(min)
    hold_time_low = 30.0,          -- 低温保持时间(min)
    cycle_count = 3,               -- 循环次数
    stabilization_time = 10.0,     -- 温度稳定时间(min)
    measurement_interval = 60.0,   -- 测量间隔(s)
    max_temperature_deviation = 3.0, -- 最大温度偏差(°C)
    dut_power_on = true,           -- 测试期间DUT是否上电
    monitoring_points = {          -- 温度监测点
        "DUT_INTERNAL",
        "DUT_SURFACE", 
        "CHAMBER_AIR",
        "DUT_CONNECTOR"
    }
}

-- 工具函数
-- function log_temp_message(level, message)
--     local timestamp = os.date("%Y-%m-%d %H:%M:%S")
--     print(string.format("[%s] [温度循环] %s: %s", timestamp, level, message))
-- end

function add_temp_error(message)
    table.insert(temperature_cycle_test.error_messages, message)
    log_message("ERROR", "[温度循环] " .. message)
end

function record_temp_data(cycle, phase, target_temp, actual_temps, dut_status)
    table.insert(temperature_cycle_test.test_data, {
        cycle = cycle,
        phase = phase,
        target_temperature = target_temp,
        actual_temperatures = actual_temps,
        dut_status = dut_status,
        timestamp = os.time()
    })
end

function wait_for_temperature_stable(target_temp, tolerance, max_wait_minutes)
    local start_time = os.clock()
    local stable_count = 0
    local required_stable_count = 3  -- 需要连续3次测量都在容差范围内
    
    while (os.clock() - start_time) < (max_wait_minutes * 60) do
        local chamber_temp_str = dll_query_command(main.device_channels.thermal_chamber, "TEMPERATURE?")
        local chamber_temp = tonumber(chamber_temp_str) or 0.0
        
        local temp_diff = math.abs(chamber_temp - target_temp)
        
        if temp_diff <= tolerance then
            stable_count = stable_count + 1
            if stable_count >= required_stable_count then
                log_message("INFO", string.format("[温度循环] 温度已稳定在 %.1f°C (目标: %.1f°C)", chamber_temp, target_temp))
                return true
            end
        else
            stable_count = 0
        end
        
        log_message("INFO", string.format("[温度循环] 等待温度稳定: 当前%.1f°C, 目标%.1f°C, 偏差%.1f°C", 
                        chamber_temp, target_temp, temp_diff))
        
        os.execute("timeout /t 30 /nobreak > nul")  -- 等待30秒
    end
    
    add_temp_error(string.format("温度稳定超时: 目标%.1f°C, 最大等待时间%.1f分钟", target_temp, max_wait_minutes))
    return false
end

function measure_all_temperatures()
    local temperatures = {}
    
    for _, point in ipairs(test_params.monitoring_points) do
        local temp_str = dll_query_command(main.device_channels.thermal_chamber, string.format("TEMPERATURE:%s?", point))
        local temp = tonumber(temp_str) or 0.0
        temperatures[point] = temp
    end
    
    return temperatures
end

function check_dut_functionality()
    -- 检查DUT基本功能
    local voltage_str = dll_query_command(main.device_channels.power_supply, "MEASURE:VOLTAGE?")
        local current_str = dll_query_command(main.device_channels.power_supply, "MEASURE:CURRENT?")
    local voltage = tonumber(voltage_str) or 0.0
    local current = tonumber(current_str) or 0.0
    
    -- 检查通信是否正常
    local comm_status = dll_query_command(main.device_channels.dut_communication, "STATUS?")
    
    return {
        voltage = voltage,
        current = current,
        communication = comm_status,
        functional = (voltage > 10.0 and current > 0.1 and comm_status == "OK")
    }
end

-- 写入温度循环测试JSON报告
function write_temperature_cycle_json_report()
    local test_duration = temperature_cycle_test.end_time - temperature_cycle_test.start_time
    local total_measurements = #temperature_cycle_test.test_data
    local completed_steps = step - 1
    local total_steps = 4
    
    -- 计算循环统计
    local cycle_stats = {}
    for _, data in ipairs(temperature_cycle_test.test_data) do
        local cycle = data.cycle
        if not cycle_stats[cycle] then
            cycle_stats[cycle] = {high_count = 0, low_count = 0, high_temps = {}, low_temps = {}}
        end
        if data.phase == "HIGH_TEMP" then
            cycle_stats[cycle].high_count = cycle_stats[cycle].high_count + 1
            if data.actual_temperatures and data.actual_temperatures.CHAMBER_AIR then
                table.insert(cycle_stats[cycle].high_temps, data.actual_temperatures.CHAMBER_AIR)
            end
        elseif data.phase == "LOW_TEMP" then
            cycle_stats[cycle].low_count = cycle_stats[cycle].low_count + 1
            if data.actual_temperatures and data.actual_temperatures.CHAMBER_AIR then
                table.insert(cycle_stats[cycle].low_temps, data.actual_temperatures.CHAMBER_AIR)
            end
        end
    end
    
    -- 构建JSON报告数据
    local json_report = {
        test_name = temperature_cycle_test.test_name,
        test_result = temperature_cycle_test.test_result,
        test_duration_seconds = test_duration,
        test_duration_hours = test_duration / 3600.0,
        completed_steps = completed_steps,
        total_steps = total_steps,
        completion_percentage = (completed_steps / total_steps) * 100,
        step_count = temperature_cycle_test.step_count,
        completed_cycles = temperature_cycle_test.cycle_count,
        target_cycles = test_params.cycle_count,
        measurement_count = total_measurements,
        max_temperature_reached = temperature_cycle_test.max_temperature_reached,
        min_temperature_reached = temperature_cycle_test.min_temperature_reached,
        temperature_range = {
            low = test_params.temperature_low,
            high = test_params.temperature_high,
            ambient = test_params.temperature_ambient
        },
        error_count = #temperature_cycle_test.error_messages,
        error_messages = temperature_cycle_test.error_messages,
        test_parameters = {
            temperature_high = test_params.temperature_high,
            temperature_low = test_params.temperature_low,
            temperature_ambient = test_params.temperature_ambient,
            temperature_tolerance = test_params.temperature_tolerance,
            ramp_rate = test_params.ramp_rate,
            hold_time_high = test_params.hold_time_high,
            hold_time_low = test_params.hold_time_low,
            cycle_count = test_params.cycle_count,
            stabilization_time = test_params.stabilization_time,
            measurement_interval = test_params.measurement_interval,
            max_temperature_deviation = test_params.max_temperature_deviation,
            dut_power_on = test_params.dut_power_on,
            monitoring_points = test_params.monitoring_points
        },
        test_data = temperature_cycle_test.test_data,
        cycle_statistics = cycle_stats,
        start_time = temperature_cycle_test.start_time,
        end_time = temperature_cycle_test.end_time
    }
    
    -- 调用C++接口写入JSON报告
    local json_str = json.encode(json_report)
    write_test_report_json(temperature_cycle_test.test_name, json_str)
    
    log_message("[温度循环] JSON测试报告已生成", "INFO")
end

-- ============================================================================
-- PRE 标签 - 预处理阶段
-- ============================================================================
::pre::
log_message("[温度循环] === 开始温度循环测试预处理阶段 ===", "INFO")
temperature_cycle_test.start_time = os.time()
temperature_cycle_test.step_count = 0
temperature_cycle_test.error_messages = {}
temperature_cycle_test.test_data = {}
temperature_cycle_test.cycle_count = 0

-- 检查设备状态
if not device_initialized then
    add_temp_error("设备未初始化，无法执行温度循环测试")
    temperature_cycle_test.test_result = "FAILED"
    goto post
end

-- 检查温度箱是否可用
local chamber_status = dll_query_command(main.device_channels.thermal_chamber, "STATUS?")
if chamber_status == "ERROR" then
    add_temp_error("温度箱设备不可用")
    temperature_cycle_test.test_result = "FAILED"
    goto post
end

-- 检查电源是否可用
local power_status = dll_query_command(main.device_channels.power_supply, "STATUS?")
if power_status == "ERROR" then
    add_temp_error("电源设备不可用")
    temperature_cycle_test.test_result = "FAILED"
    goto post
end

-- 检查DUT通信是否可用
local comm_status = dll_query_command(main.device_channels.dut_communication, "STATUS?")
if comm_status == "ERROR" then
    add_temp_error("DUT通信不可用")
    temperature_cycle_test.test_result = "FAILED"
    goto post
end

-- 配置温度箱
log_message("[温度循环] 配置温度箱...", "INFO")
dll_send_command(main.device_channels.thermal_chamber, "RESET")
dll_send_command(main.device_channels.thermal_chamber, string.format("TEMPERATURE:HIGH:%.1f", test_params.temperature_high))
dll_send_command(main.device_channels.thermal_chamber, string.format("TEMPERATURE:LOW:%.1f", test_params.temperature_low))
dll_send_command(main.device_channels.thermal_chamber, string.format("RAMP:RATE:%.1f", test_params.ramp_rate))
dll_send_command(main.device_channels.thermal_chamber, "HUMIDITY:50.0")  -- 设置湿度为50%
dll_send_command(main.device_channels.thermal_chamber, "CIRCULATION:ON")

-- 配置电源（如果需要在测试期间给DUT供电）
if test_params.dut_power_on then
    log_message("[温度循环] 配置DUT电源...", "INFO")
    dll_send_command(main.device_channels.power_supply, "VOLTAGE:12.0")
dll_send_command(main.device_channels.power_supply, "CURRENT:5.0")
dll_send_command(main.device_channels.power_supply, "OUTPUT:ON")
    log_message("[温度循环] DUT电源已开启", "INFO")
else
    dll_send_command(main.device_channels.power_supply, "OUTPUT:OFF")
    log_message("[温度循环] DUT电源保持关闭", "INFO")
end

-- 设置初始温度为环境温度
log_message("[温度循环] 设置初始温度...", "INFO")
dll_send_command(main.device_channels.thermal_chamber, string.format("TEMPERATURE:%.1f", test_params.temperature_ambient))
dll_send_command(main.device_channels.thermal_chamber, "START")

-- 等待温度稳定到环境温度
log_message("[温度循环] 等待温度稳定到环境温度...", "INFO")
if not wait_for_temperature_stable(test_params.temperature_ambient, test_params.temperature_tolerance, 20.0) then
    add_temp_error("无法稳定到环境温度")
    temperature_cycle_test.test_result = "FAILED"
    goto post
end

-- 初始功能检查
if test_params.dut_power_on then
    log_message("[温度循环] 执行初始DUT功能检查...", "INFO")
    local initial_status = check_dut_functionality()
    if not initial_status then
        add_temp_error("DUT初始功能检查失败")
        temperature_cycle_test.test_result = "FAILED"
        goto post
    end
    log_message("[温度循环] DUT初始功能检查通过", "INFO")
end

log_message("[温度循环] === 温度循环测试预处理阶段完成 ===", "INFO")

-- ============================================================================
-- SEQ 标签 - 测试序列阶段
-- ============================================================================
::seq::
log_message("[温度循环] === 开始温度循环测试序列阶段 ===", "INFO")
log_message(string.format("[温度循环] 计划执行 %d 个温度循环", test_params.cycle_count), "INFO")

-- 设置工步执行标志
run_flag[1] = 1  -- 第一个温度循环
run_flag[2] = 1  -- 第二个温度循环
run_flag[3] = 1  -- 第三个温度循环
run_flag[4] = 1  -- 恢复环境温度和最终检查

step = 1
while true do
    -- 需要执行中通过公式二次计算的量在这里，通过公式计算再次生效
    
    -- 工步1：第一个温度循环
    if step == 1 then
        if run_flag[step] ~= 0 then -- 当前编号动作执行
            local cycle = 1
            temperature_cycle_test.cycle_count = cycle
            log_message(string.format("[温度循环] 开始第 %d/%d 个温度循环", cycle, test_params.cycle_count), "INFO")
            
            -- 升温到高温点
            temperature_cycle_test.step_count = temperature_cycle_test.step_count + 1
            log_message(string.format("[温度循环] 循环%d: 升温到高温点 %.1f°C (步骤 %d)", 
                            cycle, test_params.temperature_high, temperature_cycle_test.step_count), "INFO")
            
            dll_send_command(main.device_channels.thermal_chamber, string.format("TEMPERATURE:%.1f", test_params.temperature_high))
            
            -- 等待达到高温并稳定
            local max_ramp_time = math.abs(test_params.temperature_high - test_params.temperature_ambient) / test_params.ramp_rate + 10.0
            local high_temp_stable = wait_for_temperature_stable(test_params.temperature_high, test_params.temperature_tolerance, max_ramp_time)
            
            if high_temp_stable then
                -- 高温保持阶段
                log_message(string.format("[温度循环] 循环%d: 高温保持 %.1f 分钟", cycle, test_params.hold_time_high), "INFO")
                local high_temp_start = os.clock()
                local high_temp_measurements = 0
                
                while (os.clock() - high_temp_start) < (test_params.hold_time_high * 60) do
                    local temperatures = measure_all_temperatures()
                    local dut_status = nil
                    
                    if test_params.dut_power_on then
                        dut_status = check_dut_functionality()
                        if not dut_status then
                            add_temp_error(string.format("循环%d: DUT在高温阶段功能异常", cycle))
                        end
                    end
                    
                    record_temp_data(cycle, "HIGH_TEMP", test_params.temperature_high, temperatures, dut_status)
                    
                    -- 检查温度是否超出容差
                    for point, temp in pairs(temperatures) do
                        if math.abs(temp - test_params.temperature_high) > test_params.max_temperature_deviation then
                            add_temp_error(string.format("循环%d: %s温度偏差过大: %.1f°C (目标%.1f°C)", 
                                          cycle, point, temp, test_params.temperature_high))
                        end
                    end
                    
                    -- 更新最高温度记录
                    for _, temp in pairs(temperatures) do
                        if temp > temperature_cycle_test.max_temperature_reached then
                            temperature_cycle_test.max_temperature_reached = temp
                        end
                    end
                    
                    high_temp_measurements = high_temp_measurements + 1
                    delayms(test_params.measurement_interval * 1000)
                end
                
                -- 降温到低温点
                temperature_cycle_test.step_count = temperature_cycle_test.step_count + 1
                log_message(string.format("[温度循环] 循环%d: 降温到低温点 %.1f°C (步骤 %d)", 
                                cycle, test_params.temperature_low, temperature_cycle_test.step_count), "INFO")
                
                dll_send_command(main.device_channels.thermal_chamber, string.format("TEMPERATURE:%.1f", test_params.temperature_low))
                
                -- 等待达到低温并稳定
                local max_cool_time = math.abs(test_params.temperature_high - test_params.temperature_low) / test_params.ramp_rate + 15.0
                local low_temp_stable = wait_for_temperature_stable(test_params.temperature_low, test_params.temperature_tolerance, max_cool_time)
                
                if low_temp_stable then
                    -- 低温保持阶段
                    log_message(string.format("[温度循环] 循环%d: 低温保持 %.1f 分钟", cycle, test_params.hold_time_low), "INFO")
                    local low_temp_start = os.clock()
                    local low_temp_measurements = 0
                    
                    while (os.clock() - low_temp_start) < (test_params.hold_time_low * 60) do
                        local temperatures = measure_all_temperatures()
                        local dut_status = nil
                        
                        if test_params.dut_power_on then
                            dut_status = check_dut_functionality()
                            if not dut_status then
                                add_temp_error(string.format("循环%d: DUT在低温阶段功能异常", cycle))
                            end
                        end
                        
                        record_temp_data(cycle, "LOW_TEMP", test_params.temperature_low, temperatures, dut_status)
                        
                        -- 检查温度是否超出容差
                        for point, temp in pairs(temperatures) do
                            if math.abs(temp - test_params.temperature_low) > test_params.max_temperature_deviation then
                                add_temp_error(string.format("循环%d: %s温度偏差过大: %.1f°C (目标%.1f°C)", 
                                              cycle, point, temp, test_params.temperature_low))
                            end
                        end
                        
                        -- 更新最低温度记录
                        for _, temp in pairs(temperatures) do
                            if temp < temperature_cycle_test.min_temperature_reached or temperature_cycle_test.min_temperature_reached == 0.0 then
                                temperature_cycle_test.min_temperature_reached = temp
                            end
                        end
                        
                        low_temp_measurements = low_temp_measurements + 1
                        delayms(test_params.measurement_interval * 1000)
                    end
                    
                    log_message(string.format("[温度循环] 循环%d: 第一个温度循环完成", cycle), "INFO")
                    test_result = "PASSED"
                    step = step + 1
                else
                    add_temp_error(string.format("循环%d: 无法达到低温点", cycle))
                    test_result = "FAILED"
                end
            else
                add_temp_error(string.format("循环%d: 无法达到高温点", cycle))
                test_result = "FAILED"
            end
        else
            step = step + 1  -- 当前工步不执行直接到下一步
        end
    end
    
    -- 工步2：第二个温度循环
    if step == 2 then
        if run_flag[step] ~= 0 then -- 当前编号动作执行
            local cycle = 2
            temperature_cycle_test.cycle_count = cycle
            log_message(string.format("[温度循环] 开始第 %d/%d 个温度循环", cycle, test_params.cycle_count), "INFO")
            
            -- 升温到高温点
            temperature_cycle_test.step_count = temperature_cycle_test.step_count + 1
            log_message(string.format("[温度循环] 循环%d: 升温到高温点 %.1f°C (步骤 %d)", 
                            cycle, test_params.temperature_high, temperature_cycle_test.step_count), "INFO")
            
            dll_send_command(main.device_channels.thermal_chamber, string.format("TEMPERATURE:%.1f", test_params.temperature_high))
            
            -- 等待达到高温并稳定
            local max_ramp_time = math.abs(test_params.temperature_high - test_params.temperature_low) / test_params.ramp_rate + 10.0
            local high_temp_stable = wait_for_temperature_stable(test_params.temperature_high, test_params.temperature_tolerance, max_ramp_time)
            
            if high_temp_stable then
                -- 高温保持阶段（简化处理）
                log_message(string.format("[温度循环] 循环%d: 高温保持 %.1f 分钟", cycle, test_params.hold_time_high), "INFO")
                delayms(test_params.hold_time_high * 60 * 1000)
                
                -- 降温到低温点
                dll_send_command(main.device_channels.thermal_chamber, string.format("TEMPERATURE:%.1f", test_params.temperature_low))
                local low_temp_stable = wait_for_temperature_stable(test_params.temperature_low, test_params.temperature_tolerance, max_ramp_time)
                
                if low_temp_stable then
                    -- 低温保持阶段（简化处理）
                    log_message(string.format("[温度循环] 循环%d: 低温保持 %.1f 分钟", cycle, test_params.hold_time_low), "INFO")
                    delayms(test_params.hold_time_low * 60 * 1000)
                    
                    log_message(string.format("[温度循环] 循环%d: 第二个温度循环完成", cycle), "INFO")
                    test_result = "PASSED"
                    step = step + 1
                else
                    add_temp_error(string.format("循环%d: 无法达到低温点", cycle))
                    test_result = "FAILED"
                end
            else
                add_temp_error(string.format("循环%d: 无法达到高温点", cycle))
                test_result = "FAILED"
            end
        else
            step = step + 1  -- 当前工步不执行直接到下一步
        end
    end
    
    -- 工步3：第三个温度循环
    if step == 3 then
        if run_flag[step] ~= 0 then -- 当前编号动作执行
            local cycle = 3
            temperature_cycle_test.cycle_count = cycle
            log_message(string.format("[温度循环] 开始第 %d/%d 个温度循环", cycle, test_params.cycle_count), "INFO")
            
            -- 升温到高温点
            temperature_cycle_test.step_count = temperature_cycle_test.step_count + 1
            log_message(string.format("[温度循环] 循环%d: 升温到高温点 %.1f°C (步骤 %d)", 
                            cycle, test_params.temperature_high, temperature_cycle_test.step_count), "INFO")
            
            dll_send_command(main.device_channels.thermal_chamber, string.format("TEMPERATURE:%.1f", test_params.temperature_high))
            
            -- 等待达到高温并稳定
            local max_ramp_time = math.abs(test_params.temperature_high - test_params.temperature_low) / test_params.ramp_rate + 10.0
            local high_temp_stable = wait_for_temperature_stable(test_params.temperature_high, test_params.temperature_tolerance, max_ramp_time)
            
            if high_temp_stable then
                -- 高温保持阶段（简化处理）
                log_message(string.format("[温度循环] 循环%d: 高温保持 %.1f 分钟", cycle, test_params.hold_time_high), "INFO")
                delayms(test_params.hold_time_high * 60 * 1000)
                
                -- 降温到低温点
                dll_send_command(main.device_channels.thermal_chamber, string.format("TEMPERATURE:%.1f", test_params.temperature_low))
                local low_temp_stable = wait_for_temperature_stable(test_params.temperature_low, test_params.temperature_tolerance, max_ramp_time)
                
                if low_temp_stable then
                    -- 低温保持阶段（简化处理）
                    log_message(string.format("[温度循环] 循环%d: 低温保持 %.1f 分钟", cycle, test_params.hold_time_low), "INFO")
                    delayms(test_params.hold_time_low * 60 * 1000)
                    
                    log_message(string.format("[温度循环] 循环%d: 第三个温度循环完成", cycle), "INFO")
                    test_result = "PASSED"
                    step = step + 1
                else
                    add_temp_error(string.format("循环%d: 无法达到低温点", cycle))
                    test_result = "FAILED"
                end
            else
                add_temp_error(string.format("循环%d: 无法达到高温点", cycle))
                test_result = "FAILED"
            end
        else
            step = step + 1  -- 当前工步不执行直接到下一步
        end
    end
    
    -- 工步4：恢复环境温度和最终检查
    if step == 4 then
        if run_flag[step] ~= 0 then -- 当前编号动作执行
            -- 恢复到环境温度
            log_message("[温度循环] 恢复到环境温度...", "INFO")
            dll_send_command(main.device_channels.thermal_chamber, string.format("TEMPERATURE:%.1f", test_params.temperature_ambient))
            local ambient_stable = wait_for_temperature_stable(test_params.temperature_ambient, test_params.temperature_tolerance, 30.0)
            
            if ambient_stable then
                -- 最终功能检查
                if test_params.dut_power_on then
                    log_message("[温度循环] 执行最终DUT功能检查...", "INFO")
                    local final_status = check_dut_functionality()
                     if not final_status then
                         add_temp_error("DUT最终功能检查失败")
                         test_result = "FAILED"
                    else
                        log_message("[温度循环] DUT最终功能检查通过", "INFO")
                        if test_result == "PASSED" then
                            test_result = "PASSED"
                        end
                    end
                end
                
                -- 判断测试结果
                local seq_test_passed = (#temperature_cycle_test.error_messages == 0) and 
                                       (temperature_cycle_test.cycle_count == test_params.cycle_count)
                
                if seq_test_passed and test_result == "PASSED" then
                    log_message("[温度循环] 温度循环测试序列阶段通过", "INFO")
                    test_result = "PASSED"
                    step = step + 1
                else
                    log_message("[温度循环] 温度循环测试序列阶段失败", "ERROR")
                    test_result = "FAILED"
                end
            else
                add_temp_error("无法恢复到环境温度")
                test_result = "FAILED"
            end
        else
            step = step + 1  -- 当前工步不执行直接到下一步
        end
    end
    
    -- 每一次循环都执行一次检查
    seq_protect_check()
    
    -- 检测到故障
    if next(error_status) ~= nil then
        -- 通过故障保护动作的返回做3种情况，只记录+不动作+继续执行，记录+动作+继续执行，记录+动作+停止执行
        if seq_protect_act() == true then
            break
        end
    end
    
    -- 工步执行完成
    if step >= 5 then
        -- 当前测试序列的状态输出，报告数据输出到报告统一的json
        break
    end
end

log_message("[温度循环] === 温度循环测试序列阶段完成 ===", "INFO")

-- ============================================================================
-- POST 标签 - 后处理阶段
-- ============================================================================
::post::
log_message("[温度循环] === 开始温度循环测试后处理阶段 ===", "INFO")
temperature_cycle_test.end_time = os.time()

-- 关闭温度箱
dll_send_command(main.device_channels.thermal_chamber, "STOP")
dll_send_command(main.device_channels.thermal_chamber, string.format("TEMPERATURE:%.1f", test_params.temperature_ambient))
log_message("[温度循环] 温度箱已停止并设置为环境温度", "INFO")

-- 关闭电源
if test_params.dut_power_on then
    dll_send_command(main.device_channels.power_supply, "OUTPUT:OFF")
    log_message("[温度循环] DUT电源已关闭", "INFO")
end

-- 生成测试报告
local test_duration = temperature_cycle_test.end_time - temperature_cycle_test.start_time
local total_measurements = #temperature_cycle_test.test_data

-- 计算完成的工步数
local completed_steps = step - 1
local total_steps = 4

-- 确定最终测试结果（使用step-based框架的test_result）
if test_result == "PASSED" and #temperature_cycle_test.error_messages == 0 and 
   temperature_cycle_test.cycle_count == test_params.cycle_count then
    temperature_cycle_test.test_result = "PASS"
else
    temperature_cycle_test.test_result = "FAILED"
end

-- 输出测试结果
print("\n" .. string.rep("-", 60))
print("温度循环测试结果报告")
print(string.rep("-", 60))
print(string.format("测试名称: %s", temperature_cycle_test.test_name))
print(string.format("测试结果: %s", temperature_cycle_test.test_result))
print(string.format("测试时长: %d 秒 (%.1f 小时)", test_duration, test_duration / 3600.0))
print(string.format("完成工步: %d/%d (%.1f%%)", completed_steps, total_steps, (completed_steps / total_steps) * 100))
print(string.format("测试步骤: %d", temperature_cycle_test.step_count))
print(string.format("完成循环: %d/%d", temperature_cycle_test.cycle_count, test_params.cycle_count))
print(string.format("测量点数: %d", total_measurements))
print(string.format("最高温度: %.1f°C", temperature_cycle_test.max_temperature_reached))
print(string.format("最低温度: %.1f°C", temperature_cycle_test.min_temperature_reached))
print(string.format("温度范围: %.1f°C ~ %.1f°C", test_params.temperature_low, test_params.temperature_high))
print(string.format("错误数量: %d", #temperature_cycle_test.error_messages))
if next(error_status) ~= nil then
    local error_list = {}
    for error_key, _ in pairs(error_status) do
        table.insert(error_list, error_key)
    end
    print(string.format("错误状态: %s", table.concat(error_list, ", ")))
end

if #temperature_cycle_test.error_messages > 0 then
    print("错误信息:")
    for i, error_msg in ipairs(temperature_cycle_test.error_messages) do
        print(string.format("  %d. %s", i, error_msg))
    end
end

-- 输出循环统计
if total_measurements > 0 then
    print("\n循环统计:")
    local cycle_stats = {}
    for _, data in ipairs(temperature_cycle_test.test_data) do
        local cycle = data.cycle
        if not cycle_stats[cycle] then
            cycle_stats[cycle] = {high_count = 0, low_count = 0}
        end
        if data.phase == "HIGH_TEMP" then
            cycle_stats[cycle].high_count = cycle_stats[cycle].high_count + 1
        elseif data.phase == "LOW_TEMP" then
            cycle_stats[cycle].low_count = cycle_stats[cycle].low_count + 1
        end
    end
    
    print("循环  高温测量  低温测量")
    print(string.rep("-", 30))
    for cycle = 1, temperature_cycle_test.cycle_count do
        if cycle_stats[cycle] then
            print(string.format("%3d   %8d  %8d", cycle, 
                  cycle_stats[cycle].high_count, cycle_stats[cycle].low_count))
        end
    end
end
print(string.rep("-", 60))

-- 写入JSON报告
write_temperature_cycle_json_report()

log_message("[温度循环] === 温度循环测试后处理阶段完成 ===", "INFO")
log_message(string.format("[温度循环] 温度循环测试最终结果: %s", temperature_cycle_test.test_result), "INFO")

-- 返回测试结果
return temperature_cycle_test.test_result