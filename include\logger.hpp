#ifndef LOGGER_HPP
#define LOGGER_HPP

// 防止spdlog宏定义与枚举值冲突
#define SPDLOG_NO_EXCEPTIONS
#include <spdlog/spdlog.h>
#undef ERROR
#undef FATAL
#include <memory>
#include <string>
#include <map>
#include <mutex>

namespace Logging {

// 日志级别映射到spdlog级别
enum class LogLevel {
    TRACE = 0,
    DEBUG = 1,
    INFO = 2,
    WARN = 3,
    ERROR = 4,
    FATAL = 5
};

// 日志输出目标
enum class LogTarget {
    CONSOLE,
    FILE,
    ROTATING_FILE,
    DAILY_FILE
};

// 简化的日志配置
struct LoggerConfig {
    LogLevel min_level = LogLevel::INFO;
    std::string logger_name = "default";
    bool enable_console_output = true;
    bool enable_file_output = false;
    
    // 文件输出配置
    std::string log_directory = "./logs";
    std::string log_file_name = "app.log";
    size_t max_file_size = 10 * 1024 * 1024; // 10MB
    size_t max_file_count = 10;
    bool enable_file_rotation = true;
    
    // 性能配置
    bool enable_async_logging = false;
    size_t async_queue_size = 8192;
};

// 日志统计信息
struct LoggingStatistics {
    size_t total_messages = 0;
    size_t messages_per_level[6] = {0}; // TRACE, DEBUG, INFO, WARN, ERROR, FATAL
    std::chrono::system_clock::time_point start_time;
    std::map<std::string, size_t> logger_message_counts;
};

} // namespace Logging

// 基于spdlog的Logger包装类
class Logger {
public:
    Logger();
    explicit Logger(const std::string& name);
    ~Logger();

    // 禁用拷贝构造和赋值
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;

    // 生命周期管理
    bool initialize(const Logging::LoggerConfig& config);
    void shutdown();

    // 基本日志记录方法
    void trace(const std::string& message);
    void debug(const std::string& message);
    void info(const std::string& message);
    void warn(const std::string& message);
    void error(const std::string& message);
    void fatal(const std::string& message);

    // 带源码位置的日志记录方法
    void trace(const std::string& message, const std::string& file, int line, const std::string& function);
    void debug(const std::string& message, const std::string& file, int line, const std::string& function);
    void info(const std::string& message, const std::string& file, int line, const std::string& function);
    void warn(const std::string& message, const std::string& file, int line, const std::string& function);
    void error(const std::string& message, const std::string& file, int line, const std::string& function);
    void fatal(const std::string& message, const std::string& file, int line, const std::string& function);

    // 格式化日志记录方法
    template<typename... Args>
    void trace_f(const std::string& format, Args&&... args);
    template<typename... Args>
    void debug_f(const std::string& format, Args&&... args);
    template<typename... Args>
    void info_f(const std::string& format, Args&&... args);
    template<typename... Args>
    void warn_f(const std::string& format, Args&&... args);
    template<typename... Args>
    void error_f(const std::string& format, Args&&... args);
    template<typename... Args>
    void fatal_f(const std::string& format, Args&&... args);

    // 通用日志记录方法
    void log(Logging::LogLevel level, const std::string& message);
    void log(Logging::LogLevel level, const std::string& message, const std::string& file, int line, const std::string& function);

    // 级别控制
    void set_level(Logging::LogLevel level);
    Logging::LogLevel get_level() const;
    bool is_enabled(Logging::LogLevel level) const;

    // 文件操作
    bool flush();

    // 调试支持
    void dump_internal_state() const;

    // 静态方法
    static std::shared_ptr<Logger> get_logger(const std::string& name);
    static std::shared_ptr<Logger> get_root_logger();
    static void shutdown_all_loggers();

private:
    // 成员变量
    bool m_initialized = false;
    std::string m_name;
    Logging::LogLevel m_min_level = Logging::LogLevel::INFO;
    Logging::LoggerConfig m_config;
    
    // spdlog logger实例
    std::shared_ptr<spdlog::logger> m_spdlog_logger;
    
    // 上下文和统计
    mutable std::mutex m_context_mutex;
    mutable std::mutex m_stats_mutex;
    Logging::LoggingStatistics m_statistics;
    std::chrono::system_clock::time_point m_last_stats_update;
    
    // 性能监控
    std::map<std::string, std::chrono::high_resolution_clock::time_point> m_performance_timers;
    std::map<std::string, double> m_performance_metrics;
    
    // 静态成员
    static std::map<std::string, std::shared_ptr<Logger>> s_loggers;
    static std::mutex s_loggers_mutex;
    static Logging::LoggerConfig s_global_config;
    static Logging::LogLevel s_global_level;
    
    // 私有方法
    void update_statistics(Logging::LogLevel level);
    void increment_level_counter(Logging::LogLevel level);
    bool ensure_log_directory() const;
    std::string generate_log_file_path() const;
    bool validate_config(const Logging::LoggerConfig& config) const;
    void setup_spdlog_logger();
};

// 便利宏定义
#define LOG_TRACE(logger, message) \
    if ((logger)->is_enabled(Logging::LogLevel::TRACE)) \
        (logger)->trace((message), __FILE__, __LINE__, __FUNCTION__)

#define LOG_DEBUG(logger, message) \
    if ((logger)->is_enabled(Logging::LogLevel::DEBUG)) \
        (logger)->debug((message), __FILE__, __LINE__, __FUNCTION__)

#define LOG_INFO(logger, message) \
    if ((logger)->is_enabled(Logging::LogLevel::INFO)) \
        (logger)->info((message), __FILE__, __LINE__, __FUNCTION__)

#define LOG_WARN(logger, message) \
    if ((logger)->is_enabled(Logging::LogLevel::WARN)) \
        (logger)->warn((message), __FILE__, __LINE__, __FUNCTION__)

#define LOG_ERROR(logger, message) \
    if ((logger)->is_enabled(Logging::LogLevel::ERROR)) \
        (logger)->error((message), __FILE__, __LINE__, __FUNCTION__)

#define LOG_FATAL(logger, message) \
    if ((logger)->is_enabled(Logging::LogLevel::FATAL)) \
        (logger)->fatal((message), __FILE__, __LINE__, __FUNCTION__)

// 格式化宏定义
#define LOG_TRACE_F(logger, format, ...) \
    if ((logger)->is_enabled(Logging::LogLevel::TRACE)) \
        (logger)->trace_f((format), __VA_ARGS__)

#define LOG_DEBUG_F(logger, format, ...) \
    if ((logger)->is_enabled(Logging::LogLevel::DEBUG)) \
        (logger)->debug_f((format), __VA_ARGS__)

#define LOG_INFO_F(logger, format, ...) \
    if ((logger)->is_enabled(Logging::LogLevel::INFO)) \
        (logger)->info_f((format), __VA_ARGS__)

#define LOG_WARN_F(logger, format, ...) \
    if ((logger)->is_enabled(Logging::LogLevel::WARN)) \
        (logger)->warn_f((format), __VA_ARGS__)

#define LOG_ERROR_F(logger, format, ...) \
    if ((logger)->is_enabled(Logging::LogLevel::ERROR)) \
        (logger)->error_f((format), __VA_ARGS__)

#define LOG_FATAL_F(logger, format, ...) \
    if ((logger)->is_enabled(Logging::LogLevel::FATAL)) \
        (logger)->fatal_f((format), __VA_ARGS__)

// 模板方法实现
template<typename... Args>
void Logger::trace_f(const std::string& format, Args&&... args) {
    if (is_enabled(Logging::LogLevel::TRACE)) {
        m_spdlog_logger->trace(format, std::forward<Args>(args)...);
        update_statistics(Logging::LogLevel::TRACE);
    }
}

template<typename... Args>
void Logger::debug_f(const std::string& format, Args&&... args) {
    if (is_enabled(Logging::LogLevel::DEBUG)) {
        m_spdlog_logger->debug(format, std::forward<Args>(args)...);
        update_statistics(Logging::LogLevel::DEBUG);
    }
}

template<typename... Args>
void Logger::info_f(const std::string& format, Args&&... args) {
    if (is_enabled(Logging::LogLevel::INFO)) {
        m_spdlog_logger->info(format, std::forward<Args>(args)...);
        update_statistics(Logging::LogLevel::INFO);
    }
}

template<typename... Args>
void Logger::warn_f(const std::string& format, Args&&... args) {
    if (is_enabled(Logging::LogLevel::WARN)) {
        m_spdlog_logger->warn(format, std::forward<Args>(args)...);
        update_statistics(Logging::LogLevel::WARN);
    }
}

template<typename... Args>
void Logger::error_f(const std::string& format, Args&&... args) {
    if (is_enabled(Logging::LogLevel::ERROR)) {
        m_spdlog_logger->error(format, std::forward<Args>(args)...);
        update_statistics(Logging::LogLevel::ERROR);
    }
}

template<typename... Args>
void Logger::fatal_f(const std::string& format, Args&&... args) {
    if (is_enabled(Logging::LogLevel::FATAL)) {
        m_spdlog_logger->critical(format, std::forward<Args>(args)...);
        update_statistics(Logging::LogLevel::FATAL);
    }
}

#endif // LOGGER_HPP