#ifndef GLOBAL_VARIABLE_SERVICE_HPP
#define GLOBAL_VARIABLE_SERVICE_HPP

#include <unordered_map>
#include <string>
#include <mutex>
#include <memory>
#include <vector>
#include <functional>
#include "common_types.hpp"
#include "variable_types.hpp"
#include "global_variable_task.hpp"

/**
 * @brief 全局变量服务类 - 单例模式，线程安全的变量管理
 */
class EXP_API GlobalVariableService {
public:
    using VariableChangeCallback = std::function<void(const std::string&, const VariableValue&)>;
    
    // 获取服务实例
    static GlobalVariableService& get_instance();
    
    // 禁止拷贝和赋值
    GlobalVariableService(const GlobalVariableService&) = delete;
    GlobalVariableService& operator=(const GlobalVariableService&) = delete;
    
    // 初始化服务
    ATE_EC initialize(const std::string& instance_id, const RedisConfig& redis_config = RedisConfig{});
    
    // 关闭服务
    void shutdown();
    
    // 检查服务是否已初始化
    bool is_initialized() const;
    
    // ========== 基本变量操作 ==========
    
    // 设置变量值
    ATE_EC set_variable(const std::string& key, const VariableValue& value);
    
    // 获取变量值
    ATE_EC get_variable(const std::string& key, VariableValue& value) const;
    
    // 检查变量是否存在
    bool has_variable(const std::string& key) const;
    
    // 删除变量
    ATE_EC remove_variable(const std::string& key);
    
    // ========== 类型特化接口 ==========
    
    // 设置双精度浮点数变量
    ATE_EC set_double(const std::string& key, double value, const std::string& unit = "");
    
    // 设置字符串变量
    ATE_EC set_string(const std::string& key, const std::string& value, const std::string& unit = "");
    
    // 设置布尔变量
    ATE_EC set_bool(const std::string& key, bool value, const std::string& unit = "");
    
    // 设置整数变量
    ATE_EC set_int(const std::string& key, int value, const std::string& unit = "");
    
    // 获取双精度浮点数变量
    ATE_EC get_double(const std::string& key, double& value) const;
    
    // 获取字符串变量
    ATE_EC get_string(const std::string& key, std::string& value) const;
    
    // 获取布尔变量
    ATE_EC get_bool(const std::string& key, bool& value) const;
    
    // 获取整数变量
    ATE_EC get_int(const std::string& key, int& value) const;
    
    // ========== 批量操作 ==========
    
    // 获取所有变量键名列表
    std::vector<std::string> get_all_keys() const;
    
    // 获取变量数量
    size_t get_variable_count() const;
    
    // 清空所有变量
    ATE_EC clear_all_variables();
    
    // ========== 监听器模式 ==========
    
    // 注册变量变化监听器
    ATE_EC register_change_listener(const std::string& listener_id, VariableChangeCallback callback);
    
    // 注销变量变化监听器
    ATE_EC unregister_change_listener(const std::string& listener_id);
    
    // 获取已注册的监听器数量
    size_t get_listener_count() const;
    
    // ========== Redis同步 ==========
    
    // 从Redis同步变量到本地缓存
    ATE_EC sync_from_redis();
    
    // 将本地缓存同步到Redis
    ATE_EC sync_to_redis();
    
    // 检查Redis连接状态
    bool is_redis_connected() const;

private:
    GlobalVariableService() = default;
    ~GlobalVariableService() = default;
    
    // 通知所有监听器变量已变化
    void notify_variable_changed(const std::string& key, const VariableValue& value);
    
    std::unique_ptr<GlobalVariableTask> m_global_variable_system;
    std::unordered_map<std::string, VariableChangeCallback> m_change_listeners;
    mutable std::mutex m_listeners_mutex;
    std::atomic<bool> m_initialized{false};
    mutable std::mutex m_service_mutex;
};

#endif // GLOBAL_VARIABLE_SERVICE_HPP