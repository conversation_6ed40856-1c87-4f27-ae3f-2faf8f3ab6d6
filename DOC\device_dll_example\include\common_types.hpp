#ifndef COMMON_TYPES_HPP
#define COMMON_TYPES_HPP

#include <cstdint>
#include <chrono>
#include <array>

namespace EthClient {

// 基本类型定义
using DeviceId = uint32_t;
using ChannelId = uint32_t;
using ThreadId = uint32_t;

// MODBUS常量
constexpr size_t MAX_TCP_LENGTH = 4096;
constexpr size_t MODBUS_RESPONSE_BUFFER_SIZE = 1024;  // Modbus响应缓冲区大小
constexpr uint32_t DEFAULT_TIMEOUT_MS = 5000;
constexpr uint32_t DEFAULT_THREAD_POOL_SIZE = 20;

// 设备连接状态
enum class ConnectionStatus {
    DISCONNECTED = 0,
    CONNECTING = 1,
    CONNECTED = 2,
    ERROR = 3
};

// Device operation mode
enum class OperationMode {
    CONSTANT_CURRENT = 0,
    CONSTANT_VOLTAGE = 1,
    CONSTANT_POWER = 2,
    CONSTANT_RESISTANCE = 3,
    BATTERY_TEST = 4,
    SEQUENCE = 5,
    UNKNOWN = 99
};

// Thread execution state
enum class ThreadState {
    STOPPED = 0,
    RUNNING = 1,
    PAUSED = 2,
    ERROR = 3
};

// Time utilities
using TimePoint = std::chrono::steady_clock::time_point;
using Duration = std::chrono::milliseconds;

inline TimePoint get_current_time() {
    return std::chrono::steady_clock::now();
}

inline uint64_t get_timestamp_ms() {
    return std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()
    ).count();
}

} // namespace EthClient

#endif // COMMON_TYPES_HPP