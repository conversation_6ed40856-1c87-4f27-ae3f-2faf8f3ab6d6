#include "report_process.hpp"
#include "common_types.hpp"
#include "data_process.hpp"
#include "global_variable_task.hpp"
#include "data_recorder.hpp"
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <iomanip>
#include <filesystem>
#include <random>
#include <condition_variable>
#include "../third_library/nlohmann/json.hpp"

ReportProcess::ReportProcess() {
    m_start_time = std::chrono::steady_clock::now();
    m_default_output_path = "./reports";
}

ReportProcess::~ReportProcess() {
    shutdown();
}

ATE_EC ReportProcess::initialize() {
    if (m_initialized) {
        add_error_message("ReportProcess already initialized");
        return ATE_ERROR_ALREADY_INITIALIZED;
    }

    try {
        // 创建默认输出目录
        if (!std::filesystem::exists(m_default_output_path)) {
            std::filesystem::create_directories(m_default_output_path);
        }
        
        // 启动工作线程
        m_shutdown_requested = false;
        for (size_t i = 0; i < m_max_concurrent_reports; ++i) {
            m_worker_threads.emplace_back(&ReportProcess::worker_thread_function, this);
        }
        
        // 启动调度线程
        m_scheduler_thread = std::thread(&ReportProcess::scheduler_thread_function, this);
        
        m_initialized = true;
        return ATE_SUCCESS;
    }
    catch (const std::exception& e) {
        add_error_message("Failed to initialize ReportProcess: " + std::string(e.what()));
        return ATE_ERROR_INITIALIZATION_FAILED;
    }
}

void ReportProcess::shutdown() {
    if (!m_initialized) {
        return;
    }

    m_shutdown_requested = true;
    m_queue_cv.notify_all();
    
    // 等待工作线程结束
    for (auto& thread : m_worker_threads) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    m_worker_threads.clear();
    
    // 等待调度线程结束
    if (m_scheduler_thread.joinable()) {
        m_scheduler_thread.join();
    }
    
    // 清理资源
    {
        std::lock_guard<std::mutex> lock(m_components_mutex);
        m_templates.clear();
        m_collectors.clear();
        m_chart_generators.clear();
    }
    
    {
        std::lock_guard<std::mutex> lock(m_reports_mutex);
        m_reports.clear();
    }
    
    {
        std::lock_guard<std::mutex> lock(m_scheduler_mutex);
        m_scheduled_reports.clear();
    }
    
    m_initialized = false;
}

void ReportProcess::set_data_process(DataProcess* data_process) {
    m_data_process = data_process;
}

void ReportProcess::set_variable_system(GlobalVariableTask* var_system) {
    m_variable_system = var_system;
}

void ReportProcess::set_data_recorder(DataRecorder* data_recorder) {
    m_data_recorder = data_recorder;
}

std::string ReportProcess::create_report(const ReportProcessing::ReportConfig& config) {
    if (!m_initialized) {
        add_error_message("ReportProcess not initialized");
        return "";
    }

    if (!validate_report_config(config)) {
        add_error_message("Invalid report configuration");
        return "";
    }

    try {
        std::string report_id = config.report_id.empty() ? generate_unique_report_id() : config.report_id;
        
        // 创建报告结果对象
        ReportProcessing::ReportResult result;
        result.report_id = report_id;
        result.config = config;
        result.status = ReportProcessing::ReportStatus::PENDING;
        result.created_time = std::chrono::system_clock::now();
        
        // 存储报告
        {
            std::lock_guard<std::mutex> lock(m_reports_mutex);
            m_reports[report_id] = result;
        }
        
        // 添加到生成队列
        {
            std::lock_guard<std::mutex> lock(m_queue_mutex);
            m_generation_queue.push(report_id);
        }
        m_queue_cv.notify_one();
        
        ++m_total_reports;
        return report_id;
    }
    catch (const std::exception& e) {
        add_error_message("Failed to create report: " + std::string(e.what()));
        return "";
    }
}

bool ReportProcess::generate_report_async(const std::string& report_id) {
    std::lock_guard<std::mutex> lock(m_reports_mutex);
    auto it = m_reports.find(report_id);
    if (it == m_reports.end()) {
        add_error_message("Report not found: " + report_id);
        return false;
    }
    
    if (it->second.status != ReportProcessing::ReportStatus::PENDING) {
        add_error_message("Report is not in pending status: " + report_id);
        return false;
    }
    
    // 添加到生成队列
    {
        std::lock_guard<std::mutex> queue_lock(m_queue_mutex);
        m_generation_queue.push(report_id);
    }
    m_queue_cv.notify_one();
    
    return true;
}

bool ReportProcess::cancel_report(const std::string& report_id) {
    std::lock_guard<std::mutex> lock(m_reports_mutex);
    auto it = m_reports.find(report_id);
    if (it == m_reports.end()) {
        return false;
    }
    
    if (it->second.status == ReportProcessing::ReportStatus::PENDING) {
        it->second.status = ReportProcessing::ReportStatus::CANCELLED;
        return true;
    }
    
    return false;
}

ReportProcessing::ReportStatus ReportProcess::get_report_status(const std::string& report_id) const {
    std::lock_guard<std::mutex> lock(m_reports_mutex);
    auto it = m_reports.find(report_id);
    if (it != m_reports.end()) {
        return it->second.status;
    }
    return ReportProcessing::ReportStatus::FAILED;
}

ReportProcessing::ReportResult ReportProcess::get_report_result(const std::string& report_id) const {
    std::lock_guard<std::mutex> lock(m_reports_mutex);
    auto it = m_reports.find(report_id);
    if (it != m_reports.end()) {
        return it->second;
    }
    return ReportProcessing::ReportResult{};
}

std::vector<std::string> ReportProcess::get_active_reports() const {
    std::lock_guard<std::mutex> lock(m_reports_mutex);
    std::vector<std::string> active_reports;
    
    for (const auto& [report_id, result] : m_reports) {
        if (result.status == ReportProcessing::ReportStatus::PENDING ||
            result.status == ReportProcessing::ReportStatus::GENERATING) {
            active_reports.push_back(report_id);
        }
    }
    
    return active_reports;
}

std::vector<std::string> ReportProcess::get_completed_reports() const {
    std::lock_guard<std::mutex> lock(m_reports_mutex);
    std::vector<std::string> completed_reports;
    
    for (const auto& [report_id, result] : m_reports) {
        if (result.status == ReportProcessing::ReportStatus::COMPLETED) {
            completed_reports.push_back(report_id);
        }
    }
    
    return completed_reports;
}

bool ReportProcess::delete_report(const std::string& report_id) {
    std::lock_guard<std::mutex> lock(m_reports_mutex);
    auto it = m_reports.find(report_id);
    if (it == m_reports.end()) {
        return false;
    }
    
    // 删除文件
    if (!it->second.file_path.empty() && std::filesystem::exists(it->second.file_path)) {
        try {
            std::filesystem::remove(it->second.file_path);
        }
        catch (const std::exception& e) {
            add_error_message("Failed to delete report file: " + std::string(e.what()));
        }
    }
    
    m_reports.erase(it);
    return true;
}

void ReportProcess::clear_completed_reports() {
    std::lock_guard<std::mutex> lock(m_reports_mutex);
    auto it = m_reports.begin();
    while (it != m_reports.end()) {
        if (it->second.status == ReportProcessing::ReportStatus::COMPLETED ||
            it->second.status == ReportProcessing::ReportStatus::FAILED ||
            it->second.status == ReportProcessing::ReportStatus::CANCELLED) {
            
            // 删除文件
            if (!it->second.file_path.empty() && std::filesystem::exists(it->second.file_path)) {
                try {
                    std::filesystem::remove(it->second.file_path);
                }
                catch (const std::exception& e) {
                    add_error_message("Failed to delete report file: " + std::string(e.what()));
                }
            }
            
            it = m_reports.erase(it);
        } else {
            ++it;
        }
    }
}

void ReportProcess::clear_all_reports() {
    std::lock_guard<std::mutex> lock(m_reports_mutex);
    
    // 删除所有文件
    for (const auto& [report_id, result] : m_reports) {
        if (!result.file_path.empty() && std::filesystem::exists(result.file_path)) {
            try {
                std::filesystem::remove(result.file_path);
            }
            catch (const std::exception& e) {
                add_error_message("Failed to delete report file: " + std::string(e.what()));
            }
        }
    }
    
    m_reports.clear();
}

void ReportProcess::register_template(std::unique_ptr<ReportProcessing::IReportTemplate> template_ptr) {
    if (template_ptr) {
        std::lock_guard<std::mutex> lock(m_components_mutex);
        m_templates[template_ptr->get_template_name()] = std::move(template_ptr);
    }
}

void ReportProcess::unregister_template(const std::string& template_name) {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    m_templates.erase(template_name);
}

std::vector<std::string> ReportProcess::get_available_templates() const {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    std::vector<std::string> templates;
    for (const auto& [name, template_ptr] : m_templates) {
        templates.push_back(name);
    }
    return templates;
}

std::vector<ReportProcessing::ReportFormat> ReportProcess::get_supported_formats() const {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    std::vector<ReportProcessing::ReportFormat> formats;
    for (const auto& [name, template_ptr] : m_templates) {
        formats.push_back(template_ptr->get_supported_format());
    }
    
    // 去重
    std::sort(formats.begin(), formats.end());
    formats.erase(std::unique(formats.begin(), formats.end()), formats.end());
    
    return formats;
}

void ReportProcess::register_data_collector(std::unique_ptr<ReportProcessing::IDataCollector> collector) {
    if (collector) {
        std::lock_guard<std::mutex> lock(m_components_mutex);
        m_collectors[collector->get_collector_name()] = std::move(collector);
    }
}

void ReportProcess::unregister_data_collector(const std::string& collector_name) {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    m_collectors.erase(collector_name);
}

std::vector<std::string> ReportProcess::get_available_collectors() const {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    std::vector<std::string> collectors;
    for (const auto& [name, collector_ptr] : m_collectors) {
        collectors.push_back(name);
    }
    return collectors;
}

void ReportProcess::register_chart_generator(std::unique_ptr<ReportProcessing::IChartGenerator> generator) {
    if (generator) {
        std::lock_guard<std::mutex> lock(m_components_mutex);
        m_chart_generators[generator->get_generator_name()] = std::move(generator);
    }
}

void ReportProcess::unregister_chart_generator(const std::string& generator_name) {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    m_chart_generators.erase(generator_name);
}

std::vector<std::string> ReportProcess::get_available_chart_generators() const {
    std::lock_guard<std::mutex> lock(m_components_mutex);
    std::vector<std::string> generators;
    for (const auto& [name, generator_ptr] : m_chart_generators) {
        generators.push_back(name);
    }
    return generators;
}

bool ReportProcess::schedule_report(const ReportProcessing::ReportConfig& config) {
    if (!config.is_scheduled) {
        add_error_message("Report config is not marked as scheduled");
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_scheduler_mutex);
    m_scheduled_reports[config.report_id] = config;
    return true;
}

bool ReportProcess::unschedule_report(const std::string& report_id) {
    std::lock_guard<std::mutex> lock(m_scheduler_mutex);
    return m_scheduled_reports.erase(report_id) > 0;
}

std::vector<std::string> ReportProcess::get_scheduled_reports() const {
    std::lock_guard<std::mutex> lock(m_scheduler_mutex);
    std::vector<std::string> scheduled;
    for (const auto& [report_id, config] : m_scheduled_reports) {
        scheduled.push_back(report_id);
    }
    return scheduled;
}

void ReportProcess::update_schedule() {
    std::lock_guard<std::mutex> lock(m_scheduler_mutex);
    auto now = std::chrono::system_clock::now();
    
    for (auto& [report_id, config] : m_scheduled_reports) {
        if (config.next_run_time <= now) {
            // 创建新的报告实例
            auto new_config = config;
            new_config.report_id = generate_unique_report_id();
            new_config.next_run_time = now + config.schedule_interval;
            
            create_report(new_config);
            
            // 更新下次运行时间
            config.next_run_time = now + config.schedule_interval;
        }
    }
}

std::vector<std::string> ReportProcess::create_batch_reports(const std::vector<ReportProcessing::ReportConfig>& configs) {
    std::vector<std::string> report_ids;
    
    for (const auto& config : configs) {
        std::string report_id = create_report(config);
        if (!report_id.empty()) {
            report_ids.push_back(report_id);
        }
    }
    
    return report_ids;
}

bool ReportProcess::export_reports(const std::vector<std::string>& report_ids, const std::string& export_path) {
    try {
        if (!std::filesystem::exists(export_path)) {
            std::filesystem::create_directories(export_path);
        }
        
        for (const std::string& report_id : report_ids) {
            auto result = get_report_result(report_id);
            if (result.status == ReportProcessing::ReportStatus::COMPLETED && 
                !result.file_path.empty() && std::filesystem::exists(result.file_path)) {
                
                std::string filename = std::filesystem::path(result.file_path).filename().string();
                std::string dest_path = (std::filesystem::path(export_path) / filename).string();
                
                std::filesystem::copy_file(result.file_path, dest_path, 
                                         std::filesystem::copy_options::overwrite_existing);
            }
        }
        
        return true;
    }
    catch (const std::exception& e) {
        add_error_message("Failed to export reports: " + std::string(e.what()));
        return false;
    }
}

size_t ReportProcess::get_total_reports_count() const {
    return m_total_reports;
}

size_t ReportProcess::get_pending_reports_count() const {
    std::lock_guard<std::mutex> lock(m_reports_mutex);
    size_t count = 0;
    for (const auto& [report_id, result] : m_reports) {
        if (result.status == ReportProcessing::ReportStatus::PENDING) {
            ++count;
        }
    }
    return count;
}

size_t ReportProcess::get_completed_reports_count() const {
    return m_completed_reports;
}

size_t ReportProcess::get_failed_reports_count() const {
    return m_failed_reports;
}

double ReportProcess::get_average_generation_time() const {
    std::lock_guard<std::mutex> lock(m_stats_mutex);
    if (m_generation_times.empty()) {
        return 0.0;
    }
    
    auto total = std::chrono::milliseconds::zero();
    for (const auto& time : m_generation_times) {
        total += time;
    }
    
    return static_cast<double>(total.count()) / m_generation_times.size();
}

void ReportProcess::set_default_output_path(const std::string& path) {
    m_default_output_path = path;
    if (!std::filesystem::exists(path)) {
        try {
            std::filesystem::create_directories(path);
        }
        catch (const std::exception& e) {
            add_error_message("Failed to create output directory: " + std::string(e.what()));
        }
    }
}

std::string ReportProcess::get_default_output_path() const {
    return m_default_output_path;
}

void ReportProcess::set_max_concurrent_reports(size_t max_count) {
    m_max_concurrent_reports = max_count;
}

size_t ReportProcess::get_max_concurrent_reports() const {
    return m_max_concurrent_reports;
}

void ReportProcess::set_report_retention_days(int days) {
    m_report_retention_days = days;
}

int ReportProcess::get_report_retention_days() const {
    return m_report_retention_days;
}

void ReportProcess::set_report_completed_callback(ReportCompletedCallback callback) {
    std::lock_guard<std::mutex> lock(m_callback_mutex);
    m_completed_callback = callback;
}

void ReportProcess::set_report_failed_callback(ReportFailedCallback callback) {
    std::lock_guard<std::mutex> lock(m_callback_mutex);
    m_failed_callback = callback;
}

void ReportProcess::set_report_progress_callback(ReportProgressCallback callback) {
    std::lock_guard<std::mutex> lock(m_callback_mutex);
    m_progress_callback = callback;
}

std::vector<std::string> ReportProcess::get_error_messages() const {
    std::lock_guard<std::mutex> lock(m_error_mutex);
    return m_error_messages;
}

void ReportProcess::clear_error_messages() {
    std::lock_guard<std::mutex> lock(m_error_mutex);
    m_error_messages.clear();
}

void ReportProcess::worker_thread_function() {
    while (!m_shutdown_requested) {
        std::string report_id;
        bool has_work = false;
        
        // 获取待处理的报告
        {
            std::unique_lock<std::mutex> lock(m_queue_mutex);
            m_queue_cv.wait(lock, [this] { return !m_generation_queue.empty() || m_shutdown_requested; });
            
            if (m_shutdown_requested) {
                break;
            }
            
            if (!m_generation_queue.empty()) {
                report_id = m_generation_queue.front();
                m_generation_queue.pop();
                has_work = true;
            }
        }
        
        if (has_work) {
            generate_report_internal(report_id);
        }
    }
}

void ReportProcess::scheduler_thread_function() {
    while (!m_shutdown_requested) {
        update_schedule();
        cleanup_old_reports();
        
        // 每分钟检查一次
        std::this_thread::sleep_for(std::chrono::minutes(1));
    }
}

bool ReportProcess::generate_report_internal(const std::string& report_id) {
    auto start_time = std::chrono::steady_clock::now();
    
    try {
        // 更新状态为生成中
        {
            std::lock_guard<std::mutex> lock(m_reports_mutex);
            auto it = m_reports.find(report_id);
            if (it == m_reports.end()) {
                add_error_message("Report not found: " + report_id);
                return false;
            }
            
            if (it->second.status == ReportProcessing::ReportStatus::CANCELLED) {
                return false;
            }
            
            it->second.status = ReportProcessing::ReportStatus::GENERATING;
        }
        
        notify_report_progress(report_id, 10);
        
        // 收集数据
        ReportProcessing::ReportResult result;
        {
            std::lock_guard<std::mutex> lock(m_reports_mutex);
            result = m_reports[report_id];
        }
        
        auto data_items = collect_report_data(result.config);
        notify_report_progress(report_id, 50);
        
        // 组织报告数据
        ReportProcessing::ReportSection main_section;
        main_section.section_id = "main";
        main_section.title = result.config.title;
        main_section.data_items = data_items;
        result.sections.push_back(main_section);
        
        notify_report_progress(report_id, 80);
        
        // 生成报告内容
        std::string content = generate_report_content(result);
        if (content.empty()) {
            throw std::runtime_error("Failed to generate report content");
        }
        
        // 保存到文件
        std::string output_path = result.config.output_path.empty() ? m_default_output_path : result.config.output_path;
        std::string filename = report_id + "_" + result.config.title;
        
        // 根据格式添加扩展名
        switch (result.config.format) {
            case ReportProcessing::ReportFormat::HTML:
                filename += ".html";
                break;
            case ReportProcessing::ReportFormat::JSON:
                filename += ".json";
                break;
            case ReportProcessing::ReportFormat::CSV:
                filename += ".csv";
                break;
            case ReportProcessing::ReportFormat::TEXT:
                filename += ".txt";
                break;
            case ReportProcessing::ReportFormat::MARKDOWN:
                filename += ".md";
                break;
            default:
                filename += ".txt";
                break;
        }
        
        std::string file_path = (std::filesystem::path(output_path) / filename).string();
        
        std::ofstream file(file_path);
        if (!file.is_open()) {
            throw std::runtime_error("Failed to create output file: " + file_path);
        }
        
        file << content;
        file.close();
        
        // 更新结果
        result.status = ReportProcessing::ReportStatus::COMPLETED;
        result.file_path = file_path;
        result.completed_time = std::chrono::system_clock::now();
        result.file_size = std::filesystem::file_size(file_path);
        
        {
            std::lock_guard<std::mutex> lock(m_reports_mutex);
            m_reports[report_id] = result;
        }
        
        notify_report_progress(report_id, 100);
        notify_report_completed(report_id, result);
        
        ++m_completed_reports;
        
        // 记录生成时间
        auto end_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        {
            std::lock_guard<std::mutex> lock(m_stats_mutex);
            m_generation_times.push_back(duration);
            
            // 保持最近100次的记录
            if (m_generation_times.size() > 100) {
                m_generation_times.erase(m_generation_times.begin());
            }
        }
        
        return true;
    }
    catch (const std::exception& e) {
        // 更新状态为失败
        {
            std::lock_guard<std::mutex> lock(m_reports_mutex);
            auto it = m_reports.find(report_id);
            if (it != m_reports.end()) {
                it->second.status = ReportProcessing::ReportStatus::FAILED;
                it->second.error_message = e.what();
            }
        }
        
        notify_report_failed(report_id, e.what());
        ++m_failed_reports;
        
        add_error_message("Failed to generate report '" + report_id + "': " + e.what());
        return false;
    }
}

std::vector<ReportProcessing::ReportDataItem> ReportProcess::collect_report_data(const ReportProcessing::ReportConfig& config) {
    std::vector<ReportProcessing::ReportDataItem> data_items;
    
    try {
        // 从变量系统收集数据
        if (m_variable_system && 
            std::find(config.data_sources.begin(), config.data_sources.end(), 
                     ReportProcessing::DataSourceType::VARIABLE_SYSTEM) != config.data_sources.end()) {
            
            for (const std::string& var_name : config.variable_names) {
                VariableValue value;
                ATE_EC result = m_variable_system->get_variable(var_name, value);
                if (result == ATE_SUCCESS) {
                    ReportProcessing::ReportDataItem item(var_name, value.asString());
                    item.category = "variable";
                    data_items.push_back(item);
                }
            }
        }
        
        // 从数据处理器收集数据
        if (m_data_process && 
            std::find(config.data_sources.begin(), config.data_sources.end(), 
                     ReportProcessing::DataSourceType::DATA_PROCESSOR) != config.data_sources.end()) {
            
            auto processed_data = m_data_process->get_processed_data();
            for (const auto& data_item : processed_data) {
                ReportProcessing::ReportDataItem item(data_item.name, data_item.value);
                item.category = "processed";
                item.timestamp = data_item.timestamp;
                data_items.push_back(item);
            }
        }
        
        // 使用注册的数据收集器
        {
            std::lock_guard<std::mutex> lock(m_components_mutex);
            for (const auto& [name, collector] : m_collectors) {
                if (collector->is_available()) {
                    auto collected_data = collector->collect_data(config);
                    data_items.insert(data_items.end(), collected_data.begin(), collected_data.end());
                }
            }
        }
    }
    catch (const std::exception& e) {
        add_error_message("Error collecting report data: " + std::string(e.what()));
    }
    
    return data_items;
}

std::string ReportProcess::generate_report_content(const ReportProcessing::ReportResult& report_data) {
    try {
        // 查找合适的模板
        std::lock_guard<std::mutex> lock(m_components_mutex);
        
        for (const auto& [name, template_ptr] : m_templates) {
            if (template_ptr->get_supported_format() == report_data.config.format &&
                template_ptr->validate_config(report_data.config)) {
                return template_ptr->generate_report(report_data);
            }
        }
        
        // 如果没有找到合适的模板，使用默认格式
        std::stringstream ss;
        
        switch (report_data.config.format) {
            case ReportProcessing::ReportFormat::JSON: {
                nlohmann::json root;
                root["report_id"] = report_data.report_id;
                root["title"] = report_data.config.title;
                root["description"] = report_data.config.description;
                
                auto time_t = std::chrono::system_clock::to_time_t(report_data.created_time);
                std::stringstream time_ss;
                time_ss << std::put_time(std::gmtime(&time_t), "%Y-%m-%dT%H:%M:%SZ");
                root["created_time"] = time_ss.str();
                
                nlohmann::json sections = nlohmann::json::array();
                for (const auto& section : report_data.sections) {
                    nlohmann::json json_section;
                    json_section["title"] = section.title;
                    
                    nlohmann::json items = nlohmann::json::array();
                    for (const auto& item : section.data_items) {
                        nlohmann::json json_item;
                        json_item["name"] = item.name;
                        json_item["value"] = item.value;
                        json_item["unit"] = item.unit;
                        json_item["category"] = item.category;
                        items.push_back(json_item);
                    }
                    json_section["data_items"] = items;
                    sections.push_back(json_section);
                }
                root["sections"] = sections;
                
                ss << root.dump(2);  // 2 spaces indentation
                break;
            }
            
            case ReportProcessing::ReportFormat::CSV: {
                ss << "Name,Value,Unit,Category,Timestamp\n";
                for (const auto& section : report_data.sections) {
                    for (const auto& item : section.data_items) {
                        auto time_t = std::chrono::system_clock::to_time_t(item.timestamp);
                        std::stringstream time_ss;
                        time_ss << std::put_time(std::gmtime(&time_t), "%Y-%m-%dT%H:%M:%SZ");
                        
                        ss << item.name << "," << item.value << "," << item.unit << "," 
                           << item.category << "," << time_ss.str() << "\n";
                    }
                }
                break;
            }
            
            default: {
                // 默认文本格式
                ss << "Report: " << report_data.config.title << "\n";
                ss << "Description: " << report_data.config.description << "\n";
                auto time_t_value = std::chrono::system_clock::to_time_t(report_data.created_time);
                ss << "Generated: " << std::put_time(std::gmtime(&time_t_value), "%Y-%m-%d %H:%M:%S") << "\n\n";
                
                for (const auto& section : report_data.sections) {
                    ss << "Section: " << section.title << "\n";
                    ss << "----------------------------------------\n";
                    
                    for (const auto& item : section.data_items) {
                        ss << item.name << ": " << item.value;
                        if (!item.unit.empty()) {
                            ss << " " << item.unit;
                        }
                        ss << " (" << item.category << ")\n";
                    }
                    ss << "\n";
                }
                break;
            }
        }
        
        return ss.str();
    }
    catch (const std::exception& e) {
        add_error_message("Error generating report content: " + std::string(e.what()));
        return "";
    }
}

void ReportProcess::cleanup_old_reports() {
    if (m_report_retention_days <= 0) {
        return;
    }
    
    auto cutoff_time = std::chrono::system_clock::now() - std::chrono::hours(24 * m_report_retention_days);
    
    std::lock_guard<std::mutex> lock(m_reports_mutex);
    auto it = m_reports.begin();
    while (it != m_reports.end()) {
        if (it->second.created_time < cutoff_time &&
            (it->second.status == ReportProcessing::ReportStatus::COMPLETED ||
             it->second.status == ReportProcessing::ReportStatus::FAILED ||
             it->second.status == ReportProcessing::ReportStatus::CANCELLED)) {
            
            // 删除文件
            if (!it->second.file_path.empty() && std::filesystem::exists(it->second.file_path)) {
                try {
                    std::filesystem::remove(it->second.file_path);
                }
                catch (const std::exception& e) {
                    add_error_message("Failed to delete old report file: " + std::string(e.what()));
                }
            }
            
            it = m_reports.erase(it);
        } else {
            ++it;
        }
    }
}

void ReportProcess::add_error_message(const std::string& message) {
    std::lock_guard<std::mutex> lock(m_error_mutex);
    m_error_messages.push_back(message);
    
    // 保持最近100条错误消息
    if (m_error_messages.size() > 100) {
        m_error_messages.erase(m_error_messages.begin());
    }
}

void ReportProcess::notify_report_completed(const std::string& report_id, const ReportProcessing::ReportResult& result) {
    std::lock_guard<std::mutex> lock(m_callback_mutex);
    if (m_completed_callback) {
        try {
            m_completed_callback(report_id, result);
        }
        catch (const std::exception& e) {
            add_error_message("Error in completed callback: " + std::string(e.what()));
        }
    }
}

void ReportProcess::notify_report_failed(const std::string& report_id, const std::string& error) {
    std::lock_guard<std::mutex> lock(m_callback_mutex);
    if (m_failed_callback) {
        try {
            m_failed_callback(report_id, error);
        }
        catch (const std::exception& e) {
            add_error_message("Error in failed callback: " + std::string(e.what()));
        }
    }
}

void ReportProcess::notify_report_progress(const std::string& report_id, int progress) {
    std::lock_guard<std::mutex> lock(m_callback_mutex);
    if (m_progress_callback) {
        try {
            m_progress_callback(report_id, progress);
        }
        catch (const std::exception& e) {
            add_error_message("Error in progress callback: " + std::string(e.what()));
        }
    }
}

std::string ReportProcess::generate_unique_report_id() const {
    auto now = std::chrono::system_clock::now();
    auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(1000, 9999);
    
    return "report_" + std::to_string(timestamp) + "_" + std::to_string(dis(gen));
}

bool ReportProcess::validate_report_config(const ReportProcessing::ReportConfig& config) const {
    if (config.title.empty()) {
        return false;
    }
    
    if (config.use_time_range && config.start_time >= config.end_time) {
        return false;
    }
    
    if (config.data_sources.empty()) {
        return false;
    }
    
    return true;
}