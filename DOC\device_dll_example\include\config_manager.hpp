#ifndef CONFIG_MANAGER_HPP
#define CONFIG_MANAGER_HPP

#include "common_types.hpp"
#include "data_structures.hpp"
#include "eth_client_interface.hpp"
#include <nlohmann/json.hpp>
#include <string>
#include <vector>
#include <memory>
#include <mutex>
#include <unordered_map>

namespace EthClient {

class ConfigManager {
public:
    // 嵌套结构体定义
    struct DriverInfo {
        std::string name;
        std::string version;
        std::string vendor;
        std::string description;
        std::string dll_filename;
        std::string device_type;
        bool supports_multi_instance;
        
        DriverInfo()
            : supports_multi_instance(false) {}
    };
    
    struct CapabilitiesConfig {
        uint32_t max_instances;
        bool supports_hot_plug;
        bool supports_calibration;
        bool auto_discovery;
    };
    
    struct ErrorHandlingConfig {
        bool auto_recovery;
        bool error_logging_enabled;
    };
    

    
    struct CommandValidation {
        bool required; // 是否必填
        double precision; // 精度
        struct Range {
            double min; // 最小值
            double max; // 最大值
        } range;// 范围
        std::string range_string;  // 用于复杂范围描述
    };
    
    struct CommandInfo {
        std::string command_name;    // 命令名称
        std::string function_name;   // 函数名称 应该和 命令名称相同
        std::string parameter_type;  // 参数类型
        std::vector<std::string> valid_values;  // 有效参数值
        std::string description;     // 命令描述
        std::string unit;           // 单位
        uint32_t response_time_ms;  // 响应时间
        CommandValidation validation;  // 参数校验
        std::string category;       // 所属分类
        bool is_implemented;// 是否实现
        
        CommandInfo() : response_time_ms(100), is_implemented(true) {}
    };
    
    struct CommandExecutionConfig {
        uint32_t timeout_ms;
        uint32_t retry_count;
        uint32_t retry_delay_ms;
        uint32_t queue_size;
        uint32_t concurrent_commands;
        bool priority_scheduling;
    };
    
    struct CommandValidationConfig {
        bool parameter_checking;
        bool range_validation;
        bool type_validation;
        std::vector<std::string> authorization_required;
    };
    
    struct CommandSystemConfig {
        std::vector<CommandInfo> commands;
        CommandExecutionConfig execution;
        CommandValidationConfig validation;
    };
    
    // 性能配置
    struct PerformanceConfig {
        uint32_t thread_pool_size;
        uint32_t command_timeout_ms;
        uint32_t max_concurrent_commands;
        uint32_t data_update_interval_ms;
        
        PerformanceConfig()
            : thread_pool_size(DEFAULT_THREAD_POOL_SIZE)
            , command_timeout_ms(DEFAULT_TIMEOUT_MS)
            , max_concurrent_commands(10)
            , data_update_interval_ms(1000) {}
    };
    
    // Redis配置
    struct RedisConfig {
        std::string host;
        uint16_t port;
        std::string password;
        uint32_t database;
        uint32_t timeout_ms;
        
        RedisConfig()
            : host("localhost")
            , port(6379)
            , database(0)
            , timeout_ms(5000) {}
    };

    // 构造函数和析构函数
    explicit ConfigManager();
    ~ConfigManager() = default;
    
    // 禁用拷贝构造函数和赋值运算符
    ConfigManager(const ConfigManager&) = delete;
    ConfigManager& operator=(const ConfigManager&) = delete;
    
    // 移动构造函数和赋值运算符
    ConfigManager(ConfigManager&& other) noexcept;
    ConfigManager& operator=(ConfigManager&& other) noexcept;
    
    // 配置加载和保存
    ATE_EC load_config();
    ATE_EC reload_config();
    
    // 设备配置
    std::vector<DeviceConfig> get_device_configs() const;
    bool get_device_config(const std::string& instance_id, DeviceConfig& config) const;
    std::string get_instance_id_by_device_index(uint32_t device_index) const;
    
    // 驱动程序信息
    DriverInfo get_driver_info() const;
    
    // 功能配置
    CapabilitiesConfig get_capabilities_config() const;
    
    // 错误处理配置
    ErrorHandlingConfig get_error_handling_config() const;
    
    // 命令系统配置
    std::vector<CommandInfo> get_command_system() const;
    CommandSystemConfig get_command_system_config() const;
    bool is_command_implemented(const std::string& command_name) const;
    
    // 验证
    ATE_EC validate_config() const;
    std::vector<std::string> get_validation_errors() const;
    
    // 实用函数
    std::string get_config_file_path() const;        // 获取配置文件路径
    bool is_config_loaded() const;                   // 检查配置是否已加载
    // get_last_modified_timestamp 方法已被移除
    
private:
    // 私有成员变量
    std::string m_config_file_path;
    mutable std::mutex m_config_mutex;
    
    // 配置数据
    DriverInfo m_driver_info;
    CapabilitiesConfig m_capabilities_config;
    std::vector<DeviceConfig> m_device_configs;
    PerformanceConfig m_performance_config;
    ErrorHandlingConfig m_error_handling_config;
    CommandSystemConfig m_command_system_config;
    
    // 状态变量
    std::atomic<bool> m_config_loaded;
    // m_last_modified_timestamp 已被移除，因为在当前系统中作用不大
    mutable std::string m_last_error;
    mutable std::vector<std::string> m_validation_errors;
    
    // 索引映射
    std::unordered_map<std::string, size_t> m_device_config_index;
    std::unordered_map<std::string, size_t> m_command_index;
    
    // JSON解析辅助函数
    ATE_EC parse_driver_info(const nlohmann::json& json);
    ATE_EC parse_capabilities_config(const nlohmann::json& json);
    ATE_EC parse_instance_configs(const nlohmann::json& json);
    ATE_EC parse_error_handling_config(const nlohmann::json& json);
    ATE_EC parse_command_system(const nlohmann::json& json);
    
    // 验证辅助函数
    bool validate_device_config(const DeviceConfig& config, std::vector<std::string>& errors) const;
    bool validate_redis_config(const RedisConfig& config, std::vector<std::string>& errors) const;
    
    // 文件操作辅助函数
    ATE_EC read_file(const std::string& file_path, std::string& content) const;
    bool file_exists(const std::string& file_path) const;
    // get_file_modified_time 方法已被移除
    
    // 错误处理辅助函数
    void m_set_last_error(const std::string& error) const;
};



} // namespace EthClient

#endif // CONFIG_MANAGER_HPP