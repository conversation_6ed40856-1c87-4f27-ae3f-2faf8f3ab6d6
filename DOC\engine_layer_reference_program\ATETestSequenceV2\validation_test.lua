-- ============================================================================
-- ATE测试序列系统功能完整性验证脚本 - Step-Based Framework
-- 文件: validation_test.lua
-- 描述: 验证ATE测试序列系统的功能完整性，包括框架结构、设备连接、测试流程等
-- 版本: V2.1 (标准化step-based工步控制框架)
-- ============================================================================

-- 工步控制变量
local step = 0
local run_flag = {1, 1, 1, 1, 1, 1, 1, 1, 0, 0}  -- 前8个工步启用
local test_result = "UNKNOWN"
local error_status = {}

-- 测试数据记录
local test_data_points = {}
local measurement_count = 0

-- 固定功能begin
function add_string(str)
    if not error_status[str] then
        error_status[str] = true
        return true
    end
    return false
end

-- 工步保护状态检查
function seq_protect_check()
    -- 验证脚本不需要设备状态检查，直接返回true
    return true
end

-- 工步保护动作执行
function seq_protect_act()
    if (next(error_status) ~= nil) then
        for error_key, _ in pairs(error_status) do
            log_message("[验证测试] 验证过程中发生错误: " .. error_key, "ERROR")
        end
        -- 通过故障保护动作的组合返回做3种情况，只记录+不动作+继续执行，记录+动作+继续执行，记录+动作+停止执行
        -- 无函数体 不动作
        -- return 1 动作后返回1  则动作了 后面停止执行
        -- return 2 动作了返回2  则动作了 后面继续执行
    end
    return false  -- 继续执行验证
end

-- 自定精确延时函数（毫秒级）
-- @param x 总延时时间（毫秒）
function delayms(x)
    local sleepms = 10  -- 每次循环基础延时
    local loops = math.floor(x / sleepms)  -- 完整循环次数
    local remainder = x % sleepms  -- 剩余延时
    
    -- 执行完整循环延时
    for i = 1, loops do
        -- 休眠循环基础延时 sleep_ms 采用C++注册的方式，注册一个通用的接口给lua
        if sleep_ms then sleep_ms(sleepms) end
        if seq_protect_check() == false then
            return
        end
    end
    
    -- 执行剩余延时
    if remainder > 0 then
        -- 休眠毫秒剩余
        if sleep_ms then sleep_ms(remainder) end
        if seq_protect_check() == false then
            return
        end
    end
end
-- 固定功能end

-- 验证结果记录
local validation_results = {
    framework_validation = {},
    script_validation = {},
    integration_validation = {},
    summary = {
        total_checks = 0,
        passed_checks = 0,
        failed_checks = 0,
        warnings = 0
    }
}

-- 验证日志函数
-- local function log_validation(level, message)
--     local timestamp = os.date("%Y-%m-%d %H:%M:%S")
--     print(string.format("[%s] [%s] %s", timestamp, level, message))
-- end

-- JSON报告写入函数
function write_validation_json_report()
    local json_report = {
        test_name = "ATE测试序列系统功能完整性验证",
        test_result = test_result,
        start_time = os.time(),
        end_time = os.time(),
        test_duration = 0,
        step_count = step,
        measurement_count = measurement_count,
        validation_summary = validation_results.summary,
        framework_validation = validation_results.framework_validation,
        script_validation = validation_results.script_validation,
        integration_validation = validation_results.integration_validation,
        test_data_points = test_data_points,
        error_messages = {},
        test_parameters = {
            total_checks = validation_results.summary.total_checks,
            passed_checks = validation_results.summary.passed_checks,
            failed_checks = validation_results.summary.failed_checks,
            warnings = validation_results.summary.warnings,
            success_rate = (validation_results.summary.passed_checks / validation_results.summary.total_checks) * 100
        }
    }
    
    -- 收集错误信息
    for error_key, _ in pairs(error_status) do
        table.insert(json_report.error_messages, error_key)
    end
    
    -- 调用C++接口写入JSON报告
    local success = write_test_report_json("validation_test", json_report)
    if success then
        log_message("[验证测试] JSON测试报告已生成", "INFO")
    else
        log_message("[验证测试] JSON测试报告生成失败", "ERROR")
    end
end

-- 检查函数
local function check_condition(condition, description, category)
    validation_results.summary.total_checks = validation_results.summary.total_checks + 1
    
    if condition then
        validation_results.summary.passed_checks = validation_results.summary.passed_checks + 1
        log_message("[验证测试] " .. description, "INFO")
        table.insert(validation_results[category], {status = "PASS", description = description})
        return true
    else
        validation_results.summary.failed_checks = validation_results.summary.failed_checks + 1
        log_message("[验证测试] " .. description, "ERROR")
        table.insert(validation_results[category], {status = "FAIL", description = description})
        return false
    end
end

-- 检查警告
local function check_warning(condition, description, category)
    if not condition then
        validation_results.summary.warnings = validation_results.summary.warnings + 1
        log_message("[验证测试] " .. description, "WARN")
        table.insert(validation_results[category], {status = "WARN", description = description})
    end
end

-- 1. 框架结构验证
local function validate_framework_structure()
    log_message("[验证测试] 开始验证框架结构...", "INFO")
    
    -- 检查主序列脚本
    local main_exists = io.open("e:\\KWL-Software\\EVT\\LuaExampleC\\ATETestSequenceV2\\main.lua", "r")
    check_condition(main_exists ~= nil, "主序列脚本main.lua存在", "framework_validation")
    if main_exists then main_exists:close() end
    
    -- 检查二级子序列脚本
    local sub_scripts = {
        "waveform_basic_test.lua",
        "charging_efficiency_test.lua", 
        "bidirectional_charging_test.lua",
        "temperature_cycle_test.lua"
    }
    
    for _, script in ipairs(sub_scripts) do
        local file = io.open("e:\\KWL-Software\\EVT\\LuaExampleC\\ATETestSequenceV2\\" .. script, "r")
        check_condition(file ~= nil, "二级子序列脚本" .. script .. "存在", "framework_validation")
        if file then file:close() end
    end
    
    -- 检查三级子序列脚本
    local detail_scripts = {
        "waveform_analysis_detail.lua",
        "efficiency_measurement_detail.lua",
        "v2g_protocol_detail.lua"
    }
    
    for _, script in ipairs(detail_scripts) do
        local file = io.open("e:\\KWL-Software\\EVT\\LuaExampleC\\ATETestSequenceV2\\" .. script, "r")
        check_condition(file ~= nil, "三级子序列脚本" .. script .. "存在", "framework_validation")
        if file then file:close() end
    end
    
    -- 检查监控模块
    local monitor_scripts = {
        "ate_system_level_monitor.lua",
        "ate_channel_monitor.lua"
    }
    
    for _, script in ipairs(monitor_scripts) do
        local file = io.open("e:\\KWL-Software\\EVT\\LuaExampleC\\ATETestSequenceV2\\" .. script, "r")
        check_condition(file ~= nil, "监控模块" .. script .. "存在", "framework_validation")
        if file then file:close() end
    end
end

-- 2. 脚本内容验证
local function validate_script_content()
    log_message("[验证测试] 开始验证脚本内容...", "INFO")
    
    -- 验证主要脚本的关键元素
    local scripts_to_validate = {
        "main.lua",
        "waveform_basic_test.lua",
        "charging_efficiency_test.lua",
        "bidirectional_charging_test.lua",
        "temperature_cycle_test.lua",
        "waveform_analysis_detail.lua"
    }
    
    for _, script_name in ipairs(scripts_to_validate) do
        local file_path = "e:\\KWL-Software\\EVT\\LuaExampleC\\ATETestSequenceV2\\" .. script_name
        local file = io.open(file_path, "r")
        
        if file then
            local content = file:read("*all")
            file:close()
            
            -- 检查step-based框架元素
            check_condition(content:find("local step"), script_name .. "包含step变量定义", "script_validation")
            check_condition(content:find("run_flag"), script_name .. "包含run_flag数组", "script_validation")
            check_condition(content:find("test_result"), script_name .. "包含test_result变量", "script_validation")
            check_condition(content:find("error_status"), script_name .. "包含error_status变量", "script_validation")
            
            -- 检查保护机制函数
            check_condition(content:find("seq_protect_check"), script_name .. "包含seq_protect_check函数", "script_validation")
            check_condition(content:find("seq_protect_act"), script_name .. "包含seq_protect_act函数", "script_validation")
            check_condition(content:find("delayms"), script_name .. "包含delayms函数", "script_validation")
            
            -- 检查标签分区结构
            check_condition(content:find("-- PRE"), script_name .. "包含PRE标签分区", "script_validation")
            check_condition(content:find("-- SEQ"), script_name .. "包含SEQ标签分区", "script_validation")
            check_condition(content:find("-- POST"), script_name .. "包含POST标签分区", "script_validation")
            
            -- 检查工步控制逻辑
            if script_name ~= "main.lua" then
                check_condition(content:find("if run_flag%[step%]"), script_name .. "包含工步控制逻辑", "script_validation")
                check_condition(content:find("step = step + 1"), script_name .. "包含工步递增逻辑", "script_validation")
            end
            
            -- 检查测试报告格式
            check_condition(content:find("completed_steps") or content:find("total_steps"), 
                          script_name .. "包含测试报告格式", "script_validation")
        else
            check_condition(false, script_name .. "文件可读", "script_validation")
        end
    end
end

-- 3. 集成验证
local function validate_integration()
    log_message("[验证测试] 开始验证脚本集成...", "INFO")
    
    -- 检查main.lua中的脚本引用
    local main_file = io.open("e:\\KWL-Software\\EVT\\LuaExampleC\\ATETestSequenceV2\\main.lua", "r")
    if main_file then
        local content = main_file:read("*all")
        main_file:close()
        
        -- 检查脚本间变量调用格式
        check_condition(content:find("waveform_basic_test%.test_result"), 
                      "main.lua正确引用waveform_basic_test.test_result", "integration_validation")
        check_condition(content:find("charging_efficiency_test%.test_result"), 
                      "main.lua正确引用charging_efficiency_test.test_result", "integration_validation")
        check_condition(content:find("bidirectional_charging_test%.test_result"), 
                      "main.lua正确引用bidirectional_charging_test.test_result", "integration_validation")
        check_condition(content:find("temperature_cycle_test%.test_result"), 
                      "main.lua正确引用temperature_cycle_test.test_result", "integration_validation")
    end
    
    -- 检查全局变量导出
    local scripts_with_exports = {
        {"waveform_basic_test.lua", "waveform_basic_test"},
        {"charging_efficiency_test.lua", "charging_efficiency_test"},
        {"bidirectional_charging_test.lua", "bidirectional_charging_test"},
        {"temperature_cycle_test.lua", "temperature_cycle_test"},
        {"waveform_analysis_detail.lua", "waveform_analysis_detail"}
    }
    
    for _, script_info in ipairs(scripts_with_exports) do
        local file_path = "e:\\KWL-Software\\EVT\\LuaExampleC\\ATETestSequenceV2\\" .. script_info[1]
        local file = io.open(file_path, "r")
        
        if file then
            local content = file:read("*all")
            file:close()
            
            local export_pattern = script_info[2] .. "%.test_result"
            check_condition(content:find(export_pattern), 
                          script_info[1] .. "正确导出test_result变量", "integration_validation")
        end
    end
end

-- 4. 一致性验证
local function validate_consistency()
    log_message("[验证测试] 开始验证一致性...", "INFO")
    
    -- 检查所有脚本是否使用相同的框架模式
    local framework_patterns = {
        "local step = ",
        "local run_flag = ",
        "local test_result = ",
        "local error_status = {},",
        "function seq_protect_check",
        "function seq_protect_act",
        "function delayms"
    }
    
    local scripts_to_check = {
        "waveform_basic_test.lua",
        "charging_efficiency_test.lua",
        "bidirectional_charging_test.lua",
        "temperature_cycle_test.lua",
        "waveform_analysis_detail.lua"
    }
    
    for _, pattern in ipairs(framework_patterns) do
        local pattern_found_count = 0
        
        for _, script in ipairs(scripts_to_check) do
            local file_path = "e:\\KWL-Software\\EVT\\LuaExampleC\\ATETestSequenceV2\\" .. script
            local file = io.open(file_path, "r")
            
            if file then
                local content = file:read("*all")
                file:close()
                
                if content:find(pattern) then
                    pattern_found_count = pattern_found_count + 1
                end
            end
        end
        
        check_condition(pattern_found_count == #scripts_to_check, 
                      "所有脚本都包含" .. pattern .. "模式", "framework_validation")
    end
end

-- 生成验证报告
local function generate_validation_report()
    log_message("[验证测试] 生成验证报告...", "INFO")
    
    print("\n" .. string.rep("=", 80))
    print("ATE测试序列系统功能完整性验证报告")
    print(string.rep("=", 80))
    print(string.format("验证时间: %s", os.date("%Y-%m-%d %H:%M:%S")))
    print(string.format("总检查项: %d", validation_results.summary.total_checks))
    print(string.format("通过检查: %d", validation_results.summary.passed_checks))
    print(string.format("失败检查: %d", validation_results.summary.failed_checks))
    print(string.format("警告数量: %d", validation_results.summary.warnings))
    
    local success_rate = (validation_results.summary.passed_checks / validation_results.summary.total_checks) * 100
    print(string.format("成功率: %.1f%%", success_rate))
    
    -- 详细结果
    local categories = {
        {"framework_validation", "框架结构验证"},
        {"script_validation", "脚本内容验证"},
        {"integration_validation", "集成验证"}
    }
    
    for _, category in ipairs(categories) do
        print("\n" .. string.rep("-", 40))
        print(category[2] .. ":")
        
        local results = validation_results[category[1]]
        for _, result in ipairs(results) do
            print(string.format("  [%s] %s", result.status, result.description))
        end
    end
    
    print("\n" .. string.rep("=", 80))
    
    -- 总体评估
    if success_rate >= 95 then
        print("总体评估: 优秀 - 系统功能完整性良好")
    elseif success_rate >= 85 then
        print("总体评估: 良好 - 系统基本功能完整")
    elseif success_rate >= 70 then
        print("总体评估: 一般 - 存在一些问题需要修复")
    else
        print("总体评估: 需要改进 - 存在较多问题")
    end
    
    return success_rate >= 85
end

-- 主验证函数
local function run_validation()
    log_message("[验证测试] 开始ATE测试序列系统功能完整性验证...", "INFO")
    
    -- 执行各项验证
    validate_framework_structure()
    validate_script_content()
    validate_integration()
    validate_consistency()
    
    -- 生成报告
    local validation_passed = generate_validation_report()
    
    log_message("[验证测试] 验证完成", "INFO")
    return validation_passed
end

-- 执行验证
if run_validation() then
    test_result = "PASS"
    print("\n验证结果: 系统功能完整性验证通过")
else
    test_result = "FAIL"
    print("\n验证结果: 系统功能完整性验证失败，请检查上述问题")
end

-- 写入JSON报告
write_validation_json_report()