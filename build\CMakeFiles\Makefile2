# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\mingw64\bin\cmake.exe

# The command to remove a file.
RM = C:\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\KWL-Software\EVT\EngineFrameworkInterface

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\KWL-Software\EVT\EngineFrameworkInterface\build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/EngineFrameworkInterface.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/EngineFrameworkInterface.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/EngineFrameworkInterface.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/EngineFrameworkInterface.dir

# All Build rule for target.
CMakeFiles/EngineFrameworkInterface.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\KWL-Software\EVT\EngineFrameworkInterface\build\CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13 "Built target EngineFrameworkInterface"
.PHONY : CMakeFiles/EngineFrameworkInterface.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/EngineFrameworkInterface.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\KWL-Software\EVT\EngineFrameworkInterface\build\CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/EngineFrameworkInterface.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\KWL-Software\EVT\EngineFrameworkInterface\build\CMakeFiles 0
.PHONY : CMakeFiles/EngineFrameworkInterface.dir/rule

# Convenience name for target.
EngineFrameworkInterface: CMakeFiles/EngineFrameworkInterface.dir/rule
.PHONY : EngineFrameworkInterface

# codegen rule for target.
CMakeFiles/EngineFrameworkInterface.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\KWL-Software\EVT\EngineFrameworkInterface\build\CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13 "Finished codegen for target EngineFrameworkInterface"
.PHONY : CMakeFiles/EngineFrameworkInterface.dir/codegen

# clean rule for target.
CMakeFiles/EngineFrameworkInterface.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\EngineFrameworkInterface.dir\build.make CMakeFiles/EngineFrameworkInterface.dir/clean
.PHONY : CMakeFiles/EngineFrameworkInterface.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

