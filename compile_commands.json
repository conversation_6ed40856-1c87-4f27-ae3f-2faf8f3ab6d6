[{"directory": "E:/KWL-Software/EVT/EngineFrameworkInterface/build", "command": "C:\\mingw64\\bin\\c++.exe -DDRIVER_MANAGER_DLL_EXPORTS -DEngineFrameworkInterface_EXPORTS @CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp -std=gnu++17 -o CMakeFiles\\EngineFrameworkInterface.dir\\src\\data_process.cpp.obj -c E:\\KWL-Software\\EVT\\EngineFrameworkInterface\\src\\data_process.cpp", "file": "E:/KWL-Software/EVT/EngineFrameworkInterface/src/data_process.cpp", "output": "CMakeFiles/EngineFrameworkInterface.dir/src/data_process.cpp.obj"}, {"directory": "E:/KWL-Software/EVT/EngineFrameworkInterface/build", "command": "C:\\mingw64\\bin\\c++.exe -DDRIVER_MANAGER_DLL_EXPORTS -DEngineFrameworkInterface_EXPORTS @CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp -std=gnu++17 -o CMakeFiles\\EngineFrameworkInterface.dir\\src\\data_recorder.cpp.obj -c E:\\KWL-Software\\EVT\\EngineFrameworkInterface\\src\\data_recorder.cpp", "file": "E:/KWL-Software/EVT/EngineFrameworkInterface/src/data_recorder.cpp", "output": "CMakeFiles/EngineFrameworkInterface.dir/src/data_recorder.cpp.obj"}, {"directory": "E:/KWL-Software/EVT/EngineFrameworkInterface/build", "command": "C:\\mingw64\\bin\\c++.exe -DDRIVER_MANAGER_DLL_EXPORTS -DEngineFrameworkInterface_EXPORTS @CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp -std=gnu++17 -o CMakeFiles\\EngineFrameworkInterface.dir\\src\\device_manager.cpp.obj -c E:\\KWL-Software\\EVT\\EngineFrameworkInterface\\src\\device_manager.cpp", "file": "E:/KWL-Software/EVT/EngineFrameworkInterface/src/device_manager.cpp", "output": "CMakeFiles/EngineFrameworkInterface.dir/src/device_manager.cpp.obj"}, {"directory": "E:/KWL-Software/EVT/EngineFrameworkInterface/build", "command": "C:\\mingw64\\bin\\c++.exe -DDRIVER_MANAGER_DLL_EXPORTS -DEngineFrameworkInterface_EXPORTS @CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp -std=gnu++17 -o CMakeFiles\\EngineFrameworkInterface.dir\\src\\engine_core.cpp.obj -c E:\\KWL-Software\\EVT\\EngineFrameworkInterface\\src\\engine_core.cpp", "file": "E:/KWL-Software/EVT/EngineFrameworkInterface/src/engine_core.cpp", "output": "CMakeFiles/EngineFrameworkInterface.dir/src/engine_core.cpp.obj"}, {"directory": "E:/KWL-Software/EVT/EngineFrameworkInterface/build", "command": "C:\\mingw64\\bin\\c++.exe -DDRIVER_MANAGER_DLL_EXPORTS -DEngineFrameworkInterface_EXPORTS @CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp -std=gnu++17 -o CMakeFiles\\EngineFrameworkInterface.dir\\src\\engine_framework_interface.cpp.obj -c E:\\KWL-Software\\EVT\\EngineFrameworkInterface\\src\\engine_framework_interface.cpp", "file": "E:/KWL-Software/EVT/EngineFrameworkInterface/src/engine_framework_interface.cpp", "output": "CMakeFiles/EngineFrameworkInterface.dir/src/engine_framework_interface.cpp.obj"}, {"directory": "E:/KWL-Software/EVT/EngineFrameworkInterface/build", "command": "C:\\mingw64\\bin\\c++.exe -DDRIVER_MANAGER_DLL_EXPORTS -DEngineFrameworkInterface_EXPORTS @CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp -std=gnu++17 -o CMakeFiles\\EngineFrameworkInterface.dir\\src\\engine_manager.cpp.obj -c E:\\KWL-Software\\EVT\\EngineFrameworkInterface\\src\\engine_manager.cpp", "file": "E:/KWL-Software/EVT/EngineFrameworkInterface/src/engine_manager.cpp", "output": "CMakeFiles/EngineFrameworkInterface.dir/src/engine_manager.cpp.obj"}, {"directory": "E:/KWL-Software/EVT/EngineFrameworkInterface/build", "command": "C:\\mingw64\\bin\\c++.exe -DDRIVER_MANAGER_DLL_EXPORTS -DEngineFrameworkInterface_EXPORTS @CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp -std=gnu++17 -o CMakeFiles\\EngineFrameworkInterface.dir\\src\\engine_variable_system.cpp.obj -c E:\\KWL-Software\\EVT\\EngineFrameworkInterface\\src\\engine_variable_system.cpp", "file": "E:/KWL-Software/EVT/EngineFrameworkInterface/src/engine_variable_system.cpp", "output": "CMakeFiles/EngineFrameworkInterface.dir/src/engine_variable_system.cpp.obj"}, {"directory": "E:/KWL-Software/EVT/EngineFrameworkInterface/build", "command": "C:\\mingw64\\bin\\c++.exe -DDRIVER_MANAGER_DLL_EXPORTS -DEngineFrameworkInterface_EXPORTS @CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp -std=gnu++17 -o CMakeFiles\\EngineFrameworkInterface.dir\\src\\global_variable_system.cpp.obj -c E:\\KWL-Software\\EVT\\EngineFrameworkInterface\\src\\global_variable_system.cpp", "file": "E:/KWL-Software/EVT/EngineFrameworkInterface/src/global_variable_system.cpp", "output": "CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_system.cpp.obj"}, {"directory": "E:/KWL-Software/EVT/EngineFrameworkInterface/build", "command": "C:\\mingw64\\bin\\c++.exe -DDRIVER_MANAGER_DLL_EXPORTS -DEngineFrameworkInterface_EXPORTS @CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp -std=gnu++17 -o CMakeFiles\\EngineFrameworkInterface.dir\\src\\global_variable_service.cpp.obj -c E:\\KWL-Software\\EVT\\EngineFrameworkInterface\\src\\global_variable_service.cpp", "file": "E:/KWL-Software/EVT/EngineFrameworkInterface/src/global_variable_service.cpp", "output": "CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_service.cpp.obj"}, {"directory": "E:/KWL-Software/EVT/EngineFrameworkInterface/build", "command": "C:\\mingw64\\bin\\c++.exe -DDRIVER_MANAGER_DLL_EXPORTS -DEngineFrameworkInterface_EXPORTS @CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp -std=gnu++17 -o CMakeFiles\\EngineFrameworkInterface.dir\\src\\logger.cpp.obj -c E:\\KWL-Software\\EVT\\EngineFrameworkInterface\\src\\logger.cpp", "file": "E:/KWL-Software/EVT/EngineFrameworkInterface/src/logger.cpp", "output": "CMakeFiles/EngineFrameworkInterface.dir/src/logger.cpp.obj"}, {"directory": "E:/KWL-Software/EVT/EngineFrameworkInterface/build", "command": "C:\\mingw64\\bin\\c++.exe -DDRIVER_MANAGER_DLL_EXPORTS -DEngineFrameworkInterface_EXPORTS @CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp -std=gnu++17 -o CMakeFiles\\EngineFrameworkInterface.dir\\src\\report_process.cpp.obj -c E:\\KWL-Software\\EVT\\EngineFrameworkInterface\\src\\report_process.cpp", "file": "E:/KWL-Software/EVT/EngineFrameworkInterface/src/report_process.cpp", "output": "CMakeFiles/EngineFrameworkInterface.dir/src/report_process.cpp.obj"}, {"directory": "E:/KWL-Software/EVT/EngineFrameworkInterface/build", "command": "C:\\mingw64\\bin\\c++.exe -DDRIVER_MANAGER_DLL_EXPORTS -DEngineFrameworkInterface_EXPORTS @CMakeFiles/EngineFrameworkInterface.dir/includes_CXX.rsp -std=gnu++17 -o CMakeFiles\\EngineFrameworkInterface.dir\\src\\system_monitor.cpp.obj -c E:\\KWL-Software\\EVT\\EngineFrameworkInterface\\src\\system_monitor.cpp", "file": "E:/KWL-Software/EVT/EngineFrameworkInterface/src/system_monitor.cpp", "output": "CMakeFiles/EngineFrameworkInterface.dir/src/system_monitor.cpp.obj"}]