-- ============================================================================
-- 双向充放电(V2G)测试序列 (二级子序列) - Step-Based Framework
-- 文件: bidirectional_charging_test.lua
-- 描述: 双向充放电功能测试，包含pre、seq、post三个标签分区
-- 版本: V2.1 (标准化step-based工步控制框架)
-- ============================================================================

-- 工步控制变量
local step = 0
local run_flag = {1, 1, 1, 1, 1, 0, 0, 0, 0, 0}  -- 前5个工步启用
local test_result = "UNKNOWN"
local error_status = {}

-- 测试数据记录
local test_data_points = {}
local measurement_count = 0

-- 固定功能begin
function add_string(str)
    if not error_status[str] then
        error_status[str] = true
        return true
    end
    return false
end

-- 工步保护状态检查
function seq_protect_check()
    -- 检查设备连接状态
    local battery_sim_status = dll_query_command(main.device_channels.battery_simulator, "STATUS?")
    if battery_sim_status == "ERROR" then
        add_string("电池模拟器设备故障")
    end
    
    local charger_status = dll_query_command(main.device_channels.bidirectional_charger, "STATUS?")
    if charger_status == "ERROR" then
        add_string("双向充电桩设备故障")
    end
    
    local grid_sim_status = dll_query_command(main.device_channels.grid_simulator, "STATUS?")
    if grid_sim_status == "ERROR" then
        add_string("电网模拟器设备故障")
    end
end

-- 工步保护动作执行
function seq_protect_act()
    if (next(error_status) ~= nil) then
        -- 发生故障时的保护动作
        dll_send_command(main.device_channels.bidirectional_charger, "OUTPUT:OFF")
        dll_send_command(main.device_channels.battery_simulator, "OUTPUT:OFF")
        dll_send_command(main.device_channels.grid_simulator, "OUTPUT:OFF")add_string("双向充电桩设备故障")
        for error_msg, _ in pairs(error_status) do
            log_message("ERROR", "[V2G测试] 执行保护动作: " .. error_msg)
        end
        -- 通过故障保护动作的组合返回做3种情况，只记录+不动作+继续执行，记录+动作+继续执行，记录+动作+停止执行
        --无函数体 不动作
        -- return 1 动作后返回1  则动作了 后面停止执行
        -- return 2 动作了返回2  则动作了 后面继续执行
        return 1
    end
end
-- 固定功能end

-- 子序列全局变量定义
bidirectional_charging_test = {
    test_result = "UNKNOWN",
    test_name = "双向充放电测试",
    start_time = 0,
    end_time = 0,
    step_count = 0,
    error_messages = {},
    test_data = {},
    charging_efficiency = 0.0,
    discharging_efficiency = 0.0
}

-- 测试参数配置
local test_params = {
    battery_voltage_nominal = 400.0,    -- 标称电池电压(V)
    battery_capacity = 50.0,            -- 电池容量(kWh)
    charging_current_max = 125.0,       -- 最大充电电流(A)
    discharging_current_max = 125.0,    -- 最大放电电流(A)
    charging_power_levels = {10.0, 25.0, 50.0}, -- 充电功率等级(kW)
    discharging_power_levels = {10.0, 25.0, 50.0}, -- 放电功率等级(kW)
    test_duration_per_level = 60.0,     -- 每个功率等级测试时长(s)
    voltage_tolerance = 0.02,           -- 电压容差(2%)
    current_tolerance = 0.05,           -- 电流容差(5%)
    min_efficiency = 0.85,              -- 最小效率要求(85%)
    stabilization_time = 10.0,          -- 稳定时间(s)
    measurement_interval = 2.0          -- 测量间隔(s)
}

-- 工具函数
-- function log_v2g_message(level, message)
--     local timestamp = os.date("%Y-%m-%d %H:%M:%S")
--     print(string.format("[%s] [V2G测试] %s: %s", timestamp, level, message))
-- end

function add_v2g_error(message)
    table.insert(bidirectional_charging_test.error_messages, message)
    log_message("ERROR", "[V2G测试] " .. message)
end

function record_v2g_data(mode, power_level, voltage, current, power, efficiency)
    table.insert(bidirectional_charging_test.test_data, {
        mode = mode,
        power_level = power_level,
        voltage = voltage,
        current = current,
        power = power,
        efficiency = efficiency,
        timestamp = os.time()
    })
end

function calculate_power_efficiency(measured_power, target_power)
    if target_power <= 0 then
        return 0.0
    end
    return (measured_power / target_power) * 100.0
end

-- JSON报告写入函数
function write_v2g_json_report()
    local test_duration = bidirectional_charging_test.end_time - bidirectional_charging_test.start_time
    local completed_steps = step
    
    local json_report = {
        test_name = bidirectional_charging_test.test_name,
        test_result = bidirectional_charging_test.test_result,
        test_duration = test_duration,
        completed_steps = completed_steps,
        step_count = bidirectional_charging_test.step_count,
        start_time = bidirectional_charging_test.start_time,
        end_time = bidirectional_charging_test.end_time,
        charging_efficiency = bidirectional_charging_test.charging_efficiency,
        discharging_efficiency = bidirectional_charging_test.discharging_efficiency,
        error_status = next(error_status) and error_status or {},
        error_messages = bidirectional_charging_test.error_messages,
        test_data = bidirectional_charging_test.test_data,
        test_params = test_params
    }
    
    -- 调用C++接口写入JSON文件
    local timestamp = os.date("%Y%m%d_%H%M%S")
    local filename = string.format("v2g_test_report_%s.json", timestamp)
    write_test_report_json(filename, json_report)
    log_message("INFO", "[V2G测试] JSON报告已写入: " .. filename)
end

-- ============================================================================
-- PRE 标签 - 预处理阶段
-- ============================================================================
::pre::
log_message("INFO", "[V2G测试] === 开始双向充放电测试预处理阶段 ===")
bidirectional_charging_test.start_time = os.time()
bidirectional_charging_test.step_count = 0
bidirectional_charging_test.error_messages = {}
bidirectional_charging_test.test_data = {}

-- 检查设备状态
if not device_initialized then
    add_v2g_error("设备未初始化，无法执行双向充放电测试")
    bidirectional_charging_test.test_result = "FAILED"
    goto post
end

-- 检查电池模拟器是否可用
local battery_sim_status = dll_query_command(main.device_channels.battery_simulator, "STATUS?")
if battery_sim_status == "ERROR" then
    add_v2g_error("电池模拟器设备不可用")
    bidirectional_charging_test.test_result = "FAILED"
    goto post
end

-- 检查双向充电桩是否可用
local charger_status = dll_query_command(main.device_channels.bidirectional_charger, "STATUS?")
if charger_status == "ERROR" then
    add_v2g_error("双向充电桩设备不可用")
    bidirectional_charging_test.test_result = "FAILED"
    goto post
end

-- 检查电网模拟器是否可用
local grid_sim_status = dll_query_command(main.device_channels.grid_simulator, "STATUS?")
if grid_sim_status == "ERROR" then
    add_v2g_error("电网模拟器设备不可用")
    bidirectional_charging_test.test_result = "FAILED"
    goto post
end

-- 配置电池模拟器
log_v2g_message("INFO", "配置电池模拟器...")
dll_send_command(main.device_channels.battery_simulator, "OUTPUT:OFF")
dll_send_command(main.device_channels.battery_simulator, string.format("VOLTAGE:%.1f", test_params.battery_voltage_nominal))
dll_send_command(main.device_channels.battery_simulator, string.format("CAPACITY:%.1f", test_params.battery_capacity))
dll_send_command(main.device_channels.battery_simulator, "SOC:50.0")  -- 设置初始SOC为50%
dll_send_command(main.device_channels.battery_simulator, "TEMPERATURE:25.0")

-- 配置电网模拟器
log_v2g_message("INFO", "配置电网模拟器...")
dll_send_command(main.device_channels.grid_simulator, "OUTPUT:OFF")
dll_send_command(main.device_channels.grid_simulator, "VOLTAGE:230.0")
dll_send_command(main.device_channels.grid_simulator, "FREQUENCY:50.0")
dll_send_command(main.device_channels.grid_simulator, "IMPEDANCE:0.1")

-- 配置双向充电桩
log_v2g_message("INFO", "配置双向充电桩...")
dll_send_command(main.device_channels.bidirectional_charger, "RESET")
dll_send_command(main.device_channels.bidirectional_charger, "MODE:V2G")
dll_send_command(main.device_channels.bidirectional_charger, string.format("VOLTAGE:MAX:%.1f", test_params.battery_voltage_nominal * 1.1))
dll_send_command(main.device_channels.bidirectional_charger, string.format("CURRENT:MAX:%.1f", test_params.charging_current_max))

-- 配置功率分析仪
log_v2g_message("INFO", "配置功率分析仪...")
dll_send_command(main.device_channels.power_analyzer, "RESET")
dll_send_command(main.device_channels.power_analyzer, "VOLTAGE:RANGE:AUTO")
dll_send_command(main.device_channels.power_analyzer, "CURRENT:RANGE:AUTO")
dll_send_command(main.device_channels.power_analyzer, "POWER:RANGE:AUTO")

-- 启动电网模拟器
log_v2g_message("INFO", "启动电网模拟器...")
dll_send_command(main.device_channels.grid_simulator, "OUTPUT:ON")

-- 启动电池模拟器
log_v2g_message("INFO", "启动电池模拟器...")
dll_send_command(main.device_channels.battery_simulator, "OUTPUT:ON")

-- 等待系统稳定
log_v2g_message("INFO", "等待系统稳定...")
os.execute(string.format("timeout /t %d /nobreak > nul", math.floor(test_params.stabilization_time)))

log_v2g_message("INFO", "=== 双向充放电测试预处理阶段完成 ===")

-- ============================================================================
-- SEQ 标签 - 测试序列阶段
-- ============================================================================
::seq::
-- 工步1: V2G协议测试和设备启动
if run_flag[1] ~= 0 then
    step = 1
    log_v2g_message("INFO", "工步1: V2G协议测试和设备启动")
    bidirectional_charging_test.step_count = bidirectional_charging_test.step_count + 1
    
    -- 执行V2G协议详细测试
    log_v2g_message("INFO", "执行V2G协议详细测试...")
    dofile("ATETestSequenceV2/v2g_protocol_detail.lua")
    
    -- 检查V2G协议测试结果
    if v2g_protocol_detail.test_result == "PASS" then
        -- 启动功率分析仪
        local analyzer_result = dll_send_command(main.device_channels.power_analyzer, "INTEGRATION:START")
        if analyzer_result == "OK" then
            test_result = "PASSED"
        else
            add_v2g_error("功率分析仪启动失败")
            test_result = "FAILED"
        end
    else
        add_v2g_error("V2G协议测试失败，无法继续双向充放电测试")
        test_result = "FAILED"
    end
    
    -- 每个工步的合适位置都进行故障监测
    seq_protect_check()
    -- 监测到故障后跳转
    if (seq_protect_act() == 1) then goto post end
end
    
-- 工步2: 10kW充电模式测试
if run_flag[2] ~= 0 then
    step = 2
    log_v2g_message("INFO", "工步2: 10kW充电模式测试")
    bidirectional_charging_test.step_count = bidirectional_charging_test.step_count + 1
    
    -- 设置充电模式
    dll_send_command(main.device_channels.bidirectional_charger, "MODE:CHARGING")
    
    local power_level = test_params.charging_power_levels[1]  -- 10kW
    local charging_current = (power_level * 1000.0) / test_params.battery_voltage_nominal
    
    -- 设置充电功率
    dll_send_command(main.device_channels.bidirectional_charger, string.format("CURRENT:%.2f", charging_current))
    
    -- 启动充电
    dll_send_command(main.device_channels.bidirectional_charger, "OUTPUT:ON")
    
    -- 等待稳定 - 内联延时逻辑
    local x = test_params.stabilization_time * 1000
    local sleepms = 10
    local loops = math.floor(x / sleepms)
    local remainder = x % sleepms
    for i = 1, loops do
        sleep_ms(sleepms)
        seq_protect_check() if (seq_protect_act() == 1) then goto post end
    end
    if remainder > 0 then
        sleep_ms(remainder)
        seq_protect_check() if (seq_protect_act() == 1) then goto post end
    end
            
            -- 测量充电参数
            local test_duration = test_params.test_duration_per_level
            local measurement_points = math.floor(test_duration / test_params.measurement_interval)
            local total_input_power = 0.0
            local total_output_power = 0.0
            
            for i = 1, measurement_points do
                -- 读取测量数据
                local input_power_str = dll_query_command(main.device_channels.power_analyzer, "INPUT:POWER?")
                local output_power_str = dll_query_command(main.device_channels.power_analyzer, "OUTPUT:POWER?")
                local voltage_str = dll_query_command(main.device_channels.power_analyzer, "VOLTAGE?")
                local current_str = dll_query_command(main.device_channels.power_analyzer, "CURRENT?")
                
                local input_power = tonumber(input_power_str) or 0.0
                local output_power = tonumber(output_power_str) or 0.0
                local voltage = tonumber(voltage_str) or 0.0
                local current = tonumber(current_str) or 0.0
                
                total_input_power = total_input_power + input_power
                total_output_power = total_output_power + output_power
                
                -- 记录测试数据
                measurement_count = measurement_count + 1
                table.insert(test_data_points, {
                    step = step,
                    measurement_id = measurement_count,
                    mode = "CHARGING",
                    power_level = power_level,
                    voltage = voltage,
                    current = current,
                    input_power = input_power,
                    output_power = output_power,
                    timestamp = os.time()
                })
                
                -- 等待下次测量 - 内联延时逻辑
                local x = test_params.measurement_interval * 1000
                local sleepms = 10
                local loops = math.floor(x / sleepms)
                local remainder = x % sleepms
                for j = 1, loops do
                    sleep_ms(sleepms)
                    seq_protect_check() if (seq_protect_act() == 1) then goto post end
                end
                if remainder > 0 then
                    sleep_ms(remainder)
                    seq_protect_check() if (seq_protect_act() == 1) then goto post end
                end
            end
            
            -- 计算平均效率
            local avg_input_power = total_input_power / measurement_points
            local avg_output_power = total_output_power / measurement_points
            local efficiency = calculate_power_efficiency(avg_output_power, avg_input_power)
            
            -- 记录充电效率数据
            record_v2g_data("CHARGING", power_level, test_params.battery_voltage_nominal, charging_current, avg_output_power, efficiency)
            
            -- 检查效率是否满足要求
            if efficiency >= (test_params.min_efficiency * 100.0) then
                log_v2g_message("INFO", string.format("10kW充电模式测试通过，效率: %.2f%%", efficiency))
                test_result = "PASSED"
            else
                add_v2g_error(string.format("10kW充电模式效率不足: %.2f%% < %.1f%%", efficiency, test_params.min_efficiency * 100.0))
                test_result = "FAILED"
            end
            
            -- 每个工步的合适位置都进行故障监测
            seq_protect_check()
            -- 监测到故障后跳转
            if (seq_protect_act() == 1) then goto post end
end

-- 工步3: 25kW和50kW充电模式测试
if run_flag[3] ~= 0 then
    step = 3
            log_v2g_message("INFO", "工步3: 25kW和50kW充电模式测试")
            bidirectional_charging_test.step_count = bidirectional_charging_test.step_count + 1
            
            local charging_efficiency_sum = 0.0
            local charging_test_count = 0
            local step_passed = true
            
            -- 测试25kW和50kW充电功率
            for i = 2, 3 do
                local power_level = test_params.charging_power_levels[i]
                local charging_current = (power_level * 1000.0) / test_params.battery_voltage_nominal
                
                log_v2g_message("INFO", string.format("测试充电功率: %.1fkW", power_level))
                
                -- 设置充电功率
                dll_send_command(main.device_channels.bidirectional_charger, string.format("CURRENT:%.2f", charging_current))
                dll_send_command(main.device_channels.bidirectional_charger, "OUTPUT:ON")
                
                -- 等待稳定 - 内联延时逻辑
                local x = test_params.stabilization_time * 1000
                local sleepms = 10
                local loops = math.floor(x / sleepms)
                local remainder = x % sleepms
                for j = 1, loops do
                    sleep_ms(sleepms)
                    seq_protect_check() if (seq_protect_act() == 1) then goto post end
                end
                if remainder > 0 then
                    sleep_ms(remainder)
                    seq_protect_check() if (seq_protect_act() == 1) then goto post end
                end
                
                -- 开始测量
                local measurement_start_time = os.clock()
                local voltage_sum = 0.0
                local current_sum = 0.0
                local power_sum = 0.0
                local measurement_count = 0
                
                while (os.clock() - measurement_start_time) < test_params.test_duration_per_level do
                    -- 读取电池端电压和电流
                    local battery_voltage_str = dll_query_command(main.device_channels.battery_simulator, "MEASURE:VOLTAGE?")
                    local battery_current_str = dll_query_command(main.device_channels.battery_simulator, "MEASURE:CURRENT?")
                    local battery_voltage = tonumber(battery_voltage_str) or 0.0
                    local battery_current = tonumber(battery_current_str) or 0.0
                    
                    -- 计算电池充电功率
                    local battery_power = (battery_voltage * battery_current) / 1000.0
                    
                    -- 累加测量值
                    voltage_sum = voltage_sum + battery_voltage
                    current_sum = current_sum + battery_current
                    power_sum = power_sum + battery_power
                    measurement_count = measurement_count + 1
                    
                    -- 等待下次测量 - 内联延时逻辑
                    local x = test_params.measurement_interval * 1000
                    local sleepms = 10
                    local loops = math.floor(x / sleepms)
                    local remainder = x % sleepms
                    for k = 1, loops do
                        sleep_ms(sleepms)
                        seq_protect_check() if (seq_protect_act() == 1) then goto post end
                    end
                    if remainder > 0 then
                        sleep_ms(remainder)
                        seq_protect_check() if (seq_protect_act() == 1) then goto post end
                    end
                end
                
                -- 计算平均值和效率
                local avg_voltage = voltage_sum / measurement_count
                local avg_current = current_sum / measurement_count
                local avg_power = power_sum / measurement_count
                local efficiency = calculate_power_efficiency(avg_power, power_level)
                
                -- 记录测试数据
                record_v2g_data("CHARGING", power_level, avg_voltage, avg_current, avg_power, efficiency)
                
                -- 检查充电性能
                local voltage_error = math.abs(avg_voltage - test_params.battery_voltage_nominal) / test_params.battery_voltage_nominal
                local power_error = math.abs(avg_power - power_level) / power_level
                
                if voltage_error > test_params.voltage_tolerance then
                    add_v2g_error(string.format("%.1fkW充电电压偏差过大: %.1fV (期望%.1fV, 偏差%.1f%%)", 
                                  power_level, avg_voltage, test_params.battery_voltage_nominal, voltage_error * 100.0))
                    step_passed = false
                end
                
                if power_error > 0.1 then
                    add_v2g_error(string.format("%.1fkW充电功率偏差过大: %.2fkW (期望%.1fkW, 偏差%.1f%%)", 
                                  power_level, avg_power, power_level, power_error * 100.0))
                    step_passed = false
                end
                
                if efficiency < (test_params.min_efficiency * 100.0) then
                    add_v2g_error(string.format("%.1fkW充电效率不达标: %.2f%% (要求≥%.1f%%)", 
                                  power_level, efficiency, test_params.min_efficiency * 100.0))
                    step_passed = false
                end
                
                charging_efficiency_sum = charging_efficiency_sum + efficiency
                charging_test_count = charging_test_count + 1
                
                log_v2g_message("INFO", string.format("%.1fkW充电测量完成: 电压=%.1fV, 电流=%.1fA, 效率=%.2f%%", 
                                power_level, avg_voltage, avg_current, efficiency))
                
                -- 关闭充电输出
                dll_send_command(main.device_channels.bidirectional_charger, "OUTPUT:OFF")
                -- 延时3秒 - 内联延时逻辑
                local x = 3000
                local sleepms = 10
                local loops = math.floor(x / sleepms)
                local remainder = x % sleepms
                for k = 1, loops do
                    sleep_ms(sleepms)
                    seq_protect_check() if (seq_protect_act() == 1) then goto post end
                end
                if remainder > 0 then
                    sleep_ms(remainder)
                    seq_protect_check() if (seq_protect_act() == 1) then goto post end
                end
            end
            
            -- 计算平均充电效率
            if charging_test_count > 0 then
                bidirectional_charging_test.charging_efficiency = charging_efficiency_sum / charging_test_count
            end
            
            if step_passed then
                test_result = "PASSED"
            else
                test_result = "FAILED"
            end
            
            -- 每个工步的合适位置都进行故障监测
            seq_protect_check()
            -- 监测到故障后跳转
            if (seq_protect_act() == 1) then goto post end
end
    
-- 工步4: 10kW放电模式测试
if run_flag[4] ~= 0 then
    step = 4
            log_v2g_message("INFO", "工步4: 10kW放电模式测试")
            bidirectional_charging_test.step_count = bidirectional_charging_test.step_count + 1
            
            -- 设置放电模式
            dll_send_command(main.device_channels.bidirectional_charger, "MODE:DISCHARGING")
            
            -- 设置电池SOC为80%以确保有足够电量放电
            dll_send_command(main.device_channels.battery_simulator, "SOC:80.0")
            -- 延时5秒 - 内联延时逻辑
            local x = 5000
            local sleepms = 10
            local loops = math.floor(x / sleepms)
            local remainder = x % sleepms
            for i = 1, loops do
                sleep_ms(sleepms)
                seq_protect_check() if (seq_protect_act() == 1) then goto post end
            end
            if remainder > 0 then
                sleep_ms(remainder)
                seq_protect_check() if (seq_protect_act() == 1) then goto post end
            end
            
            local power_level = test_params.discharging_power_levels[1]  -- 10kW
            local discharging_current = (power_level * 1000.0) / test_params.battery_voltage_nominal
            
            -- 设置放电功率
            dll_send_command(main.device_channels.bidirectional_charger, string.format("CURRENT:%.2f", discharging_current))
            dll_send_command(main.device_channels.bidirectional_charger, "OUTPUT:ON")
            
            -- 等待稳定 - 内联延时逻辑
            local x = test_params.stabilization_time * 1000
            local sleepms = 10
            local loops = math.floor(x / sleepms)
            local remainder = x % sleepms
            for i = 1, loops do
                sleep_ms(sleepms)
                seq_protect_check() if (seq_protect_act() == 1) then goto post end
            end
            if remainder > 0 then
                sleep_ms(remainder)
                seq_protect_check() if (seq_protect_act() == 1) then goto post end
            end
            
            -- 开始测量
            local measurement_start_time = os.clock()
            local voltage_sum = 0.0
            local current_sum = 0.0
            local battery_power_sum = 0.0
            local grid_power_sum = 0.0
            local measurement_count = 0
            
            while (os.clock() - measurement_start_time) < test_params.test_duration_per_level do
                -- 读取电池端电压和电流
                local battery_voltage_str = dll_query_command(main.device_channels.battery_simulator, "MEASURE:VOLTAGE?")
                local battery_current_str = dll_query_command(main.device_channels.battery_simulator, "MEASURE:CURRENT?")
                local battery_voltage = tonumber(battery_voltage_str) or 0.0
                local battery_current = tonumber(battery_current_str) or 0.0
                
                -- 读取电网输出功率
                local grid_power_str = dll_query_command(main.device_channels.power_analyzer, "POWER?")
                local grid_power = tonumber(grid_power_str) or 0.0
                
                -- 计算电池放电功率
                local battery_power = (battery_voltage * math.abs(battery_current)) / 1000.0
                
                -- 累加测量值
                voltage_sum = voltage_sum + battery_voltage
                current_sum = current_sum + math.abs(battery_current)
                battery_power_sum = battery_power_sum + battery_power
                grid_power_sum = grid_power_sum + grid_power
                measurement_count = measurement_count + 1
                
                -- 等待下次测量 - 内联延时逻辑
                local x = test_params.measurement_interval * 1000
                local sleepms = 10
                local loops = math.floor(x / sleepms)
                local remainder = x % sleepms
                for k = 1, loops do
                    sleep_ms(sleepms)
                    seq_protect_check() if (seq_protect_act() == 1) then goto post end
                end
                if remainder > 0 then
                    sleep_ms(remainder)
                    seq_protect_check() if (seq_protect_act() == 1) then goto post end
                end
            end
            
            -- 计算平均值和效率
            local avg_voltage = voltage_sum / measurement_count
            local avg_current = current_sum / measurement_count
            local avg_battery_power = battery_power_sum / measurement_count
            local avg_grid_power = grid_power_sum / measurement_count
            local efficiency = calculate_power_efficiency(avg_grid_power, avg_battery_power)
            
            -- 记录测试数据
            record_v2g_data("DISCHARGING", power_level, avg_voltage, avg_current, avg_battery_power, efficiency)
            
            -- 检查放电性能
            local voltage_error = math.abs(avg_voltage - test_params.battery_voltage_nominal) / test_params.battery_voltage_nominal
            local power_error = math.abs(avg_battery_power - power_level) / power_level
            
            local step_passed = true
            if voltage_error > test_params.voltage_tolerance then
                add_v2g_error(string.format("10kW放电电压偏差过大: %.1fV (期望%.1fV, 偏差%.1f%%)", 
                              avg_voltage, test_params.battery_voltage_nominal, voltage_error * 100.0))
                step_passed = false
            end
            
            if power_error > 0.1 then
                add_v2g_error(string.format("10kW放电功率偏差过大: %.2fkW (期望%.1fkW, 偏差%.1f%%)", 
                              avg_battery_power, power_level, power_error * 100.0))
                step_passed = false
            end
            
            if efficiency < (test_params.min_efficiency * 100.0) then
                add_v2g_error(string.format("10kW放电效率不达标: %.2f%% (要求≥%.1f%%)", 
                              efficiency, test_params.min_efficiency * 100.0))
                step_passed = false
            end
            
            log_v2g_message("INFO", string.format("10kW放电测量完成: 电压=%.1fV, 电流=%.1fA, 效率=%.2f%%", 
                            avg_voltage, avg_current, efficiency))
            
            -- 关闭放电输出
            dll_send_command(main.device_channels.bidirectional_charger, "OUTPUT:OFF")
            -- 延时3秒 - 内联延时逻辑
            local x = 3000
            local sleepms = 10
            local loops = math.floor(x / sleepms)
            local remainder = x % sleepms
            for k = 1, loops do
                sleep_ms(sleepms)
                seq_protect_check() if (seq_protect_act() == 1) then goto post end
            end
            if remainder > 0 then
                sleep_ms(remainder)
                seq_protect_check() if (seq_protect_act() == 1) then goto post end
            end
            
            if step_passed then
                test_result = "PASSED"
            else
                test_result = "FAILED"
            end
            
            -- 每个工步的合适位置都进行故障监测
            seq_protect_check()
            -- 监测到故障后跳转
            if (seq_protect_act() == 1) then goto post end
end
    
-- 工步5: 25kW和50kW放电模式测试和结果计算
if run_flag[5] ~= 0 then
    step = 5
            log_v2g_message("INFO", "工步5: 25kW和50kW放电模式测试和结果计算")
            bidirectional_charging_test.step_count = bidirectional_charging_test.step_count + 1
            
            local discharging_efficiency_sum = 0.0
            local discharging_test_count = 0
            local step_passed = true
            
            -- 测试25kW和50kW放电功率
            for i = 2, 3 do
                local power_level = test_params.discharging_power_levels[i]
                local discharging_current = (power_level * 1000.0) / test_params.battery_voltage_nominal
                
                log_v2g_message("INFO", string.format("测试放电功率: %.1fkW", power_level))
                
                -- 设置放电功率
                dll_send_command(main.device_channels.bidirectional_charger, string.format("CURRENT:%.2f", discharging_current))
                dll_send_command(main.device_channels.bidirectional_charger, "OUTPUT:ON")
                
                -- 等待稳定 - 内联延时逻辑
                local x = test_params.stabilization_time * 1000
                local sleepms = 10
                local loops = math.floor(x / sleepms)
                local remainder = x % sleepms
                for j = 1, loops do
                    sleep_ms(sleepms)
                    seq_protect_check() if (seq_protect_act() == 1) then goto post end
                end
                if remainder > 0 then
                    sleep_ms(remainder)
                    seq_protect_check() if (seq_protect_act() == 1) then goto post end
                end
                
                -- 开始测量
                local measurement_start_time = os.clock()
                local voltage_sum = 0.0
                local current_sum = 0.0
                local battery_power_sum = 0.0
                local grid_power_sum = 0.0
                local measurement_count = 0
                
                while (os.clock() - measurement_start_time) < test_params.test_duration_per_level do
                    -- 读取电池端电压和电流
                    local battery_voltage_str = dll_query_command(main.device_channels.battery_simulator, "MEASURE:VOLTAGE?")
                    local battery_current_str = dll_query_command(main.device_channels.battery_simulator, "MEASURE:CURRENT?")
                    local battery_voltage = tonumber(battery_voltage_str) or 0.0
                    local battery_current = tonumber(battery_current_str) or 0.0
                    
                    -- 读取电网输出功率
                    local grid_power_str = dll_query_command(main.device_channels.power_analyzer, "POWER?")
                    local grid_power = tonumber(grid_power_str) or 0.0
                    
                    -- 计算电池放电功率
                    local battery_power = (battery_voltage * math.abs(battery_current)) / 1000.0
                    
                    -- 累加测量值
                    voltage_sum = voltage_sum + battery_voltage
                    current_sum = current_sum + math.abs(battery_current)
                    battery_power_sum = battery_power_sum + battery_power
                    grid_power_sum = grid_power_sum + grid_power
                    measurement_count = measurement_count + 1
                    
                    -- 等待下次测量 - 内联延时逻辑
                    local x = test_params.measurement_interval * 1000
                    local sleepms = 10
                    local loops = math.floor(x / sleepms)
                    local remainder = x % sleepms
                    for k = 1, loops do
                        sleep_ms(sleepms)
                        seq_protect_check() if (seq_protect_act() == 1) then goto post end
                    end
                    if remainder > 0 then
                        sleep_ms(remainder)
                        seq_protect_check() if (seq_protect_act() == 1) then goto post end
                    end
                end
                
                -- 计算平均值和效率
                local avg_voltage = voltage_sum / measurement_count
                local avg_current = current_sum / measurement_count
                local avg_battery_power = battery_power_sum / measurement_count
                local avg_grid_power = grid_power_sum / measurement_count
                local efficiency = calculate_power_efficiency(avg_grid_power, avg_battery_power)
                
                -- 记录测试数据
                record_v2g_data("DISCHARGING", power_level, avg_voltage, avg_current, avg_battery_power, efficiency)
                
                -- 检查放电性能
                local voltage_error = math.abs(avg_voltage - test_params.battery_voltage_nominal) / test_params.battery_voltage_nominal
                local power_error = math.abs(avg_battery_power - power_level) / power_level
                
                if voltage_error > test_params.voltage_tolerance then
                    add_v2g_error(string.format("%.1fkW放电电压偏差过大: %.1fV (期望%.1fV, 偏差%.1f%%)", 
                                  power_level, avg_voltage, test_params.battery_voltage_nominal, voltage_error * 100.0))
                    step_passed = false
                end
                
                if power_error > 0.1 then
                    add_v2g_error(string.format("%.1fkW放电功率偏差过大: %.2fkW (期望%.1fkW, 偏差%.1f%%)", 
                                  power_level, avg_battery_power, power_level, power_error * 100.0))
                    step_passed = false
                end
                
                if efficiency < (test_params.min_efficiency * 100.0) then
                    add_v2g_error(string.format("%.1fkW放电效率不达标: %.2f%% (要求≥%.1f%%)", 
                                  power_level, efficiency, test_params.min_efficiency * 100.0))
                    step_passed = false
                end
                
                discharging_efficiency_sum = discharging_efficiency_sum + efficiency
                discharging_test_count = discharging_test_count + 1
                
                log_v2g_message("INFO", string.format("%.1fkW放电测量完成: 电压=%.1fV, 电流=%.1fA, 效率=%.2f%%", 
                                power_level, avg_voltage, avg_current, efficiency))
                
                -- 关闭放电输出
                dll_send_command(main.device_channels.bidirectional_charger, "OUTPUT:OFF")
                -- 延时3秒 - 内联延时逻辑
                local x = 3000
                local sleepms = 10
                local loops = math.floor(x / sleepms)
                local remainder = x % sleepms
                for k = 1, loops do
                    sleep_ms(sleepms)
                    seq_protect_check() if (seq_protect_act() == 1) then goto post end
                end
                if remainder > 0 then
                    sleep_ms(remainder)
                    seq_protect_check() if (seq_protect_act() == 1) then goto post end
                end
            end
            
            -- 计算平均放电效率
            if discharging_test_count > 0 then
                bidirectional_charging_test.discharging_efficiency = discharging_efficiency_sum / discharging_test_count
            end
            
            -- 判断最终测试结果
            local final_test_passed = step_passed and 
                                    (#bidirectional_charging_test.error_messages == 0) and 
                                    (bidirectional_charging_test.charging_efficiency >= (test_params.min_efficiency * 100.0)) and
                                    (bidirectional_charging_test.discharging_efficiency >= (test_params.min_efficiency * 100.0))
            
            if final_test_passed then
                log_v2g_message("INFO", "双向充放电测试序列阶段通过")
                test_result = "PASSED"
            else
                log_v2g_message("ERROR", "双向充放电测试序列阶段失败")
                test_result = "FAILED"
            end
            
            -- 每个工步的合适位置都进行故障监测
            seq_protect_check()
            -- 监测到故障后跳转
            if (seq_protect_act() == 1) then goto post end
end

-- ============================================================================
-- POST 标签 - 后处理阶段
-- ============================================================================
::post::
log_message("INFO", "[V2G测试] === 开始双向充放电测试后处理阶段 ===")
bidirectional_charging_test.end_time = os.time()

-- 关闭所有设备输出
dll_send_command(main.device_channels.bidirectional_charger, "OUTPUT:OFF")
dll_send_command(main.device_channels.battery_simulator, "OUTPUT:OFF")
dll_send_command(main.device_channels.grid_simulator, "OUTPUT:OFF")
log_message("INFO", "[V2G测试] 所有设备输出已关闭")

-- 停止功率分析仪
dll_send_command(main.device_channels.power_analyzer, "INTEGRATION:STOP")
log_message("INFO", "[V2G测试] 功率分析仪已停止")

-- 生成测试报告
local test_duration = bidirectional_charging_test.end_time - bidirectional_charging_test.start_time
local total_measurements = #bidirectional_charging_test.test_data
local completed_steps = step - 1
local total_steps = 5

-- 生成详细测试报告
local test_report = {
    test_name = bidirectional_charging_test.test_name,
    test_result = test_result,
    test_duration = test_duration,
    completed_steps = completed_steps,
    total_steps = total_steps,
    step_count = bidirectional_charging_test.step_count,
    measurement_points = total_measurements,
    average_charging_efficiency = bidirectional_charging_test.charging_efficiency,
    average_discharging_efficiency = bidirectional_charging_test.discharging_efficiency,
    error_status = next(error_status) and error_status or {},
    error_messages = bidirectional_charging_test.error_messages,
    detailed_data = bidirectional_charging_test.test_data
}

-- 确定最终测试结果
if test_result == "PASSED" and #bidirectional_charging_test.error_messages == 0 and 
   bidirectional_charging_test.charging_efficiency >= (test_params.min_efficiency * 100.0) and
   bidirectional_charging_test.discharging_efficiency >= (test_params.min_efficiency * 100.0) then
    bidirectional_charging_test.test_result = "PASS"
else
    bidirectional_charging_test.test_result = "FAIL"
end

-- 输出测试结果
print("\n" .. string.rep("-", 60))
print("双向充放电(V2G)测试结果报告")
print(string.rep("-", 60))
print(string.format("测试名称: %s", bidirectional_charging_test.test_name))
print(string.format("测试结果: %s", bidirectional_charging_test.test_result))
print(string.format("测试时长: %d 秒", test_duration))
print(string.format("测试步骤: %d", bidirectional_charging_test.step_count))
print(string.format("测量点数: %d", total_measurements))
print(string.format("充电平均效率: %.2f%%", bidirectional_charging_test.charging_efficiency))
print(string.format("放电平均效率: %.2f%%", bidirectional_charging_test.discharging_efficiency))
print(string.format("效率要求: ≥%.1f%%", test_params.min_efficiency * 100.0))
print(string.format("错误数量: %d", #bidirectional_charging_test.error_messages))

if #bidirectional_charging_test.error_messages > 0 then
    print("错误信息:")
    for i, error_msg in ipairs(bidirectional_charging_test.error_messages) do
        print(string.format("  %d. %s", i, error_msg))
    end
end

-- 输出详细测试数据
if total_measurements > 0 then
    print("\n详细测试数据:")
    print("模式      功率(kW)  电压(V)  电流(A)  效率(%)")
    print(string.rep("-", 60))
    for _, data in ipairs(bidirectional_charging_test.test_data) do
        print(string.format("%-8s  %7.1f  %6.1f  %6.1f  %7.2f", 
              data.mode, data.power_level, data.voltage, data.current, data.efficiency))
    end
end
print(string.rep("-", 60))

-- 写入JSON报告
write_v2g_json_report()

log_message("INFO", "[V2G测试] === 双向充放电测试后处理阶段完成 ===")
log_message("INFO", string.format("[V2G测试] 双向充放电测试最终结果: %s", bidirectional_charging_test.test_result))

-- 返回测试结果
return bidirectional_charging_test.test_result