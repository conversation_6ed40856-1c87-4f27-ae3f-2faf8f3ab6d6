#ifndef E5000_DLL_INTERFACE_HPP
#define E5000_DLL_INTERFACE_HPP

#include "common_types.hpp"
#include "data_structures.hpp"

extern "C" {

#ifdef _WIN32
    #ifdef E5000_DLL_EXPORTS
        #define EXP_API __declspec(dllexport)
    #else
        #define EXP_API __declspec(dllimport)
    #endif
#else
    #define EXP_API __attribute__((visibility("default")))
#endif

// ATE通用错误代码枚举 - 适用于所有外设DLL
typedef enum {
    // 成功状态
    ATE_SUCCESS = 0,                      // 操作成功
    
    // 参数相关错误 (1-19)
    ATE_ERROR_INVALID_PARAMETER = 1,      // 无效参数
    ATE_ERROR_NULL_POINTER = 2,           // 空指针
    ATE_ERROR_BUFFER_TOO_SMALL = 3,       // 缓冲区太小
    ATE_ERROR_INVALID_RANGE = 4,          // 参数超出有效范围
    ATE_ERROR_INVALID_FORMAT = 5,         // 格式错误
    
    // 初始化相关错误 (20-39)
    ATE_ERROR_NOT_INITIALIZED = 20,       // 未初始化
    ATE_ERROR_ALREADY_INITIALIZED = 21,   // 已经初始化
    ATE_ERROR_INITIALIZATION_FAILED = 22, // 初始化失败
    ATE_ERROR_CONFIG_LOAD_FAILED = 23,    // 配置加载失败
    ATE_ERROR_LIBRARY_LOAD_FAILED = 24,   // 库加载失败
    
    // 设备相关错误 (40-79)
    ATE_ERROR_DEVICE_NOT_FOUND = 40,      // 设备未找到
    ATE_ERROR_DEVICE_ALREADY_EXISTS = 41, // 设备已存在
    ATE_ERROR_DEVICE_BUSY = 42,           // 设备忙
    ATE_ERROR_DEVICE_NOT_READY = 43,      // 设备未就绪
    ATE_ERROR_DEVICE_OFFLINE = 44,        // 设备离线
    ATE_ERROR_DEVICE_FAULT = 45,          // 设备故障
    ATE_ERROR_DEVICE_OVERLOAD = 46,       // 设备过载
    ATE_ERROR_DEVICE_PROTECTION = 47,     // 设备保护
    ATE_ERROR_DEVICE_CALIBRATION = 48,    // 设备校准错误
    ATE_ERROR_DEVICE_FIRMWARE = 49,       // 固件错误
    
    // 通信相关错误 (80-119)
    ATE_ERROR_CONNECTION_FAILED = 80,     // 连接失败
    ATE_ERROR_CONNECTION_LOST = 81,       // 连接丢失
    ATE_ERROR_CONNECTION_TIMEOUT = 82,    // 连接超时
    ATE_ERROR_COMMUNICATION_TIMEOUT = 83, // 通信超时
    ATE_ERROR_COMMUNICATION_ERROR = 84,   // 通信错误
    ATE_ERROR_PROTOCOL_ERROR = 85,        // 协议错误
    ATE_ERROR_CHECKSUM_ERROR = 86,        // 校验和错误
    ATE_ERROR_NETWORK_ERROR = 87,         // 网络错误
    ATE_ERROR_PORT_IN_USE = 88,           // 端口被占用
    ATE_ERROR_AUTHENTICATION_FAILED = 89, // 认证失败
    
    // 命令相关错误 (120-139)
    ATE_ERROR_INVALID_COMMAND = 120,      // 无效命令
    ATE_ERROR_COMMAND_NOT_SUPPORTED = 121,// 命令不支持
    ATE_ERROR_COMMAND_FAILED = 122,       // 命令执行失败
    ATE_ERROR_COMMAND_TIMEOUT = 123,      // 命令超时
    ATE_ERROR_COMMAND_ABORTED = 124,      // 命令被中止
    
    // 资源相关错误 (140-159)
    ATE_ERROR_INSUFFICIENT_MEMORY = 140,  // 内存不足
    ATE_ERROR_RESOURCE_BUSY = 141,        // 资源忙
    ATE_ERROR_RESOURCE_UNAVAILABLE = 142, // 资源不可用
    ATE_ERROR_QUOTA_EXCEEDED = 143,       // 配额超限
    ATE_ERROR_HANDLE_INVALID = 144,       // 句柄无效
    
    // 文件系统相关错误 (160-179)
    ATE_ERROR_FILE_NOT_FOUND = 160,       // 文件未找到
    ATE_ERROR_FILE_ACCESS_DENIED = 161,   // 文件访问拒绝
    ATE_ERROR_FILE_ALREADY_EXISTS = 162,  // 文件已存在
    ATE_ERROR_FILE_CORRUPTED = 163,       // 文件损坏
    ATE_ERROR_DISK_FULL = 164,            // 磁盘空间不足
    ATE_ERROR_PATH_NOT_FOUND = 165,       // 路径未找到
    
    // 权限相关错误 (180-199)
    ATE_ERROR_PERMISSION_DENIED = 180,    // 权限拒绝
    ATE_ERROR_ACCESS_DENIED = 181,        // 访问拒绝
    ATE_ERROR_OPERATION_NOT_PERMITTED = 182, // 操作不被允许
    ATE_ERROR_INSUFFICIENT_PRIVILEGES = 183, // 权限不足
    
    // 数据相关错误 (200-219)
    ATE_ERROR_DATA_INVALID = 200,         // 数据无效
    ATE_ERROR_DATA_CORRUPTED = 201,       // 数据损坏
    ATE_ERROR_DATA_OVERFLOW = 202,        // 数据溢出
    ATE_ERROR_DATA_UNDERFLOW = 203,       // 数据下溢
    ATE_ERROR_DATA_OUT_OF_RANGE = 204,    // 数据超出范围
    ATE_ERROR_CONVERSION_FAILED = 205,    // 转换失败
    
    // 线程和同步相关错误 (220-239)
    ATE_ERROR_THREAD_CREATE_FAILED = 220, // 线程创建失败
    ATE_ERROR_THREAD_JOIN_FAILED = 221,   // 线程等待失败
    ATE_ERROR_MUTEX_LOCK_FAILED = 222,    // 互斥锁失败
    ATE_ERROR_SEMAPHORE_FAILED = 223,     // 信号量失败
    ATE_ERROR_DEADLOCK_DETECTED = 224,    // 检测到死锁
    
    // 系统相关错误 (240-259)
    ATE_ERROR_SYSTEM_ERROR = 240,         // 系统错误
    ATE_ERROR_OUT_OF_MEMORY = 241,        // 系统内存不足
    ATE_ERROR_SYSTEM_BUSY = 242,          // 系统忙
    ATE_ERROR_OPERATION_CANCELLED = 243,  // 操作被取消
    ATE_ERROR_OPERATION_ABORTED = 244,    // 操作被中止
    ATE_ERROR_SERVICE_UNAVAILABLE = 245,  // 服务不可用
    
    // 特定于测试设备的错误 (260-299)
    ATE_ERROR_MEASUREMENT_FAILED = 260,   // 测量失败
    ATE_ERROR_CALIBRATION_REQUIRED = 261, // 需要校准
    ATE_ERROR_SELF_TEST_FAILED = 262,     // 自检失败
    ATE_ERROR_TEMPERATURE_OUT_OF_RANGE = 263, // 温度超出范围
    ATE_ERROR_VOLTAGE_OUT_OF_RANGE = 264, // 电压超出范围
    ATE_ERROR_CURRENT_OUT_OF_RANGE = 265, // 电流超出范围
    ATE_ERROR_FREQUENCY_OUT_OF_RANGE = 266, // 频率超出范围
    ATE_ERROR_POWER_SUPPLY_FAULT = 267,   // 电源故障
    ATE_ERROR_SAFETY_INTERLOCK = 268,     // 安全联锁
    ATE_ERROR_OVERTEMPERATURE = 269,      // 过温保护
    
    // 预留给厂商自定义错误 (300-998)
    ATE_ERROR_VENDOR_SPECIFIC_BASE = 300, // 厂商自定义错误基础值
    
    // 通用未知错误
    ATE_ERROR_UNKNOWN = 999               // 未知错误
} ATE_EC;

// Command modes for execute_command_unified
typedef enum {
    COMMAND_WRITE_ONLY = 0,    // 只写入，不等待响应
    COMMAND_READ_ONLY = 1,     // 只读取，不写入
    COMMAND_QUERY = 2,         // 查询模式，等待文本响应
    COMMAND_DYNAIC_CONFIG = 3, // 动态配置，根据命令动态调整
    // COMMAND_BINARY = 2,        // 二进制数据传输模式
    // COMMAND_ASYNC_WRITE = 3    // 异步写入模式
} E5000_CommandMode;

// =============================================================================
// DLL导出函数 - 只导出以下7个函数给外部使用
// =============================================================================

/**
 * 初始化DLL资源
 * 创建并初始化设备管理器，加载配置文件，准备设备连接环境
 * @return 成功返回ATE_SUCCESS(0)，失败返回对应的ATE_EC错误码
 * @note 必须在调用其他DLL函数之前先调用此函数
 * @note 重复调用此函数是安全的，会直接返回成功
 */
EXP_API ATE_EC initialize_dll();

/**
 * 清理DLL资源
 * 断开所有设备连接，释放设备管理器资源，清理内部缓存
 * @return 成功返回ATE_SUCCESS(0)，失败返回对应的ATE_EC错误码
 * @note 程序退出前应调用此函数进行资源清理
 * @note 清理后需要重新调用initialize_dll()才能使用其他功能
 */
EXP_API ATE_EC cleanup_dll();

/**
 * 连接设备
 * 根据通道号从配置文件中查找对应的设备实例，建立Modbus TCP连接
 * @param channel_no 通道号，对应配置文件中的device_index
 * @return 成功返回ATE_SUCCESS(0)，失败返回对应的ATE_EC错误码
 * @note 连接成功后会启动设备数据采集线程
 * @note 如果设备已连接，返回ATE_ERROR_DEVICE_ALREADY_EXISTS
 */
EXP_API ATE_EC connect_device(const int32_t channel_no);

/**
 * 断开设备连接
 * 停止设备数据采集线程，关闭Modbus TCP连接，清理相关资源
 * @param channel_no 通道号，对应配置文件中的device_index
 * @return 成功返回ATE_SUCCESS(0)，失败返回对应的ATE_EC错误码
 * @note 断开连接后会清理该设备的响应缓存和索引映射
 */
EXP_API ATE_EC disconnect_device(const int32_t channel_no);

/**
 * 获取设备描述符
 * 返回设备的配置信息JSON字符串，包含设备类型、通信参数等信息
 * @param channel_no 通道号，对应配置文件中的device_index
 * @param device_descriptor 输出缓冲区，用于存储设备描述符JSON字符串
 * @param buffer_size 输入缓冲区大小
 * @param device_descriptor_size 输出参数，返回实际描述符JSON字符串长度
 * @return 成功返回ATE_SUCCESS(0)，失败返回对应的ATE_EC错误码
 * @note 如果缓冲区太小，返回ATE_ERROR_BUFFER_TOO_SMALL并设置所需大小
 * @note 设备无需连接即可获取描述符信息
 */
EXP_API ATE_EC get_device_descriptor(const int32_t channel_no, char* device_descriptor, const int32_t buffer_size, int32_t* device_descriptor_size);

/**
 * 执行统一命令
 * 支持多种命令模式：写入控制命令和透传查询命令
 * @param channel_no 通道号，对应配置文件中的device_index
 * @param mode 命令模式：COMMAND_WRITE_ONLY(0)=设备控制写入, COMMAND_QUERY(2)=透传查询
 * @param command 命令字符串，写入模式支持"CC=1.5;CV=12.0;ONOFF=ON"格式，查询模式为原始Modbus命令
 * @param command_length 命令数据的长度，为0时自动计算字符串长度
 * @param timeout_ms 超时时间(毫秒)，仅对查询模式有效
 * @param response 输出参数，用于存储响应数据（仅查询模式使用）
 * @param buffer_size 响应缓冲区大小
 * @param response_size 输出参数，返回实际响应数据的长度
 * @return 成功返回ATE_SUCCESS(0)，失败返回对应的ATE_EC错误码
 * @note 写入模式：支持CC/CV/CP/CR参数设置和ONOFF电源控制
 * @note 查询模式：直接透传Modbus命令到设备并返回原始响应
 */
EXP_API ATE_EC execute_command_unified(const int32_t channel_no, const E5000_CommandMode mode, const char* command, const int32_t command_length, const int32_t timeout_ms, char* response, const int32_t buffer_size, int32_t* response_size);

/**
 * 获取通道状态
 * 返回设备当前的实时数据，包括测量值、运行状态、错误信息等完整状态信息
 * @param channel_no 通道号，对应配置文件中的device_index
 * @param channel_state 输出缓冲区，用于存储通道状态JSON字符串
 * @param buffer_size 缓冲区大小
 * @param channel_state_size 输出参数，返回实际状态JSON字符串的长度
 * @return 成功返回ATE_SUCCESS(0)，失败返回对应的ATE_EC错误码
 * @note 返回的JSON包含E5000DataBlockV2结构体的所有字段数据
 * @note 如果缓冲区太小，返回ATE_ERROR_BUFFER_TOO_SMALL并设置所需大小
 * @note 设备必须已连接才能获取实时状态数据
 */
EXP_API ATE_EC get_channel_state(const int32_t channel_no, char *channel_state, const int32_t buffer_size, int32_t *channel_state_size);

} // extern "C"

#endif // E5000_DLL_INTERFACE_HPP