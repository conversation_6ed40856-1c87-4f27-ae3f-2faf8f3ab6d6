#ifndef DEVICE_MANAGER_HPP
#define DEVICE_MANAGER_HPP

#include "common_types.hpp"
#include "data_structures.hpp"
#include "tcp_udp_client.hpp"
#include "config_manager.hpp"
#include "eth_client_interface.hpp"
#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <thread>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <functional>
#include <shared_mutex>
#include <future>
#include <chrono>

namespace EthClient {



class DeviceManager {
public:
    DeviceManager(std::unique_ptr<ConfigManager> config_manager);
    ~DeviceManager();
    
    // 禁用拷贝构造函数和赋值运算符
    DeviceManager(const DeviceManager&) = delete;
    DeviceManager& operator=(const DeviceManager&) = delete;
    
    // 移动构造函数和赋值运算符
    DeviceManager(DeviceManager&& other) noexcept;
    DeviceManager& operator=(DeviceManager&& other) noexcept;
    
    // 生命周期管理
    // 注意：initialize()假设配置已经通过ConfigManager::load_config()加载
    // 通常由调用者（如e5000_dll_interface.cpp）负责配置加载
    ATE_EC initialize();
    ATE_EC shutdown();
    bool is_initialized() const;
    
    // 设备管理
    ATE_EC add_device(const DeviceConfig& config);
    ATE_EC remove_device(const std::string& instance_id);
    ATE_EC connect_device(const std::string& instance_id);
    ATE_EC disconnect_device(const std::string& instance_id);
    ATE_EC reconnect_device(const std::string& instance_id);
    
    // 设备信息
    std::vector<std::string> get_device_list() const;
    bool get_device_config(const std::string& instance_id, DeviceConfig& config) const;
    ConnectionStatus get_device_connection_status(const std::string& instance_id) const;
    
    // 配置管理器访问
    const ConfigManager* get_config_manager() const;
    
    // 数据操作
    std::string read_channel_state(const std::string& instance_id, ChannelId channel_id);
    
    // 设备参数操作
    ATE_EC write_command_data(const std::string& instance_id, const std::string& hex_string);
    
    // 接收缓冲区数据读取
    ATE_EC read_received_buffer_data(const std::string& instance_id, uint8_t* buffer, int32_t buffer_size, int32_t* response_size);
    
    // ModbusClient访问
    std::shared_ptr<TcpUdpClient> get_modbus_client(const std::string& instance_id) const;
    
    
    // 统计信息
    struct DeviceStatistics { // 设备统计结构体
        uint32_t total_commands_sent;        // 发送的总命令数
        uint32_t successful_commands;        // 成功的命令数
        uint32_t failed_commands;            // 失败的命令数
        uint32_t connection_attempts;        // 连接尝试次数
        uint32_t successful_connections;     // 成功连接次数
        uint64_t last_communication_timestamp; // 最后通信时间戳
        double average_response_time_ms;     // 平均响应时间(毫秒)
    };
    
    bool get_device_statistics(const std::string& instance_id, DeviceStatistics& stats) const;
    void reset_device_statistics(const std::string& instance_id);
    void reset_all_statistics();
    
    // 错误处理
    std::string get_last_error() const;
    std::vector<std::string> get_device_errors(const std::string& instance_id) const;
    void clear_device_errors(const std::string& instance_id);
    
    // 配置更新
    ATE_EC reload_configuration();
    
    // 设备线程管理
    ATE_EC start_device_thread(const std::string& instance_id);
    ATE_EC stop_device_thread(const std::string& instance_id);
    ATE_EC pause_device_thread(const std::string& instance_id);
    ATE_EC resume_device_thread(const std::string& instance_id);
    
    // 设备任务队列管理
    ATE_EC enqueue_device_task(const std::string& instance_id, std::function<void()> task);
    ATE_EC enqueue_device_command_task(const std::string& instance_id, 
                                       std::function<void()> task, 
                                       std::promise<bool>* result_promise = nullptr);
    
private:
    // 内部设备表示
    struct ManagedDevice {
        DeviceConfig config;
        std::unique_ptr<TcpUdpClient> tcp_udp_client;
        std::atomic<ConnectionStatus> connection_status;
        std::atomic<ThreadState> thread_state;
        DeviceStatistics statistics;
        std::vector<std::string> error_log;
        mutable std::mutex device_mutex;
        mutable std::mutex statistics_mutex;    // 保护统计信息
        std::atomic<uint64_t> last_activity_timestamp;
        
        // 全局数据块
        E5000DataBlockV2 global_data;
        mutable std::mutex global_data_mutex;
        
        // 设备专用工作线程
        std::unique_ptr<std::thread> worker_thread;
        std::atomic<bool> thread_stop_requested;
        std::atomic<bool> thread_paused;
        std::condition_variable thread_cv;
        std::mutex thread_mutex;
        
        ManagedDevice(const DeviceConfig& cfg)
            : config(cfg), 
              connection_status(ConnectionStatus::DISCONNECTED),
              thread_state(ThreadState::STOPPED),
              last_activity_timestamp(0),
              thread_stop_requested(false),
              thread_paused(false) {
            statistics = {};
        }
        ~ManagedDevice() = default;
        
        // 禁用拷贝
        ManagedDevice(const ManagedDevice&) = delete;
        ManagedDevice& operator=(const ManagedDevice&) = delete;
        
        // 启用移动
        ManagedDevice(ManagedDevice&& other) noexcept;
        ManagedDevice& operator=(ManagedDevice&& other) noexcept;
    };
    
    
    // 错误处理
    void m_set_last_error(const std::string& error);
    void add_device_error_log(ManagedDevice& device, const std::string& error_message);
    void write_error_log_to_file(const ManagedDevice& device);
    
    // 设备线程管理
    ATE_EC start_device_worker_thread(ManagedDevice& device);
    void stop_device_worker_thread(ManagedDevice& device);
    void device_worker_loop(const std::string& instance_id);
    
    // 成员变量
    std::unique_ptr<ConfigManager> m_config_manager;
    
    // Device storage
    std::unordered_map<std::string, std::unique_ptr<ManagedDevice>> m_devices;
    std::unordered_map<std::string, size_t> m_device_index;
    mutable std::shared_mutex m_devices_shared_mutex;
    mutable std::mutex m_devices_mutex;
    
    // State management
    std::atomic<bool> m_initialized;
    std::atomic<bool> m_shutdown_requested;
    
    // Error handling
    mutable std::mutex m_error_mutex;
    std::string m_last_error;
    
    // Performance settings
    std::atomic<uint32_t> m_connection_timeout_ms;
    std::atomic<uint32_t> m_command_timeout_ms;
    
    // Statistics
    std::atomic<uint32_t> m_total_commands_sent;
    std::atomic<uint32_t> m_total_errors;
    std::atomic<uint32_t> m_total_reconnections;
};


// Factory function
std::unique_ptr<DeviceManager> create_device_manager(std::unique_ptr<ConfigManager> config_manager);

} // namespace EthClient

#endif // DEVICE_MANAGER_HPP