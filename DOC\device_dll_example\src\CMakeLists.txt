# ============================================================================
# 源文件和头文件定义
# ============================================================================

# 库源文件列表（不包含main.cpp）
# 这些是构成EthClient驱动库核心功能的源文件
set(LIB_SOURCES
    tcp_udp_client.cpp     # TCP/UDP通信客户端实现
    config_manager.cpp     # 配置文件管理器实现
    device_manager.cpp     # 设备管理器实现
    eth_client_interface.cpp # DLL接口层实现
)

# 头文件列表
# 包含所有公共接口和数据结构定义
set(HEADERS
    ../include/tcp_udp_client.hpp     # TCP/UDP客户端接口定义
    ../include/config_manager.hpp     # 配置管理器接口定义
    ../include/device_manager.hpp     # 设备管理器接口定义
    ../include/data_structures.hpp    # 数据结构定义（包含E5000DataBlockV2）
    ../include/eth_client_interface.hpp # DLL接口定义（EXP_API导出）
)

# ============================================================================
# 库目标创建
# ============================================================================

# 创建静态库
# 静态库会被链接到最终的可执行文件中，不需要运行时依赖
add_library(EthClient_lib STATIC ${LIB_SOURCES} ${HEADERS})

# 创建共享库（Windows上的DLL）
# 共享库可以被多个程序共享使用，减少内存占用
add_library(EthClient_dll SHARED ${LIB_SOURCES} ${HEADERS})

# ============================================================================
# 库链接配置
# ============================================================================

# 为静态库链接必要的系统库
target_link_libraries(EthClient_lib
    Threads::Threads  # 线程库，用于多线程支持
    ws2_32           # Windows Socket库（TCP/UDP通信需要）
)

# 为共享库（DLL）链接必要的系统库
target_link_libraries(EthClient_dll
    Threads::Threads  # 线程库，用于多线程支持
    ws2_32           # Windows Socket库（TCP/UDP通信需要）
)

# ============================================================================
# 目标属性设置
# ============================================================================

# 设置静态库的属性
set_target_properties(EthClient_lib PROPERTIES
    CXX_STANDARD 17              # 使用C++17标准
    CXX_STANDARD_REQUIRED ON     # 要求必须支持C++17
)

# 设置共享库（DLL）的属性
set_target_properties(EthClient_dll PROPERTIES
    CXX_STANDARD 17              # 使用C++17标准
    CXX_STANDARD_REQUIRED ON     # 要求必须支持C++17
    WINDOWS_EXPORT_ALL_SYMBOLS ON # Windows上自动导出所有符号
    OUTPUT_NAME "KwlEthClient_V01"   # 输出文件名为KwlEthClient_V01.dll
    PREFIX ""                    # 不添加lib前缀
)

# ============================================================================
# 编译定义配置
# ============================================================================

# 为DLL添加编译定义
# EthClient_DLL_EXPORTS: 用于控制DLL符号的导出/导入
target_compile_definitions(EthClient_dll PRIVATE EthClient_DLL_EXPORTS)

# 为静态库添加编译定义
target_compile_definitions(EthClient_lib PRIVATE
    WIN32_LEAN_AND_MEAN  # 减少Windows头文件的包含内容，加快编译速度
    NOMINMAX             # 禁用Windows.h中的min/max宏，避免与std::min/max冲突
)