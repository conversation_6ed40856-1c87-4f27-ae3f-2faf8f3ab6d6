-- ============================================================================
-- V2G协议详细测试序列 (三级子序列) - Step-Based Framework
-- 文件: v2g_protocol_detail.lua
-- 描述: V2G协议详细测试，由bidirectional_charging_test.lua调用，包含pre、seq、post三个标签分区
-- 版本: V2.1 (标准化step-based工步控制框架)
-- ============================================================================

-- 工步控制变量
local step = 0
local run_flag = {1, 1, 1, 1, 1, 1, 1, 1, 0, 0}  -- 前8个工步启用
local test_result = "UNKNOWN"
local error_status = {}

-- 测试数据记录
local test_data_points = {}
local measurement_count = 0

-- 固定功能begin
function add_string(str)
    if not error_status[str] then
        error_status[str] = true
        return true
    end
    return false
end

-- 工步保护状态检查
function seq_protect_check()
    -- 检查V2G通信设备状态
    local comm_status = dll_query_command(main.device_channels.v2g_communication, "STATUS?")
    if comm_status == "ERROR" then
        add_string("V2G通信设备故障")
        return false
    end
    
    -- 检查充电桩控制器状态
    local controller_status = dll_query_command(main.device_channels.charger_controller, "STATUS?")
    if controller_status == "ERROR" then
        add_string("充电桩控制器故障")
        return false
    end
    
    return true
end

-- 工步保护动作执行
function seq_protect_act()
    if (next(error_status) ~= nil) then
        -- 发生故障时的保护动作
        dll_send_command(main.device_channels.v2g_communication, "DISCONNECT")
        dll_send_command(main.device_channels.charger_controller, "STOP")
        
        for error_key, _ in pairs(error_status) do
            log_message("[V2G协议] 执行保护动作: " .. error_key, "ERROR")
        end
        
        -- return true 动作后返回true 则动作了 后面停止
        -- return false 动作了返回false 则表示 后面 继续执行
        return true
    end
    return false
end

-- 自定精确延时函数（毫秒级）
-- @param x 总延时时间（毫秒）
function delayms(x)
    local sleepms = 10  -- 每次循环基础延时
    local loops = math.floor(x / sleepms)  -- 完整循环次数
    local remainder = x % sleepms  -- 剩余延时
    
    -- 执行完整循环延时
    for i = 1, loops do
        -- 休眠循环基础延时 sleep_ms 采用C++注册的方式，注册一个通用的接口给lua
        sleep_ms(sleepms)
        if seq_protect_check() == false then
            return
        end
    end
    
    -- 执行剩余延时
    if remainder > 0 then
        -- 休眠毫秒剩余
        sleep_ms(remainder)
        if seq_protect_check() == false then
            return
        end
    end
end
-- 固定功能end

-- 子序列全局变量定义
v2g_protocol_detail = {
    test_result = "UNKNOWN",
    test_name = "V2G协议详细测试",
    start_time = 0,
    end_time = 0,
    step_count = 0,
    error_messages = {},
    protocol_data = {},
    handshake_results = {},
    authentication_results = {},
    charging_schedule = {},
    communication_log = {}
}

-- 测试参数配置
local v2g_params = {
    protocol_version = "ISO15118-2",               -- 协议版本
    communication_timeout = 30.0,                  -- 通信超时(s)
    handshake_timeout = 10.0,                      -- 握手超时(s)
    authentication_timeout = 15.0,                 -- 认证超时(s)
    charging_profile_timeout = 20.0,               -- 充电配置超时(s)
    max_retry_count = 3,                           -- 最大重试次数
    supported_payment_methods = {"ExternalPayment", "Contract"}, -- 支持的支付方式
    supported_energy_modes = {"AC_single_phase", "AC_three_phase", "DC"}, -- 支持的能量模式
    max_voltage = 1000.0,                          -- 最大电压(V)
    max_current = 400.0,                           -- 最大电流(A)
    max_power = 350000.0,                          -- 最大功率(W)
    min_soc = 20.0,                                -- 最小SOC(%)
    max_soc = 100.0,                               -- 最大SOC(%)
    target_soc = 80.0,                             -- 目标SOC(%)
    departure_time = 3600,                         -- 预计离开时间(s)
    communication_protocol = "PLC",                -- 通信协议(PLC/WiFi)
    security_level = "TLS",                        -- 安全级别
    certificate_validation = true,                 -- 证书验证
    message_validation = true                      -- 消息验证
}

-- V2G消息类型定义
local v2g_messages = {
    -- 会话建立
    SESSION_SETUP_REQ = "SessionSetupReq",
    SESSION_SETUP_RES = "SessionSetupRes",
    
    -- 服务发现
    SERVICE_DISCOVERY_REQ = "ServiceDiscoveryReq",
    SERVICE_DISCOVERY_RES = "ServiceDiscoveryRes",
    
    -- 服务详情
    SERVICE_DETAIL_REQ = "ServiceDetailReq",
    SERVICE_DETAIL_RES = "ServiceDetailRes",
    
    -- 支付选择
    PAYMENT_SERVICE_SELECTION_REQ = "PaymentServiceSelectionReq",
    PAYMENT_SERVICE_SELECTION_RES = "PaymentServiceSelectionRes",
    
    -- 认证
    AUTHORIZATION_REQ = "AuthorizationReq",
    AUTHORIZATION_RES = "AuthorizationRes",
    
    -- 充电参数发现
    CHARGE_PARAMETER_DISCOVERY_REQ = "ChargeParameterDiscoveryReq",
    CHARGE_PARAMETER_DISCOVERY_RES = "ChargeParameterDiscoveryRes",
    
    -- 功率传输准备
    POWER_DELIVERY_REQ = "PowerDeliveryReq",
    POWER_DELIVERY_RES = "PowerDeliveryRes",
    
    -- 充电状态
    CHARGING_STATUS_REQ = "ChargingStatusReq",
    CHARGING_STATUS_RES = "ChargingStatusRes",
    
    -- 会话停止
    SESSION_STOP_REQ = "SessionStopReq",
    SESSION_STOP_RES = "SessionStopRes"
}

-- 工具函数
-- function log_v2g_message(level, message)
--     local timestamp = os.date("%Y-%m-%d %H:%M:%S")
--     print(string.format("[%s] [V2G协议] %s: %s", timestamp, level, message))
-- end

-- JSON报告写入函数
function write_v2g_protocol_json_report()
    local test_duration = v2g_protocol_detail.end_time - v2g_protocol_detail.start_time
    local total_communications = #v2g_protocol_detail.communication_log
    local successful_handshakes = 0
    local successful_authentications = 0
    
    -- 统计握手成功次数
    for _, handshake in ipairs(v2g_protocol_detail.handshake_results) do
        if handshake.result == "SUCCESS" then
            successful_handshakes = successful_handshakes + 1
        end
    end
    
    -- 统计认证成功次数
    for _, auth in ipairs(v2g_protocol_detail.authentication_results) do
        if auth.result == "SUCCESS" then
            successful_authentications = successful_authentications + 1
        end
    end
    
    local json_report = {
        test_name = v2g_protocol_detail.test_name,
        test_result = v2g_protocol_detail.test_result,
        start_time = v2g_protocol_detail.start_time,
        end_time = v2g_protocol_detail.end_time,
        test_duration = test_duration,
        step_count = v2g_protocol_detail.step_count,
        total_communications = total_communications,
        successful_handshakes = successful_handshakes,
        successful_authentications = successful_authentications,
        charging_schedules = #v2g_protocol_detail.charging_schedule,
        error_count = #v2g_protocol_detail.error_messages,
        error_messages = v2g_protocol_detail.error_messages,
        test_parameters = {
            protocol_version = v2g_params.protocol_version,
            communication_timeout = v2g_params.communication_timeout,
            handshake_timeout = v2g_params.handshake_timeout,
            authentication_timeout = v2g_params.authentication_timeout,
            charging_profile_timeout = v2g_params.charging_profile_timeout,
            max_retry_count = v2g_params.max_retry_count,
            supported_payment_methods = v2g_params.supported_payment_methods,
            supported_energy_modes = v2g_params.supported_energy_modes,
            max_voltage = v2g_params.max_voltage,
            max_current = v2g_params.max_current,
            max_power = v2g_params.max_power,
            communication_protocol = v2g_params.communication_protocol,
            security_level = v2g_params.security_level,
            certificate_validation = v2g_params.certificate_validation,
            message_validation = v2g_params.message_validation
        },
        handshake_results = v2g_protocol_detail.handshake_results,
        authentication_results = v2g_protocol_detail.authentication_results,
        charging_schedule = v2g_protocol_detail.charging_schedule,
        communication_log = v2g_protocol_detail.communication_log,
        protocol_data = v2g_protocol_detail.protocol_data
    }
    
    -- 调用C++接口写入JSON报告
    local success = write_test_report_json("v2g_protocol_detail", json_report)
    if success then
        log_message("[V2G协议] JSON测试报告已生成", "INFO")
    else
        log_message("[V2G协议] JSON测试报告生成失败", "ERROR")
    end
end

function add_v2g_error(message)
    table.insert(v2g_protocol_detail.error_messages, message)
    log_message("[V2G协议] " .. message, "ERROR")
end

function log_communication(direction, message_type, content)
    local comm_entry = {
        timestamp = os.time(),
        direction = direction,  -- "TX" 或 "RX"
        message_type = message_type,
        content = content,
        success = true
    }
    table.insert(v2g_protocol_detail.communication_log, comm_entry)
    log_message(string.format("[V2G协议] %s %s: %s", direction, message_type, content), "INFO")
end

function send_v2g_message(message_type, payload)
    local message = {
        type = message_type,
        timestamp = os.time(),
        payload = payload
    }
    
    -- 发送消息到V2G通信模块
    local message_json = string.format('{"type":"%s","payload":%s}', message_type, payload)
    local result = dll_send_command(main.device_channels.v2g_communication, message_json)
    
    log_communication("TX", message_type, payload)
    
    if result == "OK" then
        return true
    else
        add_v2g_error(string.format("发送V2G消息失败: %s", message_type))
        return false
    end
end

function receive_v2g_message(expected_type, timeout)
    local start_time = os.clock()
    
    while (os.clock() - start_time) < timeout do
        local response = dll_query_command(main.device_channels.v2g_communication, "GET_MESSAGE")
        
        if response and response ~= "" and response ~= "TIMEOUT" then
            -- 解析响应
            local message_start = string.find(response, '{')
            if message_start then
                local message_json = string.sub(response, message_start)
                
                -- 简单的JSON解析（实际应用中应使用专业的JSON库）
                local type_start = string.find(message_json, '"type":"')
                if type_start then
                    local type_end = string.find(message_json, '"', type_start + 8)
                    if type_end then
                        local message_type = string.sub(message_json, type_start + 8, type_end - 1)
                        
                        log_communication("RX", message_type, message_json)
                        
                        if message_type == expected_type then
                            return message_json
                        else
                            log_message(string.format("[V2G协议] 收到意外消息类型: %s (期望: %s)", message_type, expected_type), "WARN")
                        end
                    end
                end
            end
        end
        
        os.execute("timeout /t 1 /nobreak > nul")
    end
    
    add_v2g_error(string.format("接收V2G消息超时: %s", expected_type))
    return nil
end

function perform_session_setup()
    log_message("[V2G协议] 执行会话建立...", "INFO")
    
    local setup_payload = string.format('{
        "EVCCID":"%s",
        "ProtocolVersion":"%s",
        "SupportedAppProtocols":["%s"]
    }', "EV123456789", v2g_params.protocol_version, v2g_params.protocol_version)
    
    if not send_v2g_message(v2g_messages.SESSION_SETUP_REQ, setup_payload) then
        return false
    end
    
    local response = receive_v2g_message(v2g_messages.SESSION_SETUP_RES, v2g_params.handshake_timeout)
    if not response then
        return false
    end
    
    -- 解析响应状态
    local response_code_start = string.find(response, '"ResponseCode":"')
    if response_code_start then
        local response_code_end = string.find(response, '"', response_code_start + 16)
        if response_code_end then
            local response_code = string.sub(response, response_code_start + 16, response_code_end - 1)
            
            if response_code == "OK" then
                log_message("[V2G协议] 会话建立成功", "INFO")
                table.insert(v2g_protocol_detail.handshake_results, {
                    step = "SessionSetup",
                    result = "SUCCESS",
                    response_code = response_code
                })
                return true
            else
                add_v2g_error(string.format("会话建立失败: %s", response_code))
                table.insert(v2g_protocol_detail.handshake_results, {
                    step = "SessionSetup",
                    result = "FAILED",
                    response_code = response_code
                })
                return false
            end
        end
    end
    
    add_v2g_error("会话建立响应解析失败")
    return false
end

function perform_service_discovery()
    log_message("[V2G协议] 执行服务发现...", "INFO")
    
    local discovery_payload = '{
        "ServiceCategory":"EVCharging",
        "ServiceScope":"Internet"
    }'
    
    if not send_v2g_message(v2g_messages.SERVICE_DISCOVERY_REQ, discovery_payload) then
        return false
    end
    
    local response = receive_v2g_message(v2g_messages.SERVICE_DISCOVERY_RES, v2g_params.communication_timeout)
    if not response then
        return false
    end
    
    -- 检查是否包含充电服务
    if string.find(response, "EVCharging") then
        log_message("[V2G协议] 服务发现成功，找到充电服务", "INFO")
        table.insert(v2g_protocol_detail.handshake_results, {
            step = "ServiceDiscovery",
            result = "SUCCESS",
            services_found = {"EVCharging"}
        })
        return true
    else
        add_v2g_error("服务发现失败，未找到充电服务")
        table.insert(v2g_protocol_detail.handshake_results, {
            step = "ServiceDiscovery",
            result = "FAILED",
            services_found = {}
        })
        return false
    end
end

function perform_payment_selection()
    log_message("[V2G协议] 执行支付方式选择...", "INFO")
    
    local payment_payload = string.format('{
        "SelectedPaymentOption":"%s",
        "SelectedServiceList":["EVCharging"]
    }', v2g_params.supported_payment_methods[1])
    
    if not send_v2g_message(v2g_messages.PAYMENT_SERVICE_SELECTION_REQ, payment_payload) then
        return false
    end
    
    local response = receive_v2g_message(v2g_messages.PAYMENT_SERVICE_SELECTION_RES, v2g_params.communication_timeout)
    if not response then
        return false
    end
    
    if string.find(response, '"ResponseCode":"OK"') then
        log_message("[V2G协议] 支付方式选择成功", "INFO")
        return true
    else
        add_v2g_error("支付方式选择失败")
        return false
    end
end

function perform_authorization()
    log_message("[V2G协议] 执行认证...", "INFO")
    
    local auth_payload = '{
        "IdTokenType":"ISO14443",
        "IdToken":"1234567890ABCDEF",
        "GenChallenge":"ChallengePlaceholder"
    }'
    
    if not send_v2g_message(v2g_messages.AUTHORIZATION_REQ, auth_payload) then
        return false
    end
    
    local response = receive_v2g_message(v2g_messages.AUTHORIZATION_RES, v2g_params.authentication_timeout)
    if not response then
        return false
    end
    
    if string.find(response, '"ResponseCode":"OK"') then
        log_message("[V2G协议] 认证成功", "INFO")
        table.insert(v2g_protocol_detail.authentication_results, {
            method = "ISO14443",
            result = "SUCCESS",
            timestamp = os.time()
        })
        return true
    else
        add_v2g_error("认证失败")
        table.insert(v2g_protocol_detail.authentication_results, {
            method = "ISO14443",
            result = "FAILED",
            timestamp = os.time()
        })
        return false
    end
end

function perform_charge_parameter_discovery()
    log_message("[V2G协议] 执行充电参数发现...", "INFO")
    
    local param_payload = string.format('{
        "MaxEntriesSAScheduleTuple":1024,
        "RequestedEnergyTransferMode":"%s",
        "EVChargeParameter":{
            "DepartureTime":%d,
            "EVTargetEnergyRequest":%.1f,
            "EVMaximumEnergyRequest":%.1f,
            "EVMinimumEnergyRequest":%.1f
        }
    }', v2g_params.supported_energy_modes[1], v2g_params.departure_time, 
        v2g_params.target_soc, v2g_params.max_soc, v2g_params.min_soc)
    
    if not send_v2g_message(v2g_messages.CHARGE_PARAMETER_DISCOVERY_REQ, param_payload) then
        return false
    end
    
    local response = receive_v2g_message(v2g_messages.CHARGE_PARAMETER_DISCOVERY_RES, v2g_params.charging_profile_timeout)
    if not response then
        return false
    end
    
    if string.find(response, '"ResponseCode":"OK"') then
        log_message("[V2G协议] 充电参数发现成功", "INFO")
        
        -- 解析充电调度
        local schedule_entry = {
            start_time = os.time(),
            duration = v2g_params.departure_time,
            max_power = v2g_params.max_power,
            max_voltage = v2g_params.max_voltage,
            max_current = v2g_params.max_current
        }
        table.insert(v2g_protocol_detail.charging_schedule, schedule_entry)
        
        return true
    else
        add_v2g_error("充电参数发现失败")
        return false
    end
end

function perform_power_delivery_start()
    log_message("[V2G协议] 执行功率传输开始...", "INFO")
    
    local power_payload = '{
        "ChargeProgress":"Start",
        "SAScheduleTupleID":1,
        "ChargingProfile":{
            "ProfileEntries":[
                {
                    "ChargingRateUnit":"W",
                    "StartPeriod":0,
                    "PowerMax":50000
                }
            ]
        }
    }'
    
    if not send_v2g_message(v2g_messages.POWER_DELIVERY_REQ, power_payload) then
        return false
    end
    
    local response = receive_v2g_message(v2g_messages.POWER_DELIVERY_RES, v2g_params.communication_timeout)
    if not response then
        return false
    end
    
    if string.find(response, '"ResponseCode":"OK"') then
        log_message("[V2G协议] 功率传输开始成功", "INFO")
        return true
    else
        add_v2g_error("功率传输开始失败")
        return false
    end
end

function monitor_charging_status(duration)
    log_message(string.format("[V2G协议] 监控充电状态 %d 秒...", duration), "INFO")
    
    local start_time = os.clock()
    local status_count = 0
    
    while (os.clock() - start_time) < duration do
        local status_payload = '{
            "EVTargetVoltage":400.0,
            "EVTargetCurrent":125.0,
            "EVChargingComplete":false
        }'
        
        if send_v2g_message(v2g_messages.CHARGING_STATUS_REQ, status_payload) then
            local response = receive_v2g_message(v2g_messages.CHARGING_STATUS_RES, 5.0)
            
            if response then
                status_count = status_count + 1
                
                -- 解析充电状态
                if string.find(response, '"ResponseCode":"OK"') then
                    log_message(string.format("[V2G协议] 充电状态查询成功 (%d)", status_count), "INFO")
                    
                    -- 记录状态数据
                    local status_data = {
                        timestamp = os.time(),
                        status_count = status_count,
                        response_ok = true
                    }
                    table.insert(v2g_protocol_detail.protocol_data, status_data)
                else
                    add_v2g_error(string.format("充电状态查询失败 (%d)", status_count))
                end
            end
        end
        
        os.execute("timeout /t 5 /nobreak > nul")
    end
    
    log_message(string.format("[V2G协议] 充电状态监控完成，共查询 %d 次", status_count), "INFO")
    return status_count > 0
end

function perform_power_delivery_stop()
    log_message("[V2G协议] 执行功率传输停止...", "INFO")
    
    local stop_payload = '{
        "ChargeProgress":"Stop",
        "SAScheduleTupleID":1
    }'
    
    if not send_v2g_message(v2g_messages.POWER_DELIVERY_REQ, stop_payload) then
        return false
    end
    
    local response = receive_v2g_message(v2g_messages.POWER_DELIVERY_RES, v2g_params.communication_timeout)
    if not response then
        return false
    end
    
    if string.find(response, '"ResponseCode":"OK"') then
        log_message("[V2G协议] 功率传输停止成功", "INFO")
        return true
    else
        add_v2g_error("功率传输停止失败")
        return false
    end
end

function perform_session_stop()
    log_message("[V2G协议] 执行会话停止...", "INFO")
    
    local stop_payload = '{
        "ChargingSession":"Terminate"
    }'
    
    if not send_v2g_message(v2g_messages.SESSION_STOP_REQ, stop_payload) then
        return false
    end
    
    local response = receive_v2g_message(v2g_messages.SESSION_STOP_RES, v2g_params.communication_timeout)
    if not response then
        return false
    end
    
    if string.find(response, '"ResponseCode":"OK"') then
        log_message("[V2G协议] 会话停止成功", "INFO")
        return true
    else
        add_v2g_error("会话停止失败")
        return false
    end
end

-- ============================================================================
-- PRE 标签 - 预处理阶段
-- ============================================================================
::pre::
log_message("[V2G协议] === 开始V2G协议详细测试预处理阶段 ===", "INFO")
v2g_protocol_detail.start_time = os.time()
v2g_protocol_detail.step_count = 0
v2g_protocol_detail.error_messages = {}
v2g_protocol_detail.protocol_data = {}
v2g_protocol_detail.handshake_results = {}
v2g_protocol_detail.authentication_results = {}
v2g_protocol_detail.charging_schedule = {}
v2g_protocol_detail.communication_log = {}

-- 检查设备状态
if not device_initialized then
    add_v2g_error("设备未初始化，无法执行V2G协议测试")
    v2g_protocol_detail.test_result = "FAILED"
    goto post
end

-- 检查V2G通信模块
local v2g_status = dll_query_command(main.device_channels.v2g_communication, "STATUS?")
if v2g_status == "ERROR" then
    add_v2g_error("V2G通信模块不可用")
    v2g_protocol_detail.test_result = "FAILED"
    goto post
end

-- 检查PLC通信
local plc_status = dll_query_command(main.device_channels.plc_modem, "STATUS?")
if plc_status == "ERROR" then
    add_v2g_error("PLC调制解调器不可用")
    v2g_protocol_detail.test_result = "FAILED"
    goto post
end

-- 初始化V2G通信模块
log_message("[V2G协议] 初始化V2G通信模块...", "INFO")
dll_send_command(main.device_channels.v2g_communication, "RESET")
dll_send_command(main.device_channels.v2g_communication, string.format("PROTOCOL:%s", v2g_params.protocol_version))
dll_send_command(main.device_channels.v2g_communication, string.format("TIMEOUT:%d", math.floor(v2g_params.communication_timeout)))
dll_send_command(main.device_channels.v2g_communication, string.format("SECURITY:%s", v2g_params.security_level))

if v2g_params.certificate_validation then
    dll_send_command(main.device_channels.v2g_communication, "CERT_VALIDATION:ON")
else
    dll_send_command(main.device_channels.v2g_communication, "CERT_VALIDATION:OFF")
end

if v2g_params.message_validation then
    dll_send_command(main.device_channels.v2g_communication, "MSG_VALIDATION:ON")
else
    dll_send_command(main.device_channels.v2g_communication, "MSG_VALIDATION:OFF")
end

-- 初始化PLC调制解调器
log_message("[V2G协议] 初始化PLC调制解调器...", "INFO")
dll_send_command(main.device_channels.plc_modem, "RESET")
dll_send_command(main.device_channels.plc_modem, "MODE:EVSE")  -- 设置为EVSE模式
dll_send_command(main.device_channels.plc_modem, "FREQUENCY:AUTO")
dll_send_command(main.device_channels.plc_modem, "POWER:HIGH")

-- 启动通信模块
dll_send_command(main.device_channels.v2g_communication, "START")
dll_send_command(main.device_channels.plc_modem, "START")

-- 等待模块就绪
log_message("[V2G协议] 等待通信模块就绪...", "INFO")
local ready_timeout = 30.0
local ready_start_time = os.clock()

while (os.clock() - ready_start_time) < ready_timeout do
    local v2g_ready = dll_query_command(main.device_channels.v2g_communication, "READY?")
    local plc_ready = dll_query_command(main.device_channels.plc_modem, "READY?")
    
    if v2g_ready == "YES" and plc_ready == "YES" then
        log_message("[V2G协议] 通信模块就绪", "INFO")
        break
    end
    
    os.execute("timeout /t 2 /nobreak > nul")
end

-- 最终检查模块状态
local final_v2g_ready = dll_query_command(main.device_channels.v2g_communication, "READY?")
local final_plc_ready = dll_query_command(main.device_channels.plc_modem, "READY?")

if final_v2g_ready ~= "YES" then
    add_v2g_error("V2G通信模块未就绪")
    v2g_protocol_detail.test_result = "FAILED"
    goto post
end

if final_plc_ready ~= "YES" then
    add_v2g_error("PLC调制解调器未就绪")
    v2g_protocol_detail.test_result = "FAILED"
    goto post
end

log_message("[V2G协议] === V2G协议详细测试预处理阶段完成 ===", "INFO")

-- ============================================================================
-- SEQ 标签 - 测试序列阶段
-- ============================================================================
::seq::
log_message("[V2G协议] === 开始V2G协议详细测试序列阶段 ===", "INFO")

-- 步骤1: 会话建立
v2g_protocol_detail.step_count = v2g_protocol_detail.step_count + 1
log_message(string.format("[V2G协议] 步骤 %d: 会话建立", v2g_protocol_detail.step_count), "INFO")

if not perform_session_setup() then
    add_v2g_error("会话建立失败")
    goto seq_failed
end

-- 步骤2: 服务发现
v2g_protocol_detail.step_count = v2g_protocol_detail.step_count + 1
log_message(string.format("[V2G协议] 步骤 %d: 服务发现", v2g_protocol_detail.step_count), "INFO")

if not perform_service_discovery() then
    add_v2g_error("服务发现失败")
    goto seq_failed
end

-- 步骤3: 支付方式选择
v2g_protocol_detail.step_count = v2g_protocol_detail.step_count + 1
log_message(string.format("[V2G协议] 步骤 %d: 支付方式选择", v2g_protocol_detail.step_count), "INFO")

if not perform_payment_selection() then
    add_v2g_error("支付方式选择失败")
    goto seq_failed
end

-- 步骤4: 认证
v2g_protocol_detail.step_count = v2g_protocol_detail.step_count + 1
log_message(string.format("[V2G协议] 步骤 %d: 认证", v2g_protocol_detail.step_count), "INFO")

if not perform_authorization() then
    add_v2g_error("认证失败")
    goto seq_failed
end

-- 步骤5: 充电参数发现
v2g_protocol_detail.step_count = v2g_protocol_detail.step_count + 1
log_message(string.format("[V2G协议] 步骤 %d: 充电参数发现", v2g_protocol_detail.step_count), "INFO")

if not perform_charge_parameter_discovery() then
    add_v2g_error("充电参数发现失败")
    goto seq_failed
end

-- 步骤6: 功率传输开始
v2g_protocol_detail.step_count = v2g_protocol_detail.step_count + 1
log_message(string.format("[V2G协议] 步骤 %d: 功率传输开始", v2g_protocol_detail.step_count), "INFO")

if not perform_power_delivery_start() then
    add_v2g_error("功率传输开始失败")
    goto seq_failed
end

-- 步骤7: 充电状态监控
v2g_protocol_detail.step_count = v2g_protocol_detail.step_count + 1
log_message(string.format("[V2G协议] 步骤 %d: 充电状态监控", v2g_protocol_detail.step_count), "INFO")

local monitoring_duration = 60  -- 监控60秒
if not monitor_charging_status(monitoring_duration) then
    add_v2g_error("充电状态监控失败")
    goto seq_failed
end

-- 步骤8: 功率传输停止
v2g_protocol_detail.step_count = v2g_protocol_detail.step_count + 1
log_message(string.format("[V2G协议] 步骤 %d: 功率传输停止", v2g_protocol_detail.step_count), "INFO")

if not perform_power_delivery_stop() then
    add_v2g_error("功率传输停止失败")
    goto seq_failed
end

-- 步骤9: 会话停止
v2g_protocol_detail.step_count = v2g_protocol_detail.step_count + 1
log_message(string.format("[V2G协议] 步骤 %d: 会话停止", v2g_protocol_detail.step_count), "INFO")

if not perform_session_stop() then
    add_v2g_error("会话停止失败")
    goto seq_failed
end

-- 所有步骤成功完成
log_message("[V2G协议] V2G协议详细测试序列阶段通过", "INFO")
goto seq_completed

::seq_failed::
log_message("[V2G协议] V2G协议详细测试序列阶段失败", "ERROR")

::seq_completed::
log_message("[V2G协议] === V2G协议详细测试序列阶段完成 ===", "INFO")

-- ============================================================================
-- POST 标签 - 后处理阶段
-- ============================================================================
::post::
log_message("[V2G协议] === 开始V2G协议详细测试后处理阶段 ===", "INFO")
v2g_protocol_detail.end_time = os.time()

-- 停止通信模块
dll_send_command(main.device_channels.v2g_communication, "STOP")
dll_send_command(main.device_channels.plc_modem, "STOP")
log_message("[V2G协议] 通信模块已停止", "INFO")

-- 生成测试报告
local test_duration = v2g_protocol_detail.end_time - v2g_protocol_detail.start_time
local total_communications = #v2g_protocol_detail.communication_log
local successful_handshakes = 0
local successful_authentications = 0

-- 统计握手成功次数
for _, handshake in ipairs(v2g_protocol_detail.handshake_results) do
    if handshake.result == "SUCCESS" then
        successful_handshakes = successful_handshakes + 1
    end
end

-- 统计认证成功次数
for _, auth in ipairs(v2g_protocol_detail.authentication_results) do
    if auth.result == "SUCCESS" then
        successful_authentications = successful_authentications + 1
    end
end

-- 确定最终测试结果
if #v2g_protocol_detail.error_messages == 0 and 
   v2g_protocol_detail.step_count >= 9 and
   successful_handshakes > 0 and
   successful_authentications > 0 then
    v2g_protocol_detail.test_result = "PASS"
else
    v2g_protocol_detail.test_result = "FAILED"
end

-- 输出测试结果
print("\n" .. string.rep("-", 70))
print("V2G协议详细测试结果报告")
print(string.rep("-", 70))
print(string.format("测试名称: %s", v2g_protocol_detail.test_name))
print(string.format("测试结果: %s", v2g_protocol_detail.test_result))
print(string.format("协议版本: %s", v2g_params.protocol_version))
print(string.format("测试时长: %d 秒 (%.1f 分钟)", test_duration, test_duration / 60.0))
print(string.format("测试步骤: %d", v2g_protocol_detail.step_count))
print(string.format("通信次数: %d", total_communications))
print(string.format("握手成功: %d", successful_handshakes))
print(string.format("认证成功: %d", successful_authentications))
print(string.format("充电调度: %d 个", #v2g_protocol_detail.charging_schedule))
print(string.format("错误数量: %d", #v2g_protocol_detail.error_messages))

if #v2g_protocol_detail.error_messages > 0 then
    print("错误信息:")
    for i, error_msg in ipairs(v2g_protocol_detail.error_messages) do
        print(string.format("  %d. %s", i, error_msg))
    end
end

-- 输出握手结果
if #v2g_protocol_detail.handshake_results > 0 then
    print("\n握手结果:")
    for _, handshake in ipairs(v2g_protocol_detail.handshake_results) do
        print(string.format("  %s: %s", handshake.step, handshake.result))
    end
end

-- 输出认证结果
if #v2g_protocol_detail.authentication_results > 0 then
    print("\n认证结果:")
    for _, auth in ipairs(v2g_protocol_detail.authentication_results) do
        print(string.format("  方法: %s, 结果: %s", auth.method, auth.result))
    end
end

-- 输出充电调度
if #v2g_protocol_detail.charging_schedule > 0 then
    print("\n充电调度:")
    for i, schedule in ipairs(v2g_protocol_detail.charging_schedule) do
        print(string.format("  调度 %d: 时长=%ds, 最大功率=%.0fW, 最大电压=%.0fV, 最大电流=%.0fA", 
              i, schedule.duration, schedule.max_power, schedule.max_voltage, schedule.max_current))
    end
end

print(string.rep("-", 70))

-- 写入JSON报告
write_v2g_protocol_json_report()

log_message("[V2G协议] === V2G协议详细测试后处理阶段完成 ===", "INFO")
log_message(string.format("[V2G协议] V2G协议详细测试最终结果: %s", v2g_protocol_detail.test_result), "INFO")

-- 返回测试结果
return v2g_protocol_detail.test_result