# Clangd configuration file
# 禁用所有诊断和波浪线提示

Diagnostics:
  # 抑制所有诊断
  Suppress: "*"
  # 禁用clang-tidy检查
  ClangTidy:
    Add: []
    Remove: "*"
  # 禁用未使用的包含文件警告
  UnusedIncludes: None
  # 禁用预处理器警告
  MissingIncludes: None

# 编译器标志配置
CompileFlags:
  Add:
    # 增加错误限制数量
    - "-ferror-limit=0"
    # 禁用所有警告
    - "-w"
    # 忽略未知警告选项
    - "-Wno-unknown-warning-option"

# 索引配置
Index:
  # 禁用背景索引以减少资源使用
  Background: Build

# 完成配置
Completion:
  # 禁用所有片段
  AllScopes: false

# 悬停信息配置
Hover:
  # 显示简化的悬停信息
  ShowAKA: false

InlayHints:
  Enabled: true
  ParameterNames: false
  DeducedTypes: true