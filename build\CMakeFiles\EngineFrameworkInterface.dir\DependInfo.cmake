
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "E:/KWL-Software/EVT/EngineFrameworkInterface/src/data_process.cpp" "CMakeFiles/EngineFrameworkInterface.dir/src/data_process.cpp.obj" "gcc" "CMakeFiles/EngineFrameworkInterface.dir/src/data_process.cpp.obj.d"
  "E:/KWL-Software/EVT/EngineFrameworkInterface/src/data_recorder.cpp" "CMakeFiles/EngineFrameworkInterface.dir/src/data_recorder.cpp.obj" "gcc" "CMakeFiles/EngineFrameworkInterface.dir/src/data_recorder.cpp.obj.d"
  "E:/KWL-Software/EVT/EngineFrameworkInterface/src/device_manager.cpp" "CMakeFiles/EngineFrameworkInterface.dir/src/device_manager.cpp.obj" "gcc" "CMakeFiles/EngineFrameworkInterface.dir/src/device_manager.cpp.obj.d"
  "E:/KWL-Software/EVT/EngineFrameworkInterface/src/engine_core.cpp" "CMakeFiles/EngineFrameworkInterface.dir/src/engine_core.cpp.obj" "gcc" "CMakeFiles/EngineFrameworkInterface.dir/src/engine_core.cpp.obj.d"
  "E:/KWL-Software/EVT/EngineFrameworkInterface/src/engine_framework_interface.cpp" "CMakeFiles/EngineFrameworkInterface.dir/src/engine_framework_interface.cpp.obj" "gcc" "CMakeFiles/EngineFrameworkInterface.dir/src/engine_framework_interface.cpp.obj.d"
  "E:/KWL-Software/EVT/EngineFrameworkInterface/src/engine_manager.cpp" "CMakeFiles/EngineFrameworkInterface.dir/src/engine_manager.cpp.obj" "gcc" "CMakeFiles/EngineFrameworkInterface.dir/src/engine_manager.cpp.obj.d"
  "E:/KWL-Software/EVT/EngineFrameworkInterface/src/engine_variable_system.cpp" "CMakeFiles/EngineFrameworkInterface.dir/src/engine_variable_system.cpp.obj" "gcc" "CMakeFiles/EngineFrameworkInterface.dir/src/engine_variable_system.cpp.obj.d"
  "E:/KWL-Software/EVT/EngineFrameworkInterface/src/global_core.cpp" "CMakeFiles/EngineFrameworkInterface.dir/src/global_core.cpp.obj" "gcc" "CMakeFiles/EngineFrameworkInterface.dir/src/global_core.cpp.obj.d"
  "E:/KWL-Software/EVT/EngineFrameworkInterface/src/global_variable_service.cpp" "CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_service.cpp.obj" "gcc" "CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_service.cpp.obj.d"
  "E:/KWL-Software/EVT/EngineFrameworkInterface/src/global_variable_task.cpp" "CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_task.cpp.obj" "gcc" "CMakeFiles/EngineFrameworkInterface.dir/src/global_variable_task.cpp.obj.d"
  "E:/KWL-Software/EVT/EngineFrameworkInterface/src/logger.cpp" "CMakeFiles/EngineFrameworkInterface.dir/src/logger.cpp.obj" "gcc" "CMakeFiles/EngineFrameworkInterface.dir/src/logger.cpp.obj.d"
  "E:/KWL-Software/EVT/EngineFrameworkInterface/src/report_process.cpp" "CMakeFiles/EngineFrameworkInterface.dir/src/report_process.cpp.obj" "gcc" "CMakeFiles/EngineFrameworkInterface.dir/src/report_process.cpp.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
