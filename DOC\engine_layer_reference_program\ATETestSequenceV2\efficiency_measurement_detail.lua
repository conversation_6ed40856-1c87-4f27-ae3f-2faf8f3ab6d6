-- ============================================================================
-- 效率测量详细测试序列 (三级子测试序列) - Step-Based Framework
-- 由charging_efficiency_test.lua调用，进行详细的效率测量
-- 版本: V2.1 (标准化step-based工步控制框架)
-- ============================================================================

-- 工步控制变量
local step = 0
local run_flag = {1, 1, 1, 1, 1, 1, 0, 0, 0, 0}  -- 前6个工步启用
local test_result = "UNKNOWN"
local error_status = {}

-- 测试数据记录
local test_data_points = {}
local measurement_count = 0

-- 固定功能begin
function add_string(str)
    if not error_status[str] then
        error_status[str] = true
        return true
    end
    return false
end

-- 工步保护状态检查
function seq_protect_check()
    -- 检查功率分析仪状态
    local analyzer_input_status = dll_query_command(main.device_channels.power_analyzer_input, "STATUS?")
    if analyzer_input_status == "ERROR" then
        add_string("输入侧功率分析仪故障")
        return false
    end
    
    local analyzer_output_status = dll_query_command(main.device_channels.power_analyzer_output, "STATUS?")
    if analyzer_output_status == "ERROR" then
        add_string("输出侧功率分析仪故障")
        return false
    end
    
    -- 检查电源设备状态
    local power_supply_status = dll_query_command(main.device_channels.power_supply, "STATUS?")
    if power_supply_status == "ERROR" then
        add_string("电源设备故障")
        return false
    end
    
    return true
end

-- 工步保护动作执行
function seq_protect_act()
    -- 发生故障时的保护动作
    dll_send_command(main.device_channels.power_supply, "OUTPUT:OFF")
    dll_send_command(main.device_channels.power_analyzer_input, "STOP")
    dll_send_command(main.device_channels.power_analyzer_output, "STOP")
    
    for error_key, _ in pairs(error_status) do
        log_message("ERROR", "[效率测量] 执行保护动作: " .. error_key)
    end
    
    -- return true 动作后返回true 则动作了 后面停止
    -- return false 动作了返回false 则表示 后面 继续执行
    return true
end

-- 自定精确延时函数（毫秒级）
-- @param x 总延时时间（毫秒）
function delayms(x)
    local sleepms = 10  -- 每次循环基础延时
    local loops = math.floor(x / sleepms)  -- 完整循环次数
    local remainder = x % sleepms  -- 剩余延时
    
    -- 执行完整循环延时
    for i = 1, loops do
        -- 休眠循环基础延时 sleep_ms 采用C++注册的方式，注册一个通用的接口给lua
        sleep_ms(sleepms)
        if seq_protect_check() == false then
            return
        end
    end
    
    -- 执行剩余延时
    if remainder > 0 then
        -- 休眠毫秒剩余
        sleep_ms(remainder)
        if seq_protect_check() == false then
            return
        end
    end
end
-- 固定功能end

-- 全局变量定义 (三级子序列变量，可被上级序列访问)
efficiency_measurement_detail = {
    test_name = "效率测量详细测试",
    test_result = "UNKNOWN",
    start_time = 0,
    end_time = 0,
    step_count = 0,
    error_messages = {},
    measurement_data = {},
    efficiency_points = {},
    power_loss_analysis = {},
    thermal_data = {},
    -- 供上级序列访问的测量结果
    avg_input_power = 0.0,
    avg_output_power = 0.0,
    calculated_efficiency = 0.0
}

-- 测试参数配置
local measurement_params = {
    voltage_points = {380.0, 400.0, 420.0},    -- 电压测试点(V)
    current_points = {10.0, 25.0, 50.0, 75.0, 100.0, 125.0}, -- 电流测试点(A)
    measurement_duration = 30.0,               -- 每点测量时长(s)
    stabilization_time = 10.0,                 -- 稳定时间(s)
    measurement_interval = 1.0,                -- 测量间隔(s)
    voltage_tolerance = 0.01,                  -- 电压容差(1%)
    current_tolerance = 0.02,                  -- 电流容差(2%)
    power_tolerance = 0.03,                    -- 功率容差(3%)
    min_efficiency = 85.0,                     -- 最小效率要求(%)
    max_temperature_rise = 40.0,               -- 最大温升(°C)
    ambient_temperature = 25.0,                -- 环境温度(°C)
    power_factor_min = 0.95,                   -- 最小功率因数
    measurement_accuracy = 0.1,                -- 测量精度(%)
    calibration_points = 3                     -- 校准点数量
}

-- 工具函数
-- function log_efficiency_message(level, message)
--     local timestamp = os.date("%Y-%m-%d %H:%M:%S")
--     print(string.format("[%s] [效率测量] %s: %s", timestamp, level, message))
-- end

function add_efficiency_error(message)
    table.insert(efficiency_measurement_detail.error_messages, message)
    log_message("ERROR", "[效率测量] " .. message)
end

function calculate_efficiency(input_power, output_power)
    if input_power <= 0 then
        return 0.0
    end
    return (output_power / input_power) * 100.0
end

function calculate_power_loss(input_power, output_power)
    return input_power - output_power
end

function measure_power_parameters(measurement_point)
    local measurements = {
        input_voltage = 0.0,
        input_current = 0.0,
        input_power = 0.0,
        input_power_factor = 0.0,
        output_voltage = 0.0,
        output_current = 0.0,
        output_power = 0.0,
        efficiency = 0.0,
        power_loss = 0.0,
        temperature = 0.0,
        valid = false
    }
    
    -- 读取输入侧参数
    local input_v_str = dll_query_command(main.device_channels.power_analyzer_input, "VOLTAGE?")
    local input_i_str = dll_query_command(main.device_channels.power_analyzer_input, "CURRENT?")
    local input_p_str = dll_query_command(main.device_channels.power_analyzer_input, "POWER?")
    local input_pf_str = dll_query_command(main.device_channels.power_analyzer_input, "POWER:FACTOR?")
    
    measurements.input_voltage = tonumber(input_v_str) or 0.0
    measurements.input_current = tonumber(input_i_str) or 0.0
    measurements.input_power = tonumber(input_p_str) or 0.0
    measurements.input_power_factor = tonumber(input_pf_str) or 0.0
    
    -- 读取输出侧参数
    local output_v_str = dll_query_command(main.device_channels.power_analyzer_output, "VOLTAGE?")
    local output_i_str = dll_query_command(main.device_channels.power_analyzer_output, "CURRENT?")
    local output_p_str = dll_query_command(main.device_channels.power_analyzer_output, "POWER?")
    
    measurements.output_voltage = tonumber(output_v_str) or 0.0
    measurements.output_current = tonumber(output_i_str) or 0.0
    measurements.output_power = tonumber(output_p_str) or 0.0
    
    -- 读取温度
    local temp_str = dll_query_command(main.device_channels.thermal_sensor, "TEMPERATURE?")
    measurements.temperature = tonumber(temp_str) or measurement_params.ambient_temperature
    
    -- 计算效率和功率损耗
    measurements.efficiency = calculate_efficiency(measurements.input_power, measurements.output_power)
    measurements.power_loss = calculate_power_loss(measurements.input_power, measurements.output_power)
    
    -- 验证测量有效性
    local voltage_error = math.abs(measurements.output_voltage - measurement_point.voltage) / measurement_point.voltage
    local current_error = math.abs(measurements.output_current - measurement_point.current) / measurement_point.current
    
    measurements.valid = (voltage_error <= measurement_params.voltage_tolerance) and 
                       (current_error <= measurement_params.current_tolerance) and
                       (measurements.input_power > 0) and
                       (measurements.output_power > 0)
    
    return measurements
end

-- JSON报告写入函数
function write_efficiency_measurement_json_report()
    local json_report = {
        test_name = efficiency_measurement_detail.test_name,
        test_result = efficiency_measurement_detail.test_result,
        test_duration = efficiency_measurement_detail.test_duration,
        step_count = efficiency_measurement_detail.step_count,
        measurement_count = #efficiency_measurement_detail.measurement_data,
        avg_input_power = efficiency_measurement_detail.avg_input_power,
        avg_output_power = efficiency_measurement_detail.avg_output_power,
        calculated_efficiency = efficiency_measurement_detail.calculated_efficiency,
        error_count = #efficiency_measurement_detail.error_messages,
        error_messages = efficiency_measurement_detail.error_messages,
        measurement_data = efficiency_measurement_detail.measurement_data,
        efficiency_points = efficiency_measurement_detail.efficiency_points,
        power_loss_analysis = efficiency_measurement_detail.power_loss_analysis,
        thermal_data = efficiency_measurement_detail.thermal_data,
        timestamp = os.date("%Y-%m-%d %H:%M:%S")
    }
    
    -- 调用C++接口写入JSON报告
    local success = write_test_report_json("efficiency_measurement_detail", json_report)
    if success then
        log_message("INFO", "[效率测量] JSON测试报告已生成")
    else
        log_message("ERROR", "[效率测量] JSON测试报告生成失败")
    end
end

function perform_calibration()
    log_message("INFO", "[效率测量] 执行功率分析仪校准...")
    
    -- 校准输入侧功率分析仪
    dll_send_command(main.device_channels.power_analyzer_input, "CALIBRATION:START")
    local cal_result_input = dll_query_command(main.device_channels.power_analyzer_input, "CALIBRATION:RESULT?")
    
    if cal_result_input ~= "PASS" then
        add_efficiency_error("输入侧功率分析仪校准失败")
        return false
    end
    
    -- 校准输出侧功率分析仪
    dll_send_command(main.device_channels.power_analyzer_output, "CALIBRATION:START")
    local cal_result_output = dll_query_command(main.device_channels.power_analyzer_output, "CALIBRATION:RESULT?")
    
    if cal_result_output ~= "PASS" then
        add_efficiency_error("输出侧功率分析仪校准失败")
        return false
    end
    
    log_message("INFO", "[效率测量] 功率分析仪校准完成")
    return true
end

function set_load_conditions(voltage, current)
    -- 设置电子负载
    dll_send_command(main.device_channels.electronic_load, "MODE:CV")  -- 恒压模式
    dll_send_command(main.device_channels.electronic_load, string.format("VOLTAGE:%.2f", voltage))
    dll_send_command(main.device_channels.electronic_load, string.format("CURRENT:%.2f", current))
    dll_send_command(main.device_channels.electronic_load, "INPUT:ON")
    
    -- 设置电源
    dll_send_command(main.device_channels.power_supply, string.format("VOLTAGE:%.2f", voltage * 1.05))  -- 稍高于负载电压
    dll_send_command(main.device_channels.power_supply, string.format("CURRENT:%.2f", current * 1.2))   -- 稍高于负载电流
    dll_send_command(main.device_channels.power_supply, "OUTPUT:ON")
end

function wait_for_stabilization(target_voltage, target_current)
    local stable_count = 0
    local required_stable_count = 5  -- 需要连续5次测量都稳定
    local max_wait_time = measurement_params.stabilization_time * 2
    local start_time = os.clock()
    
    while (os.clock() - start_time) < max_wait_time do
        local current_v_str = dll_query_command(main.device_channels.power_analyzer_output, "VOLTAGE?")
        local current_i_str = dll_query_command(main.device_channels.power_analyzer_output, "CURRENT?")
        local current_v = tonumber(current_v_str) or 0.0
        local current_i = tonumber(current_i_str) or 0.0
        
        local v_error = math.abs(current_v - target_voltage) / target_voltage
        local i_error = math.abs(current_i - target_current) / target_current
        
        if v_error <= measurement_params.voltage_tolerance and i_error <= measurement_params.current_tolerance then
            stable_count = stable_count + 1
            if stable_count >= required_stable_count then
                log_message("INFO", string.format("[效率测量] 负载已稳定: %.1fV, %.1fA", current_v, current_i))
                return true
            end
        else
            stable_count = 0
        end
        
        os.execute("timeout /t 2 /nobreak > nul")
    end
    
    add_efficiency_error(string.format("负载稳定超时: 目标%.1fV/%.1fA", target_voltage, target_current))
    return false
end

-- ============================================================================
-- PRE 标签 - 预处理阶段
-- ============================================================================
::pre::
log_message("INFO", "[效率测量] === 开始效率测量详细测试预处理阶段 ===")
efficiency_measurement_detail.start_time = os.time()
efficiency_measurement_detail.step_count = 0
efficiency_measurement_detail.error_messages = {}
efficiency_measurement_detail.measurement_data = {}
efficiency_measurement_detail.efficiency_points = {}
efficiency_measurement_detail.power_loss_analysis = {}
efficiency_measurement_detail.thermal_data = {}

-- 检查设备状态 (访问main.lua的全局变量)
if not main.device_initialized then
    add_efficiency_error("设备未初始化，无法执行效率测量")
    efficiency_measurement_detail.test_result = "FAILED"
    goto post
end

-- 检查功率分析仪（输入侧）
local pa_input_status = dll_query_command(main.device_channels.power_analyzer_input, "STATUS?")
if pa_input_status == "ERROR" then
    add_efficiency_error("输入侧功率分析仪不可用")
    efficiency_measurement_detail.test_result = "FAILED"
    goto post
end

-- 检查功率分析仪（输出侧）
local pa_output_status = dll_query_command(main.device_channels.power_analyzer_output, "STATUS?")
if pa_output_status == "ERROR" then
    add_efficiency_error("输出侧功率分析仪不可用")
    efficiency_measurement_detail.test_result = "FAILED"
    goto post
end

-- 检查电子负载
local load_status = dll_query_command(main.device_channels.electronic_load, "STATUS?")
if load_status == "ERROR" then
    add_efficiency_error("电子负载不可用")
    efficiency_measurement_detail.test_result = "FAILED"
    goto post
end

-- 检查电源
local ps_status = dll_query_command(main.device_channels.power_supply, "STATUS?")
if ps_status == "ERROR" then
    add_efficiency_error("电源不可用")
    efficiency_measurement_detail.test_result = "FAILED"
    goto post
end

-- 检查温度传感器
local temp_status = dll_query_command(main.device_channels.thermal_sensor, "STATUS?")
if temp_status == "ERROR" then
    add_efficiency_error("温度传感器不可用")
    efficiency_measurement_detail.test_result = "FAILED"
    goto post
end

-- 配置功率分析仪（输入侧）
log_message("INFO", "[效率测量] 配置输入侧功率分析仪...")
dll_send_command(main.device_channels.power_analyzer_input, "RESET")
dll_send_command(main.device_channels.power_analyzer_input, "VOLTAGE:RANGE:AUTO")
dll_send_command(main.device_channels.power_analyzer_input, "CURRENT:RANGE:AUTO")
dll_send_command(main.device_channels.power_analyzer_input, "POWER:RANGE:AUTO")
dll_send_command(main.device_channels.power_analyzer_input, "FREQUENCY:50")
dll_send_command(main.device_channels.power_analyzer_input, "INTEGRATION:ON")
dll_send_command(main.device_channels.power_analyzer_input, "AVERAGE:COUNT:10")

-- 配置功率分析仪（输出侧）
log_message("INFO", "[效率测量] 配置输出侧功率分析仪...")
dll_send_command(main.device_channels.power_analyzer_output, "RESET")
dll_send_command(main.device_channels.power_analyzer_output, "VOLTAGE:RANGE:AUTO")
dll_send_command(main.device_channels.power_analyzer_output, "CURRENT:RANGE:AUTO")
dll_send_command(main.device_channels.power_analyzer_output, "POWER:RANGE:AUTO")
dll_send_command(main.device_channels.power_analyzer_output, "INTEGRATION:ON")
dll_send_command(main.device_channels.power_analyzer_output, "AVERAGE:COUNT:10")

-- 配置电子负载
log_message("INFO", "[效率测量] 配置电子负载...")
dll_send_command(main.device_channels.electronic_load, "RESET")
dll_send_command(main.device_channels.electronic_load, "INPUT:OFF")
dll_send_command(main.device_channels.electronic_load, "PROTECTION:VOLTAGE:MAX:500")
dll_send_command(main.device_channels.electronic_load, "PROTECTION:CURRENT:MAX:150")
dll_send_command(main.device_channels.electronic_load, "PROTECTION:POWER:MAX:75000")

-- 配置电源
log_message("INFO", "[效率测量] 配置电源...")
dll_send_command(main.device_channels.power_supply, "RESET")
dll_send_command(main.device_channels.power_supply, "OUTPUT:OFF")
dll_send_command(main.device_channels.power_supply, "PROTECTION:VOLTAGE:MAX:500")
dll_send_command(main.device_channels.power_supply, "PROTECTION:CURRENT:MAX:150")

-- 执行校准
if not perform_calibration() then
    efficiency_measurement_detail.test_result = "FAILED"
    goto post
end

-- 测量环境温度
local ambient_temp_str = dll_query_command(main.device_channels.thermal_sensor, "TEMPERATURE?")
measurement_params.ambient_temperature = tonumber(ambient_temp_str) or 25.0
log_message("INFO", string.format("[效率测量] 环境温度: %.1f°C", measurement_params.ambient_temperature))

log_message("INFO", "[效率测量] === 效率测量详细测试预处理阶段完成 ===")

-- ============================================================================
-- SEQ 标签 - 测试序列阶段
-- ============================================================================
::seq::
log_message("INFO", "[效率测量] === 开始效率测量详细测试序列阶段 ===")

-- 遍历所有电压和电流测试点
for _, voltage in ipairs(measurement_params.voltage_points) do
    for _, current in ipairs(measurement_params.current_points) do
        efficiency_measurement_detail.step_count = efficiency_measurement_detail.step_count + 1
        
        local test_point = {
            voltage = voltage,
            current = current,
            power = voltage * current / 1000.0  -- 转换为kW
        }
        
        log_message("INFO", string.format("[效率测量] 测试点 %d: %.1fV, %.1fA, %.2fkW (步骤 %d)", 
                              efficiency_measurement_detail.step_count, voltage, current, test_point.power, 
                              efficiency_measurement_detail.step_count))
        
        -- 设置负载条件
        set_load_conditions(voltage, current)
        
        -- 等待稳定
        if not wait_for_stabilization(voltage, current) then
            add_efficiency_error(string.format("测试点 %.1fV/%.1fA 无法稳定", voltage, current))
            goto next_point
        end
        
        -- 开始测量
        local measurement_start_time = os.clock()
        local measurements = {}
        local measurement_count = 0
        
        -- 重置积分器
        dll_send_command(main.device_channels.power_analyzer_input, "INTEGRATION:RESET")
        dll_send_command(main.device_channels.power_analyzer_output, "INTEGRATION:RESET")
        dll_send_command(main.device_channels.power_analyzer_input, "INTEGRATION:START")
        dll_send_command(main.device_channels.power_analyzer_output, "INTEGRATION:START")
        
        while (os.clock() - measurement_start_time) < measurement_params.measurement_duration do
            local measurement = measure_power_parameters(test_point)
            
            if measurement.valid then
                table.insert(measurements, measurement)
                measurement_count = measurement_count + 1
                
                -- 检查温度
                local temp_rise = measurement.temperature - measurement_params.ambient_temperature
                if temp_rise > measurement_params.max_temperature_rise then
                    add_efficiency_error(string.format("温升过高: %.1f°C > %.1f°C", 
                                        temp_rise, measurement_params.max_temperature_rise))
                end
                
                -- 检查功率因数
                if measurement.input_power_factor < measurement_params.power_factor_min then
                    add_efficiency_error(string.format("功率因数过低: %.3f < %.2f", 
                                        measurement.input_power_factor, measurement_params.power_factor_min))
                end
            else
                add_efficiency_error(string.format("测试点 %.1fV/%.1fA 测量无效", voltage, current))
            end
            
            os.execute(string.format("timeout /t %d /nobreak > nul", math.floor(measurement_params.measurement_interval)))
        end
        
        -- 停止积分
        dll_send_command(main.device_channels.power_analyzer_input, "INTEGRATION:STOP")
        dll_send_command(main.device_channels.power_analyzer_output, "INTEGRATION:STOP")
        
        -- 计算平均值
        if measurement_count > 0 then
            local avg_measurement = {
                voltage = voltage,
                current = current,
                input_voltage = 0.0,
                input_current = 0.0,
                input_power = 0.0,
                input_power_factor = 0.0,
                output_voltage = 0.0,
                output_current = 0.0,
                output_power = 0.0,
                efficiency = 0.0,
                power_loss = 0.0,
                temperature = 0.0,
                measurement_count = measurement_count
            }
            
            -- 计算所有参数的平均值
            for _, m in ipairs(measurements) do
                avg_measurement.input_voltage = avg_measurement.input_voltage + m.input_voltage
                avg_measurement.input_current = avg_measurement.input_current + m.input_current
                avg_measurement.input_power = avg_measurement.input_power + m.input_power
                avg_measurement.input_power_factor = avg_measurement.input_power_factor + m.input_power_factor
                avg_measurement.output_voltage = avg_measurement.output_voltage + m.output_voltage
                avg_measurement.output_current = avg_measurement.output_current + m.output_current
                avg_measurement.output_power = avg_measurement.output_power + m.output_power
                avg_measurement.temperature = avg_measurement.temperature + m.temperature
            end
            
            -- 除以测量次数得到平均值
            avg_measurement.input_voltage = avg_measurement.input_voltage / measurement_count
            avg_measurement.input_current = avg_measurement.input_current / measurement_count
            avg_measurement.input_power = avg_measurement.input_power / measurement_count
            avg_measurement.input_power_factor = avg_measurement.input_power_factor / measurement_count
            avg_measurement.output_voltage = avg_measurement.output_voltage / measurement_count
            avg_measurement.output_current = avg_measurement.output_current / measurement_count
            avg_measurement.output_power = avg_measurement.output_power / measurement_count
            avg_measurement.temperature = avg_measurement.temperature / measurement_count
            
            -- 重新计算效率和功率损耗
            avg_measurement.efficiency = calculate_efficiency(avg_measurement.input_power, avg_measurement.output_power)
            avg_measurement.power_loss = calculate_power_loss(avg_measurement.input_power, avg_measurement.output_power)
            
            -- 记录测试数据
            table.insert(efficiency_measurement_detail.measurement_data, avg_measurement)
            table.insert(efficiency_measurement_detail.efficiency_points, {
                voltage = voltage,
                current = current,
                power = test_point.power,
                efficiency = avg_measurement.efficiency
            })
            
            -- 记录功率损耗分析
            table.insert(efficiency_measurement_detail.power_loss_analysis, {
                voltage = voltage,
                current = current,
                power_loss = avg_measurement.power_loss,
                loss_percentage = (avg_measurement.power_loss / avg_measurement.input_power) * 100.0
            })
            
            -- 记录热数据
            table.insert(efficiency_measurement_detail.thermal_data, {
                voltage = voltage,
                current = current,
                temperature = avg_measurement.temperature,
                temperature_rise = avg_measurement.temperature - measurement_params.ambient_temperature
            })
            
            log_message("INFO", string.format("[效率测量] 测试点完成: 效率=%.2f%%, 功率损耗=%.1fW, 温度=%.1f°C", 
                                  avg_measurement.efficiency, avg_measurement.power_loss, avg_measurement.temperature))
            
            -- 检查效率是否达标
            if avg_measurement.efficiency < measurement_params.min_efficiency then
                add_efficiency_error(string.format("效率不达标: %.2f%% < %.1f%% (%.1fV/%.1fA)", 
                                    avg_measurement.efficiency, measurement_params.min_efficiency, voltage, current))
            end
        else
            add_efficiency_error(string.format("测试点 %.1fV/%.1fA 无有效测量数据", voltage, current))
        end
        
        ::next_point::
        
        -- 关闭负载
        dll_send_command(main.device_channels.electronic_load, "INPUT:OFF")
        dll_send_command(main.device_channels.power_supply, "OUTPUT:OFF")
        
        -- 等待冷却
        os.execute("timeout /t 5 /nobreak > nul")
    end
end

-- 判断测试结果
local seq_test_passed = (#efficiency_measurement_detail.error_messages == 0) and 
                       (#efficiency_measurement_detail.measurement_data > 0)

if seq_test_passed then
    log_message("INFO", "[效率测量] 效率测量详细测试序列阶段通过")
else
    log_message("ERROR", "[效率测量] 效率测量详细测试序列阶段失败")
end

log_message("INFO", "[效率测量] === 效率测量详细测试序列阶段完成 ===")

-- ============================================================================
-- POST 标签 - 后处理阶段
-- ============================================================================
::post::
log_message("INFO", "[效率测量] === 开始效率测量详细测试后处理阶段 ===")
efficiency_measurement_detail.end_time = os.time()

-- 关闭所有设备
dll_send_command(main.device_channels.electronic_load, "INPUT:OFF")
dll_send_command(main.device_channels.power_supply, "OUTPUT:OFF")
dll_send_command(main.device_channels.power_analyzer_input, "INTEGRATION:STOP")
dll_send_command(main.device_channels.power_analyzer_output, "INTEGRATION:STOP")
log_message("INFO", "[效率测量] 所有设备已关闭")

-- 生成测试报告
local test_duration = efficiency_measurement_detail.end_time - efficiency_measurement_detail.start_time
local total_measurements = #efficiency_measurement_detail.measurement_data

-- 计算统计数据
local efficiency_sum = 0.0
local max_efficiency = 0.0
local min_efficiency = 100.0
local total_power_loss = 0.0
local max_temperature = measurement_params.ambient_temperature

for _, data in ipairs(efficiency_measurement_detail.measurement_data) do
    efficiency_sum = efficiency_sum + data.efficiency
    if data.efficiency > max_efficiency then
        max_efficiency = data.efficiency
    end
    if data.efficiency < min_efficiency then
        min_efficiency = data.efficiency
    end
    total_power_loss = total_power_loss + data.power_loss
    if data.temperature > max_temperature then
        max_temperature = data.temperature
    end
end

local avg_efficiency = 0.0
if total_measurements > 0 then
    avg_efficiency = efficiency_sum / total_measurements
end

-- 确定最终测试结果
if #efficiency_measurement_detail.error_messages == 0 and 
   total_measurements > 0 and
   min_efficiency >= measurement_params.min_efficiency then
    efficiency_measurement_detail.test_result = "PASS"
else
    efficiency_measurement_detail.test_result = "FAILED"
end

-- 输出测试结果
print("\n" .. string.rep("-", 70))
print("效率测量详细测试结果报告")
print(string.rep("-", 70))
print(string.format("测试名称: %s", efficiency_measurement_detail.test_name))
print(string.format("测试结果: %s", efficiency_measurement_detail.test_result))
print(string.format("测试时长: %d 秒 (%.1f 分钟)", test_duration, test_duration / 60.0))
print(string.format("测试步骤: %d", efficiency_measurement_detail.step_count))
print(string.format("测量点数: %d", total_measurements))
print(string.format("平均效率: %.2f%%", avg_efficiency))
print(string.format("最高效率: %.2f%%", max_efficiency))
print(string.format("最低效率: %.2f%%", min_efficiency))
print(string.format("总功率损耗: %.1fW", total_power_loss))
print(string.format("最高温度: %.1f°C", max_temperature))
print(string.format("最大温升: %.1f°C", max_temperature - measurement_params.ambient_temperature))
print(string.format("错误数量: %d", #efficiency_measurement_detail.error_messages))

if #efficiency_measurement_detail.error_messages > 0 then
    print("错误信息:")
    for i, error_msg in ipairs(efficiency_measurement_detail.error_messages) do
        print(string.format("  %d. %s", i, error_msg))
    end
end

-- 输出详细测量数据
if total_measurements > 0 then
    print("\n详细测量数据:")
    print("电压(V)  电流(A)  输入功率(W)  输出功率(W)  效率(%)  损耗(W)  温度(°C)")
    print(string.rep("-", 70))
    
    for _, data in ipairs(efficiency_measurement_detail.measurement_data) do
        print(string.format("%7.1f  %7.1f  %10.1f  %10.1f  %7.2f  %7.1f  %7.1f", 
              data.voltage, data.current, data.input_power, data.output_power, 
              data.efficiency, data.power_loss, data.temperature))
    end
end
print(string.rep("-", 70))

-- 写入JSON报告
write_efficiency_measurement_json_report()

log_message("INFO", "[效率测量] === 效率测量详细测试后处理阶段完成 ===")
log_message("INFO", string.format("[效率测量] 效率测量详细测试最终结果: %s", efficiency_measurement_detail.test_result))

-- 返回测试结果
return efficiency_measurement_detail.test_result