#include "engine_core.hpp"
#include "device_manager.hpp"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <algorithm>

// EngineCore 实现
EngineCore::EngineCore(const EngineCoreConfig& config)
    : m_config(config)
    , m_last_heartbeat(std::chrono::system_clock::now()) {
}

EngineCore::~EngineCore() {
    // 析构函数中不再调用shutdown，由外部显式调用cleanup
}

ATE_EC EngineCore::initialize(const std::string& dll_directory_path) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_state.load() != FSMState::TERMINATED) {
        return ATE_ERROR_ALREADY_INITIALIZED;
    }
    
    if (!m_config.is_valid()) {
        set_last_error("Invalid configuration");
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    try {
        // 初始化设备管理器
        if (!dll_directory_path.empty()) {
            auto& device_manager = DeviceInstanceManager::get_instance();
            ATE_EC result = device_manager.initialize(dll_directory_path);
            if (result != ATE_SUCCESS) {
                set_last_error("Failed to initialize device manager");
                set_state(FSMState::ERROR_STATE);
                return result;
            }
        }
        
        // 初始化子系统
        ATE_EC result = initialize_subsystems();
        if (result != ATE_SUCCESS) {
            set_last_error("Failed to initialize subsystems");
            set_state(FSMState::ERROR_STATE);
            return result;
        }
        
        set_state(FSMState::READY);
        return ATE_SUCCESS;
        
    } catch (const std::exception& e) {
        set_last_error(std::string("Exception during initialization: ") + e.what());
        set_state(FSMState::ERROR_STATE);
        return ATE_ERROR_INITIALIZATION_FAILED;
    }
}



ATE_EC EngineCore::start_engine() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_state.load() != FSMState::READY && m_state.load() != FSMState::PAUSED) {
        return ATE_ERROR_OPERATION_NOT_PERMITTED;
    }
    
    try {
        // 启动监控线程
        if (!m_status_monitor_thread) {
            m_status_monitor_thread = std::make_unique<std::thread>(
                &EngineCore::status_monitor_thread_function, this);
        }
        
        // 启动心跳线程
        if (!m_heartbeat_thread) {
            m_heartbeat_thread = std::make_unique<std::thread>(
                &EngineCore::heartbeat_thread_function, this);
        }
        
        set_state(FSMState::RUNNING);
        return ATE_SUCCESS;
        
    } catch (const std::exception& e) {
        set_last_error(std::string("Failed to start engine: ") + e.what());
        return ATE_ERROR_THREAD_CREATE_FAILED;
    }
}

ATE_EC EngineCore::stop_engine() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_state.load() != FSMState::RUNNING) {
        return ATE_ERROR_OPERATION_NOT_PERMITTED;
    }
    
    // 停止数据记录
    if (m_data_recorder && m_data_recorder->is_recording()) {
        m_data_recorder->stop_recording();
    }
    
    set_state(FSMState::READY);
    return ATE_SUCCESS;
}

ATE_EC EngineCore::suspend_engine() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_state.load() != FSMState::RUNNING) {
        return ATE_ERROR_OPERATION_NOT_PERMITTED;
    }
    
    try {
        set_state(FSMState::PAUSED);
        set_last_error("");
        return ATE_SUCCESS;
    } catch (const std::exception& e) {
        set_last_error(std::string("Suspend failed: ") + e.what());
        return ATE_ERROR_COMMAND_FAILED;
    }
}

ATE_EC EngineCore::resume_engine() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_state.load() != FSMState::PAUSED) {
        return ATE_ERROR_OPERATION_NOT_PERMITTED;
    }
    
    try {
        set_state(FSMState::RUNNING);
        set_last_error("");
        return ATE_SUCCESS;
    } catch (const std::exception& e) {
        set_last_error(std::string("Cleanup failed: ") + e.what());
        return ATE_ERROR_COMMAND_FAILED;
    }
}

FSMState EngineCore::get_engine_state() const {
    return m_state.load();
}



// ========================================================================
// 变量系统接口
// ========================================================================

EngineVariableSystem& EngineCore::get_variable_system() {
    if (!m_variable_system) {
        throw std::runtime_error("Variable system not initialized");
    }
    return *m_variable_system;
}

// ========================================================================
// 数据记录器接口
// ========================================================================

DataRecorder& EngineCore::get_data_recorder() {
    if (!m_data_recorder) {
        throw std::runtime_error("Data recorder not initialized");
    }
    return *m_data_recorder;
}

ATE_EC EngineCore::begin_data_recording(const std::string& config_name) {
    if (!m_data_recorder) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    return m_data_recorder->start_recording();
}

ATE_EC EngineCore::end_data_recording() {
    if (!m_data_recorder) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    return m_data_recorder->stop_recording();
}

ATE_EC EngineCore::add_variable_to_record(const std::string& variable_name) {
    if (!m_data_recorder) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    return m_data_recorder->add_variable_to_record(variable_name);
}

// ========================================================================
// 配置管理
// ========================================================================

ATE_EC EngineCore::update_config(const EngineCoreConfig& config) {
    if (!config.is_valid()) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    if (m_state.load() == FSMState::RUNNING) {
        return ATE_ERROR_DEVICE_BUSY;
    }
    
    std::lock_guard<std::mutex> lock(m_mutex);
    m_config = config;
    
    // 更新子系统配置
    // 注意：GlobalVariableTask的Redis配置在构造时设置，运行时不可更改
    
    // DataRecorder配置在构造时设置，运行时不可更改
    
    return ATE_SUCCESS;
}

const EngineCoreConfig& EngineCore::get_config() const {
    return m_config;
}

// ========================================================================
// 状态监控和诊断
// ========================================================================

std::string EngineCore::get_system_status() const {
    std::ostringstream oss;
    oss << "{";
    oss << "\"engine_state\":\"" << static_cast<int>(m_state.load()) << "\",";
    oss << "\"is_initialized\":" << (m_state.load() != FSMState::TERMINATED ? "true" : "false") << ",";
    oss << "\"is_running\":" << (m_state.load() == FSMState::RUNNING ? "true" : "false") << ",";
    
    if (m_variable_system) {
        oss << "\"variable_count\":" << m_variable_system->get_variable_count() << ",";
    }
    
    if (m_data_recorder) {
        oss << "\"recording\":" << (m_data_recorder->is_recording() ? "true" : "false") << ",";
        oss << "\"record_count\":" << m_data_recorder->get_record_count() << ",";
        oss << "\"buffer_usage\":" << std::fixed << std::setprecision(2) << m_data_recorder->get_buffer_usage() << ",";
    }
    
    auto& device_manager = DeviceInstanceManager::get_instance();
    oss << "\"device_count\":" << device_manager.get_instance_count() << ",";
    
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    oss << "\"timestamp\":\"" << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << "\"";
    
    oss << "}";
    return oss.str();
}

ATE_EC EngineCore::perform_self_test() {
    if (m_state.load() == FSMState::TERMINATED) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    // 检查子系统健康状态
    if (!check_subsystems_health()) {
        set_last_error("Subsystems health check failed");
        return ATE_ERROR_SELF_TEST_FAILED;
    }
    
    // 检查设备管理器
    auto& device_manager = DeviceInstanceManager::get_instance();
    if (!device_manager.is_initialized()) {
        set_last_error("Device manager not initialized");
        return ATE_ERROR_SELF_TEST_FAILED;
    }
    
    return ATE_SUCCESS;
}

std::string EngineCore::get_last_error() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_last_error;
}

// ========================================================================
// 私有成员函数
// ========================================================================

void EngineCore::status_monitor_thread_function() {
    while (!m_shutdown_requested.load()) {
        try {
            // 检查子系统健康状态
            if (!check_subsystems_health()) {
                set_last_error("Subsystem health check failed");
                // 这里可以添加自动恢复逻辑
            }
            
        } catch (const std::exception& e) {
            set_last_error(std::string("Status monitor error: ") + e.what());
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(1000)); // 1秒检查间隔
    }
}

void EngineCore::heartbeat_thread_function() {
    while (!m_shutdown_requested.load()) {
        m_last_heartbeat = std::chrono::system_clock::now();
        std::this_thread::sleep_for(std::chrono::milliseconds(5000)); // 5秒心跳间隔
    }
}

void EngineCore::set_state(FSMState new_state) {
    FSMState old_state = m_state.load();
    m_state.store(new_state);
    
    // 如果状态发生变化，触发回调
    if (old_state != new_state) {
        // 触发外部注册的回调函数（异步执行）
        extern void async_invoke_state_change_callback(int32_t engine_id, FSMState from, FSMState to, const std::string& event_data);
        async_invoke_state_change_callback(m_config.engine_id, old_state, new_state, "");
    }
}

void EngineCore::set_last_error(const std::string& error_message) {
    m_last_error = error_message;
    std::cerr << "EngineCore Error: " << error_message << std::endl;
}

bool EngineCore::check_subsystems_health() const {
    // 检查变量系统
    if (!m_variable_system) {
        return false;
    }
    
    // 检查数据记录器
    if (!m_data_recorder) {
        return false;
    }
    
    return true;
}

ATE_EC EngineCore::initialize_subsystems() {
    try {
        // 初始化变量系统
        m_variable_system = std::make_unique<EngineVariableSystem>();
        
        // 为数据记录器创建单独的GlobalVariableTask实例
    RedisConfig default_redis_config;
    m_global_variable_system = std::make_unique<GlobalVariableTask>("default_instance", default_redis_config);
        ATE_EC result = m_global_variable_system->initialize();
        if (result != ATE_SUCCESS) {
            return result;
        }
        
        // 初始化数据记录器
        DataRecorderConfig default_recorder_config;
        m_data_recorder = std::make_unique<DataRecorder>(*m_global_variable_system, default_recorder_config);
        result = m_data_recorder->initialize();
        if (result != ATE_SUCCESS) {
            return result;
        }
        
        // 初始化报告处理系统
        m_report_process = std::make_unique<ReportProcess>();
        m_report_process->set_variable_system(m_global_variable_system.get());
        m_report_process->set_data_recorder(m_data_recorder.get());
        result = m_report_process->initialize();
        if (result != ATE_SUCCESS) {
            return result;
        }
        
        return ATE_SUCCESS;
        
    } catch (const std::exception& e) {
        set_last_error(std::string("Failed to initialize subsystems: ") + e.what());
        return ATE_ERROR_INITIALIZATION_FAILED;
    }
}

void EngineCore::shutdown_subsystems() {
    if (m_report_process) {
        m_report_process->shutdown();
        m_report_process.reset();
    }
    
    if (m_data_recorder) {
        m_data_recorder->shutdown();
        m_data_recorder.reset();
    }
    
    if (m_global_variable_system) {
        m_global_variable_system->shutdown();
        m_global_variable_system.reset();
    }
    
    if (m_variable_system) {
        m_variable_system.reset();
    }
}

// ========================================================================
// Lua脚本执行控制接口实现
// ========================================================================

ATE_EC EngineCore::execute_script(const std::string& script_content, const std::string& script_name, const std::string& script_type) {
    if (m_state.load() == FSMState::TERMINATED) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_variable_system) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    try {
        // 确定脚本类型
        LuaScriptType lua_script_type = LuaScriptType::MA_SCRIPT;
        if (script_type == "PR_SCRIPT") {
            lua_script_type = LuaScriptType::PR_SCRIPT;
        }
        
        // 调用变量系统的脚本执行接口
        ATE_EC result = m_variable_system->start_script_execution(script_content, script_name, lua_script_type);
        
        if (result == ATE_SUCCESS) {
            std::cout << "Started Lua script execution: " << script_name << " (Type: " << script_type << ")" << std::endl;
        }
        
        return result;
        
    } catch (const std::exception& e) {
        std::cout << "Error starting Lua script: " << e.what() << std::endl;
        return ATE_ERROR_COMMAND_FAILED;
    }
}

ATE_EC EngineCore::terminate_script() {
    if (m_state.load() == FSMState::TERMINATED) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_variable_system) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    try {
        ATE_EC result = m_variable_system->stop_script_execution();
        
        if (result == ATE_SUCCESS) {
            std::cout << "Stopped Lua script execution" << std::endl;
        }
        
        return result;
        
    } catch (const std::exception& e) {
        std::cout << "Error stopping Lua script: " << e.what() << std::endl;
        return ATE_ERROR_COMMAND_FAILED;
    }
}

ATE_EC EngineCore::pause_script() {
    if (m_state.load() == FSMState::TERMINATED) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_variable_system) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    try {
        ATE_EC result = m_variable_system->suspend_script_execution();
        
        if (result == ATE_SUCCESS) {
            std::cout << "Suspended Lua script execution" << std::endl;
        }
        
        return result;
        
    } catch (const std::exception& e) {
        std::cout << "Error suspending Lua script: " << e.what() << std::endl;
        return ATE_ERROR_COMMAND_FAILED;
    }
}

ATE_EC EngineCore::resume_script() {
    if (m_state.load() == FSMState::TERMINATED) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_variable_system) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    try {
        ATE_EC result = m_variable_system->resume_script_execution();
        
        if (result == ATE_SUCCESS) {
            std::cout << "Resumed Lua script execution" << std::endl;
        }
        
        return result;
        
    } catch (const std::exception& e) {
        std::cout << "Error resuming Lua script: " << e.what() << std::endl;
        return ATE_ERROR_COMMAND_FAILED;
    }
}

ATE_EC EngineCore::set_script_breakpoint(int line_number, const std::string& script_name) {
    if (m_state.load() == FSMState::TERMINATED) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_variable_system) {
        return ATE_ERROR_INVALID_PARAMETER;
    }
    
    try {
        // 创建断点对象
        DebugBreakpoint breakpoint(line_number, script_name);
        ATE_EC result = m_variable_system->add_debug_breakpoint(breakpoint);
        
        if (result == ATE_SUCCESS) {
            std::cout << "Set debug breakpoint at line " << line_number;
            if (!script_name.empty()) {
                std::cout << " in script: " << script_name;
            }
            std::cout << std::endl;
        }
        
        return result;
        
    } catch (const std::exception& e) {
        std::cout << "Error setting debug breakpoint: " << e.what() << std::endl;
        return ATE_ERROR_COMMAND_FAILED;
    }
}

std::string EngineCore::get_variables_snapshot_json() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_state.load() == FSMState::TERMINATED || !m_variable_system) {
        return "{}";
    }
    
    try {
        return m_variable_system->get_all_variables_json();
    } catch (const std::exception& e) {
        std::cout << "Error getting debug variables: " << e.what() << std::endl;
        return "{}";
    }
}

LuaExecutionState EngineCore::get_script_execution_state() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_state.load() == FSMState::TERMINATED || !m_variable_system) {
        return LuaExecutionState::IDLE;
    }
    
    try {
        return m_variable_system->get_script_execution_state();
    } catch (const std::exception& e) {
        std::cout << "Error getting Lua script state: " << e.what() << std::endl;
        return LuaExecutionState::IDLE;
    }
}

ATE_EC EngineCore::cleanup() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    try {
        std::cout << "EngineCore[" << m_config.engine_id << "] starting cleanup" << std::endl;
        
        // 设置清理请求标志
        m_cleanup_requested.store(true);
        
        // 停止数据记录
        if (m_data_recorder && m_data_recorder->is_recording()) {
            m_data_recorder->stop_recording();
        }
        
        // 清理变量系统
        if (m_variable_system) {
            // 可以添加变量系统的清理逻辑
            std::cout << "EngineCore[" << m_config.engine_id << "] cleaning up variable system" << std::endl;
        }
        
        // 重置清理请求标志
        m_cleanup_requested.store(false);
        
        std::cout << "EngineCore[" << m_config.engine_id << "] cleanup completed" << std::endl;
        set_state(FSMState::TERMINATED);
        set_last_error("");
        return ATE_SUCCESS;
        
    } catch (const std::exception& e) {
        m_cleanup_requested.store(false);
        set_last_error(std::string("Cleanup failed: ") + e.what());
        return ATE_ERROR_COMMAND_FAILED;
    }
}

