#ifndef ENGINE_FRAMEWORK_INTERFACE_HPP
#define ENGINE_FRAMEWORK_INTERFACE_HPP

#include "common_types.hpp"
#include <cstdint>

// 定义引擎状态变化回调函数类型，确保跨语言兼容性
typedef void (*EngineCoreStateChangeCallback)(int32_t engine_id, FSMState from, FSMState to, const char* event_data);

// 定义全局核心状态变化回调函数类型
typedef void (*GlobalCoreStateChangeCallback)(FSMState from, FSMState to, const char* event_data);

extern "C" {

/**
 * @brief 初始化引擎管理器
 * 
 * 该函数负责初始化引擎管理器，加载指定路径下的所有外设DLL，
 * 解析对应的JSON配置文件，并建立全局容器来管理所有设备实例。
 * 
 * @param dll_directory_path DLL目录路径，包含所有需要管理的外设DLL文件
 * @return ATE_EC 错误代码
 *   - ATE_SUCCESS: 初始化成功
 *   - ATE_ERROR_INVALID_PARAMETER: 路径参数无效
 *   - ATE_ERROR_ALREADY_INITIALIZED: 已经初始化
 *   - ATE_ERROR_INITIALIZATION_FAILED: 初始化失败
 *   - ATE_ERROR_CONFIG_LOAD_FAILED: 配置文件加载失败
 *   - ATE_ERROR_LIBRARY_LOAD_FAILED: DLL加载失败
 *   - ATE_ERROR_PATH_NOT_FOUND: 路径不存在
 * 
 * @note 该函数会扫描指定目录下的所有DLL文件和对应的JSON配置文件，
 *       为每个有效的设备实例创建容器元素，包含：
 *       - 实例名（从JSON配置中读取）
 *       - 设备索引（从JSON配置中读取）
 *       - DLL实例化后的句柄
 */
EXP_API ATE_EC initialize_dll(const char* dll_directory_path);

/**
 * @brief 清理引擎管理器
 * 
 * 该函数负责清理引擎管理器，卸载指定路径下已经加载的所有外设DLL，
 * 释放相关资源，并清理全局容器中的数据。
 * 
 * @param dll_directory_path DLL目录路径，指定需要清理的DLL路径
 *                          如果为空字符串或nullptr，则清理所有已加载的DLL
 * @return ATE_EC 错误代码
 *   - ATE_SUCCESS: 清理成功
 *   - ATE_ERROR_NOT_INITIALIZED: 未初始化
 *   - ATE_ERROR_INVALID_PARAMETER: 路径参数无效
 * 
 * @note 该函数会依次调用每个已加载DLL的cleanup_dll函数，
 *       然后卸载DLL并清理对应的容器元素。
 */
EXP_API ATE_EC cleanup_dll(const char* dll_directory_path);

/**
 * @brief 连接设备
 * 
 * 该函数通过实例名在全局容器中查找对应的设备实例，
 * 获取其句柄和设备索引，然后调用对应DLL的connect_device函数。
 * 
 * @param instance_name 设备实例名称，用于在全局容器中查找对应的设备
 * @return ATE_EC 错误代码
 *   - ATE_SUCCESS: 连接成功
 *   - ATE_ERROR_NOT_INITIALIZED: 驱动管理器未初始化
 *   - ATE_ERROR_INVALID_PARAMETER: 实例名参数无效
 *   - ATE_ERROR_DEVICE_NOT_FOUND: 未找到指定实例
 *   - ATE_ERROR_CONNECTION_FAILED: 连接失败
 *   - ATE_ERROR_DEVICE_ALREADY_EXISTS: 设备已连接
 * 
 * @note 该函数不加锁，依赖底层每个设备DLL自己的锁机制，
 *       以实现不同设备的并发执行。
 */
EXP_API ATE_EC connect_device(const char* instance_name);

/**
 * @brief 断开设备连接
 * 
 * 该函数通过实例名在全局容器中查找对应的设备实例，
 * 获取其句柄和设备索引，然后调用对应DLL的disconnect_device函数。
 * 
 * @param instance_name 设备实例名称，用于在全局容器中查找对应的设备
 * @return ATE_EC 错误代码
 *   - ATE_SUCCESS: 断开连接成功
 *   - ATE_ERROR_NOT_INITIALIZED: 驱动管理器未初始化
 *   - ATE_ERROR_INVALID_PARAMETER: 实例名参数无效
 *   - ATE_ERROR_DEVICE_NOT_FOUND: 未找到指定实例
 *   - ATE_ERROR_DEVICE_OFFLINE: 设备已离线
 * 
 * @note 该函数不加锁，依赖底层每个设备DLL自己的锁机制。
 */
EXP_API ATE_EC disconnect_device(const char* instance_name);

/**
 * @brief 获取设备描述符
 * 
 * 该函数通过实例名在全局容器中查找对应的设备实例，
 * 获取其句柄和设备索引，然后调用对应DLL的get_device_descriptor函数。
 * 
 * @param instance_name 设备实例名称
 * @param device_descriptor 用于存储设备描述符的缓冲区
 * @param buffer_size 缓冲区大小
 * @param device_descriptor_size 实际返回的设备描述符大小
 * @return ATE_EC 错误代码
 *   - ATE_SUCCESS: 获取成功
 *   - ATE_ERROR_NOT_INITIALIZED: 驱动管理器未初始化
 *   - ATE_ERROR_INVALID_PARAMETER: 参数无效
 *   - ATE_ERROR_DEVICE_NOT_FOUND: 未找到指定实例
 *   - ATE_ERROR_BUFFER_TOO_SMALL: 缓冲区太小
 *   - ATE_ERROR_NULL_POINTER: 空指针
 * 
 * @note 该函数不加锁，依赖底层每个设备DLL自己的锁机制。
 */
EXP_API ATE_EC get_device_descriptor(const char* instance_name, 
                                    char* device_descriptor, 
                                    const int32_t buffer_size, 
                                    int32_t* device_descriptor_size);

/**
 * @brief 执行统一命令接口
 * 
 * 该函数通过实例名在全局容器中查找对应的设备实例，
 * 获取其句柄和设备索引，然后调用对应DLL的execute_command_unified函数。
 * 
 * @param instance_name 设备实例名称
 * @param mode 命令模式
 * @param command 命令字符串
 * @param command_length 命令长度
 * @param timeout_ms 超时时间（毫秒）
 * @param response 响应缓冲区
 * @param buffer_size 响应缓冲区大小
 * @param response_size 实际响应大小
 * @return ATE_EC 错误代码
 *   - ATE_SUCCESS: 命令执行成功
 *   - ATE_ERROR_NOT_INITIALIZED: 驱动管理器未初始化
 *   - ATE_ERROR_INVALID_PARAMETER: 参数无效
 *   - ATE_ERROR_DEVICE_NOT_FOUND: 未找到指定实例
 *   - ATE_ERROR_COMMAND_FAILED: 命令执行失败
 *   - ATE_ERROR_COMMAND_TIMEOUT: 命令超时
 *   - ATE_ERROR_BUFFER_TOO_SMALL: 响应缓冲区太小
 *   - ATE_ERROR_NULL_POINTER: 空指针
 * 
 * @note 该函数不加锁，依赖底层每个设备DLL自己的锁机制，
 *       以实现不同设备的并发执行。
 */
EXP_API ATE_EC execute_command_unified(const char* instance_name, 
                                      const E5000_CommandMode mode, 
                                      const char* command, 
                                      const int32_t command_length, 
                                      const int32_t timeout_ms, 
                                      char* response, 
                                      const int32_t buffer_size, 
                                      int32_t* response_size);

/**
 * @brief 获取通道状态
 * 
 * 该函数通过实例名在全局容器中查找对应的设备实例，
 * 获取其句柄和设备索引，然后调用对应DLL的get_channel_state函数。
 * 
 * @param instance_name 设备实例名称
 * @param channel_state 用于存储通道状态的缓冲区
 * @param buffer_size 缓冲区大小
 * @param channel_state_size 实际返回的通道状态大小
 * @return ATE_EC 错误代码
 *   - ATE_SUCCESS: 获取成功
 *   - ATE_ERROR_NOT_INITIALIZED: 驱动管理器未初始化
 *   - ATE_ERROR_INVALID_PARAMETER: 参数无效
 *   - ATE_ERROR_DEVICE_NOT_FOUND: 未找到指定实例
 *   - ATE_ERROR_BUFFER_TOO_SMALL: 缓冲区太小
 *   - ATE_ERROR_NULL_POINTER: 空指针
 * 
 * @note 该函数不加锁，依赖底层每个设备DLL自己的锁机制。
 */
EXP_API ATE_EC get_channel_state(const char* instance_name, 
                                char* channel_state, 
                                const int32_t buffer_size, 
                                int32_t* channel_state_size);

/**
 * @brief 注册引擎状态变化回调函数
 * 
 * 该函数用于注册引擎状态变化的回调函数，当引擎状态发生变化时会调用该回调函数
 * 回调函数将在独立线程中异步执行，不会阻塞引擎核心操作
 * 
 * @param engine_id 引擎ID
 * @param callback 回调函数指针
 * @return ATE_EC 错误代码
 *   - ATE_SUCCESS: 注册成功
 *   - ATE_ERROR_INVALID_PARAMETER: 参数无效
 */
EXP_API ATE_EC register_engine_state_change_callback(int32_t engine_id, EngineCoreStateChangeCallback callback);

/**
 * @brief 注销引擎状态变化回调函数
 * 
 * 该函数用于注销之前注册的引擎状态变化回调函数
 * 
 * @param engine_id 引擎ID
 * @return ATE_EC 错误代码
 *   - ATE_SUCCESS: 注销成功
 *   - ATE_ERROR_INVALID_PARAMETER: 参数无效
 */
EXP_API ATE_EC unregister_engine_state_change_callback(int32_t engine_id);

/**
 * @brief 注册全局核心状态变化回调函数
 * 
 * 该函数用于注册全局核心状态变化的回调函数，当全局核心状态发生变化时会调用该回调函数
 * 回调函数将在独立线程中异步执行，不会阻塞全局核心操作
 * 
 * @param callback 回调函数指针
 * @return ATE_EC 错误代码
 *   - ATE_SUCCESS: 注册成功
 *   - ATE_ERROR_INVALID_PARAMETER: 参数无效
 */
EXP_API ATE_EC register_global_state_change_callback(GlobalCoreStateChangeCallback callback);

/**
 * @brief 注销全局核心状态变化回调函数
 * 
 * 该函数用于注销之前注册的全局核心状态变化回调函数
 * 
 * @return ATE_EC 错误代码
 *   - ATE_SUCCESS: 注销成功
 */
EXP_API ATE_EC unregister_global_state_change_callback();

/**
 * @brief 暂停指定ID的引擎核心
 * 
 * 该函数通过引擎管理器暂停指定ID的引擎核心实例。
 * 
 * @param engine_id 引擎核心ID
 * @return ATE_EC 错误代码
 *   - ATE_SUCCESS: 暂停成功
 *   - ATE_ERROR_NOT_INITIALIZED: 引擎管理器未初始化
 *   - ATE_ERROR_INVALID_PARAMETER: 引擎ID无效
 *   - ATE_ERROR_ENGINE_NOT_FOUND: 未找到指定引擎
 *   - ATE_ERROR_COMMAND_FAILED: 暂停失败
 */
EXP_API ATE_EC suspend_engine_core(int32_t engine_id);

/**
 * @brief 恢复指定ID的引擎核心
 * 
 * 该函数通过引擎管理器恢复指定ID的引擎核心实例。
 * 
 * @param engine_id 引擎核心ID
 * @return ATE_EC 错误代码
 *   - ATE_SUCCESS: 恢复成功
 *   - ATE_ERROR_NOT_INITIALIZED: 引擎管理器未初始化
 *   - ATE_ERROR_INVALID_PARAMETER: 引擎ID无效
 *   - ATE_ERROR_ENGINE_NOT_FOUND: 未找到指定引擎
 *   - ATE_ERROR_COMMAND_FAILED: 恢复失败
 */
EXP_API ATE_EC resume_engine_core(int32_t engine_id);

/**
 * @brief 暂停全局核心
 * 
 * 该函数暂停全局核心的运行，停止监控系统资源和状态。
 * 
 * @return ATE_EC 错误代码
 *   - ATE_SUCCESS: 暂停成功
 *   - ATE_ERROR_NOT_INITIALIZED: 全局核心未初始化
 *   - ATE_ERROR_OPERATION_FAILED: 暂停操作失败
 */
EXP_API ATE_EC suspend_global_core();

/**
 * @brief 恢复全局核心
 * 
 * 该函数恢复全局核心的运行，重新开始监控系统资源和状态。
 * 
 * @return ATE_EC 错误代码
 *   - ATE_SUCCESS: 恢复成功
 *   - ATE_ERROR_NOT_INITIALIZED: 全局核心未初始化
 *   - ATE_ERROR_OPERATION_FAILED: 恢复操作失败
 */
EXP_API ATE_EC resume_global_core();

} // extern "C"

#endif // ENGINE_FRAMEWORK_INTERFACE_HPP