#include "engine_manager.hpp"
#include "global_variable_service.hpp"
#include <algorithm>
#include <stdexcept>

EngineManager::EngineManager() 
    : m_initialized(false) {
}

EngineManager::~EngineManager() {
    try {
        cleanup(); // 忽略返回值，析构函数中不应抛出异常
    } catch (...) {
        // 析构函数中忽略所有异常
    }
}

EngineManager& EngineManager::get_instance() {
    static EngineManager instance;
    return instance;
}

ATE_EC EngineManager::initialize(const std::string& config_str) {
    if (m_initialized) {
        return ATE_ERROR_ALREADY_INITIALIZED; // 已经初始化
    }
    
    try {
        // 验证配置
        if (!validate_config(config_str)) {
            return ATE_ERROR_INVALID_PARAMETER;
        }
        
        // 保存配置字符串
        m_config = config_str;
        
        // 解析JSON配置字符串
        nlohmann::json config = nlohmann::json::parse(config_str);

        // 初始化全局变量服务实例
        GlobalVariableService::get_instance(); 

        // 获取全局核心单例
        m_global_core = &GlobalCore::get_instance();
        
        // 如果配置中包含引擎核心配置，则创建引擎核心实例
        if (config.contains("engines") && config["engines"].is_array()) {
            for (const auto& engine_config : config["engines"]) {
                if (engine_config.contains("id") && engine_config.contains("config")) {
                    int engine_id = engine_config["id"];
                    std::string engine_config_str = engine_config["config"].dump();
                    if (!add_engine_core(engine_id, engine_config_str)) {
                        return ATE_ERROR_INITIALIZATION_FAILED;
                    }
                }
            }
        }

        m_initialized = true;
        return ATE_SUCCESS;
        
    } catch (const std::exception& e) {
        return ATE_ERROR_INITIALIZATION_FAILED;
    }
}

ATE_EC EngineManager::start() {
    if (!m_initialized) {
        return ATE_ERROR_NOT_INITIALIZED;
    }
    
    if (m_initialized && m_global_core && m_global_core->get_state() == FSMState::RUNNING) {
        return ATE_SUCCESS; // 已经在运行
    }
    
    try {
        // 启动全局核心
        if (m_global_core) {
            ATE_EC result = start_global_core();
            if (result != ATE_SUCCESS) {
                return result;
            }
        }
        
        // 启动所有引擎核心
        std::lock_guard<std::mutex> lock(m_engines_mutex);
         for (auto& pair : m_engine_cores) {
             if (pair.second) {
                 ATE_EC result = pair.second->start_engine();
                 if (result != ATE_SUCCESS) {
                     return result;
                 }
             }
         }
        
        return ATE_SUCCESS;
        
    } catch (const std::exception& e) {
        return ATE_ERROR_SYSTEM_ERROR;
    }
}



bool EngineManager::add_engine_core(int engine_id, const std::string& engine_config_str) {
    std::lock_guard<std::mutex> lock(m_engines_mutex);
    
    // 检查是否已存在相同ID的引擎
    if (find_engine_core(engine_id) != nullptr) {
        return false; // 已存在
    }
    
    try {
        // 解析JSON配置字符串
        nlohmann::json engine_config = nlohmann::json::parse(engine_config_str);
        
        auto engine = create_engine_core(engine_id, engine_config_str);
        if (engine) {
            m_engine_cores[engine_id] = std::move(engine);
            return true;
        }
    } catch (const std::exception& e) {
        // 创建失败
        return false;
    }
    
    return false;
}

bool EngineManager::remove_engine_core(int engine_id) {
    std::lock_guard<std::mutex> lock(m_engines_mutex);
    
    auto it = m_engine_cores.find(engine_id);
    
    if (it != m_engine_cores.end()) {
        // 先停止引擎
        if (it->second->get_engine_state() == FSMState::RUNNING) {
            it->second->stop_engine();
        }
        m_engine_cores.erase(it);
        return true;
    }
    
    return false;
}

ATE_EC EngineManager::start_engine_core(int engine_id) {
    std::lock_guard<std::mutex> lock(m_engines_mutex);
    
    EngineCore* engine = find_engine_core(engine_id);
    if (engine) {
        return engine->start_engine();
    }
    
    return ATE_ERROR_NOT_FOUND;
}

ATE_EC EngineManager::stop_engine_core(int engine_id) {
    std::lock_guard<std::mutex> lock(m_engines_mutex);
    
    EngineCore* engine = find_engine_core(engine_id);
    if (engine) {
        return engine->stop_engine();
    }
    
    return ATE_ERROR_NOT_FOUND;
}

ATE_EC EngineManager::suspend_engine_core(int engine_id) {
    // 获取互斥锁保护
    std::lock_guard<std::mutex> lock(m_engines_mutex);
    
    // 查找指定ID的引擎核心
    auto* engine = find_engine_core(engine_id);
    if (!engine) {
        // 如果找不到引擎，返回失败
        return ATE_ERROR_NOT_FOUND;
    }
    
    try {
        // 尝试暂停引擎核心
        ATE_EC result = engine->suspend_engine();
        if (result != ATE_SUCCESS) {
            return result;
        }
        
        return ATE_SUCCESS;
    }
    catch (const std::exception& e) {
        // 捕获并处理可能的异常
        return ATE_ERROR_SYSTEM_ERROR;
    }
}

ATE_EC EngineManager::resume_engine_core(int engine_id) {
    // 获取互斥锁保护
    std::lock_guard<std::mutex> lock(m_engines_mutex);
    
    // 查找指定ID的引擎核心
    auto* engine = find_engine_core(engine_id);
    if (!engine) {
        // 如果找不到引擎，返回失败
        return ATE_ERROR_NOT_FOUND;
    }
    
    try {
        // 尝试恢复引擎核心
        ATE_EC result = engine->resume_engine();
        if (result != ATE_SUCCESS) {
            return result;
        }
        
        return ATE_SUCCESS;
    }
    catch (const std::exception& e) {
        // 捕获并处理可能的异常
        return ATE_ERROR_SYSTEM_ERROR;
    }
}



ATE_EC EngineManager::start_global_core() {
    if (m_global_core) {
        return m_global_core->start();
    }
    return ATE_ERROR_NOT_INITIALIZED;
}

ATE_EC EngineManager::stop_global_core() {
    if (m_global_core) {
        return m_global_core->stop();
    }
    return ATE_ERROR_NOT_INITIALIZED;
}

FSMState EngineManager::get_engine_core_status(int engine_id) {
    std::lock_guard<std::mutex> lock(m_engines_mutex);
    
    auto* engine = find_engine_core(engine_id);
    if (!engine) {
        return FSMState::TERMINATED;
    }
    
    try {
        // 返回引擎核心的状态
        return engine->get_engine_state();
    }
    catch (const std::exception& e) {
        return FSMState::ERROR_STATE;
    }
}

FSMState EngineManager::get_global_core_status() {
    if (m_global_core) {
        // 直接返回全局核心的状态
        return m_global_core->get_state();
    }
    return FSMState::TERMINATED;
}






std::string EngineManager::get_config() const {
    if (m_config.empty()) {
        return "{}";
    }
    return m_config;
}

bool EngineManager::validate_config(const std::string& config_str) const {
    try {
        // 解析JSON配置字符串
        nlohmann::json config = nlohmann::json::parse(config_str);
        
        // 基本配置验证
        if (!config.is_object()) {
            return false;
        }
        
        // 验证顶层必需字段：version
        if (!config.contains("version") || !config["version"].is_string()) {
            return false;
        }
        
        // 验证engine_cores数组
        if (!config.contains("engine_cores") || !config["engine_cores"].is_array()) {
            return false;
        }
        
        // 验证每个引擎核心配置
        for (const auto& engine_config : config["engine_cores"]) {
            if (!engine_config.is_object()) {
                return false;
            }
            
            // 验证引擎核心必需字段
            if (!engine_config.contains("engine_id") || !engine_config["engine_id"].is_number_integer()) {
                return false;
            }
            
            if (!engine_config.contains("engine_name") || !engine_config["engine_name"].is_string()) {
                return false;
            }
            
            if (!engine_config.contains("test_project_package_path") || !engine_config["test_project_package_path"].is_string()) {
                return false;
            }
            
            if (!engine_config.contains("test_report_storage_path") || !engine_config["test_report_storage_path"].is_string()) {
                return false;
            }
            
            if (!engine_config.contains("test_data_storage_path") || !engine_config["test_data_storage_path"].is_string()) {
                return false;
            }
            
            if (!engine_config.contains("log_level") || !engine_config["log_level"].is_string()) {
                return false;
            }
            
            // 验证日志级别
            std::string log_level = engine_config["log_level"];
            if (log_level != "DEBUG" && log_level != "INFO" && log_level != "WARN" && log_level != "ERROR") {
                return false;
            }
        }
        
        // 验证system_monitor配置
        if (!config.contains("system_monitor") || !config["system_monitor"].is_object()) {
            return false;
        }
        
        const auto& system_monitor = config["system_monitor"];
        
        // 验证系统监控必需字段
        if (!system_monitor.contains("enabled") || !system_monitor["enabled"].is_boolean()) {
            return false;
        }
        
        if (!system_monitor.contains("monitor_name") || !system_monitor["monitor_name"].is_string()) {
            return false;
        }
        
        if (!system_monitor.contains("global_core_package_path") || !system_monitor["global_core_package_path"].is_string()) {
            return false;
        }
        
        if (!system_monitor.contains("global_variable_system_path") || !system_monitor["global_variable_system_path"].is_string()) {
            return false;
        }
        
        if (!system_monitor.contains("redis_host") || !system_monitor["redis_host"].is_string()) {
            return false;
        }
        
        if (!system_monitor.contains("redis_port") || !system_monitor["redis_port"].is_number_integer()) {
            return false;
        }
        
        if (!system_monitor.contains("redis_database") || !system_monitor["redis_database"].is_number_integer()) {
            return false;
        }
        
        if (!system_monitor.contains("update_frequency") || !system_monitor["update_frequency"].is_number_integer()) {
            return false;
        }
        
        // 验证Redis端口范围
        int redis_port = system_monitor["redis_port"];
        if (redis_port <= 0 || redis_port > 65535) {
            return false;
        }
        
        // 验证Redis数据库编号
        int redis_database = system_monitor["redis_database"];
        if (redis_database < 0 || redis_database > 15) {
            return false;
        }
        
        // 验证更新频率
        int update_frequency = system_monitor["update_frequency"];
        if (update_frequency <= 0 || update_frequency > 10000) {
            return false;
        }
        
        // 验证global_settings配置
        if (!config.contains("global_settings") || !config["global_settings"].is_object()) {
            return false;
        }
        
        const auto& global_settings = config["global_settings"];
        
        // 验证全局设置必需字段
        if (!global_settings.contains("temp_directory") || !global_settings["temp_directory"].is_string()) {
            return false;
        }
        
        if (!global_settings.contains("backup_directory") || !global_settings["backup_directory"].is_string()) {
            return false;
        }
        
        if (!global_settings.contains("auto_start_engines") || !global_settings["auto_start_engines"].is_boolean()) {
            return false;
        }
        
        if (!global_settings.contains("auto_start_system_monitor") || !global_settings["auto_start_system_monitor"].is_boolean()) {
            return false;
        }
        
        return true;
    } catch (const std::exception& e) {
        return false;
    }
}

std::unique_ptr<EngineCore> EngineManager::create_engine_core(int engine_id, const std::string& engine_config_str) {
    try {
        // 解析JSON配置字符串
        nlohmann::json engine_config = nlohmann::json::parse(engine_config_str);
        
        // 创建引擎核心配置
        EngineCoreConfig config;
        
        // 配置日志级别
        if (engine_config.contains("log_level")) {
            config.log_level = engine_config["log_level"];
        }
        
        auto engine = std::make_unique<EngineCore>(config);
        
        // 获取DLL路径
        std::string dll_path = "";
        if (engine_config.contains("dll_path")) {
            dll_path = engine_config["dll_path"];
        }
        
        // 初始化引擎核心
        if (engine->initialize(dll_path) == ATE_SUCCESS) {
            return engine;
        }
    } catch (const std::exception& e) {
        // 创建失败，记录错误信息
    }
    
    return nullptr;
}

EngineCore* EngineManager::find_engine_core(int engine_id) {
    auto it = m_engine_cores.find(engine_id);
    return (it != m_engine_cores.end()) ? it->second.get() : nullptr;
}

// 清理和调试功能实现
ATE_EC EngineManager::cleanup() {
    try {
        // 停止全局核心
        if (m_global_core) {
            stop_global_core();
        }
        
        // 停止并清理所有引擎核心
        {
            std::lock_guard<std::mutex> lock(m_engines_mutex);
            for (auto& pair : m_engine_cores) {
                if (pair.second) {
                    pair.second->stop_engine();
                    pair.second->cleanup();
                }
            }
            m_engine_cores.clear();
        }
        
        // 清理全局核心
        if (m_global_core) {
            m_global_core->cleanup();
            m_global_core = nullptr;
        }
        
        m_initialized = false;
        m_config.clear();
        
        return ATE_SUCCESS;
        
    } catch (const std::exception& e) {
        return ATE_ERROR_SYSTEM_ERROR;
    }
}

const GlobalSettings& EngineManager::get_global_settings() const {
    return m_global_settings;
}

std::string EngineManager::get_global_settings_json_string() const {
    return m_global_settings.config_json_string;
}

ATE_EC EngineManager::debug_engine(int engine_id) {
    try {
        std::lock_guard<std::mutex> lock(m_engines_mutex);
        
        // 查找指定的引擎核心
        auto it = m_engine_cores.find(engine_id);
        if (it == m_engine_cores.end()) {
            return ATE_ERROR_NOT_FOUND; // 引擎不存在
        }
        
        if (it->second) {
            // 这里可以添加具体的调试信息输出
            // 例如：记录引擎状态、性能指标等
            return ATE_SUCCESS; // 成功
        }
        
        return ATE_ERROR_NOT_INITIALIZED; // 引擎核心为空
    } catch (const std::exception& e) {
        return ATE_ERROR_SYSTEM_ERROR; // 失败
    }
}

ATE_EC EngineManager::debug_global_core() {
    try {
        if (m_global_core) {
            // 输出全局核心调试信息
            // 例如：系统资源使用情况、监控状态等
            return ATE_SUCCESS; // 成功
        }
        return ATE_ERROR_NOT_INITIALIZED; // 全局核心未初始化
    } catch (const std::exception& e) {
        return ATE_ERROR_SYSTEM_ERROR; // 失败
    }
}

// 状态机回调函数实现
ATE_EC EngineManager::FSM_engine_callback(int engine_id) {
    try {
        // 引擎状态机回调处理
        // 这里可以根据引擎状态变化执行相应的操作
        std::lock_guard<std::mutex> lock(m_engines_mutex);
        
        auto it = m_engine_cores.find(engine_id);
        if (it == m_engine_cores.end()) {
            return ATE_ERROR_NOT_FOUND; // 引擎不存在
        }
        
        if (it->second) {
            // 处理指定引擎的状态变化
            // 例如：状态转换、事件处理等
            return ATE_SUCCESS; // 成功
        }
        
        return ATE_ERROR_NOT_INITIALIZED; // 引擎核心为空
    } catch (const std::exception& e) {
        return ATE_ERROR_SYSTEM_ERROR; // 失败
    }
}

ATE_EC EngineManager::FSM_global_core_callback() {
    try {
        if (m_global_core) {
            // 全局核心状态机回调处理
            // 例如：监控状态变化、告警处理等
            return ATE_SUCCESS; // 成功
        }
        return ATE_ERROR_NOT_INITIALIZED; // 全局核心未初始化
    } catch (const std::exception& e) {
        return ATE_ERROR_SYSTEM_ERROR; // 失败
    }
}

// 数据记录器管理实现
ATE_EC EngineManager::start_data_recorder(int engine_id) {
    try {
        std::lock_guard<std::mutex> lock(m_engines_mutex);
        
        auto it = m_engine_cores.find(engine_id);
        if (it == m_engine_cores.end()) {
            return ATE_ERROR_NOT_FOUND; // 引擎不存在
        }
        
        if (it->second) {
            // 启动指定引擎核心的数据记录器
            // 这里需要调用EngineCore的数据记录器启动方法
            return ATE_SUCCESS; // 成功
        }
        
        return ATE_ERROR_NOT_INITIALIZED; // 引擎核心为空
    } catch (const std::exception& e) {
        return ATE_ERROR_SYSTEM_ERROR; // 失败
    }
}

ATE_EC EngineManager::stop_data_recorder(int engine_id) {
    try {
        std::lock_guard<std::mutex> lock(m_engines_mutex);
        
        auto it = m_engine_cores.find(engine_id);
        if (it == m_engine_cores.end()) {
            return ATE_ERROR_NOT_FOUND; // 引擎不存在
        }
        
        if (it->second) {
            // 停止指定引擎核心的数据记录器
            // 这里需要调用EngineCore的数据记录器停止方法
            return ATE_SUCCESS; // 成功
        }
        
        return ATE_ERROR_NOT_INITIALIZED; // 引擎核心为空
    } catch (const std::exception& e) {
        return ATE_ERROR_SYSTEM_ERROR; // 失败
    }
}


// 系统监控管理（扩展功能）实现
ATE_EC EngineManager::suspend_global_core() {
    try {
        if (m_global_core) {
            // 暂停全局核心
            return m_global_core->suspend();
        }
        return ATE_ERROR_NOT_INITIALIZED; // 全局核心未初始化
    } catch (const std::exception& e) {
        return ATE_ERROR_SYSTEM_ERROR; // 失败
    }
}

ATE_EC EngineManager::resume_global_core() {
    try {
        if (m_global_core) {
            // 恢复全局核心
            return m_global_core->resume();
        }
        return ATE_ERROR_NOT_INITIALIZED; // 全局核心未初始化
    } catch (const std::exception& e) {
        return ATE_ERROR_SYSTEM_ERROR; // 失败
    }
}

ATE_EC EngineManager::start_report_output(int engine_id) {
    try {
        // 查找指定的引擎核心
        EngineCore* engine = find_engine_core(engine_id);
        if (!engine) {
            return ATE_ERROR_NOT_FOUND; // 引擎核心未找到
        }
        
        // 启动报告输出功能
        // 这里需要调用EngineCore的相关方法来启动报告输出
        // 假设EngineCore有start_report方法
        // ATE_EC result = engine->start_report();
        // if (result != ATE_SUCCESS) {
        //     return result; // 启动失败
        // }
        
        // 临时实现：返回成功
        return ATE_SUCCESS; // 成功
        
    } catch (const std::exception& e) {
        return ATE_ERROR_SYSTEM_ERROR; // 失败
    }
}