-- ============================================================================
-- ATE测试通道级监控模块 - Step-Based Framework
-- 文件: ate_channel_monitor.lua
-- 描述: 通道级监控，监控每个测试通道的运行状态，包括设备状态、测试参数等
-- 版本: V2.1 (标准化step-based工步控制框架)
-- 作者：TyrOnline
-- 创建时间：2025
-- ============================================================================

-- 工步控制变量
local step = 0
local run_flag = {1, 1, 1, 1, 1, 1, 1, 0, 0, 0}  -- 前7个工步启用
local test_result = "UNKNOWN"
local error_status = {}

-- 测试数据记录
local test_data_points = {}
local measurement_count = 0

-- 固定功能begin
function add_string(str)
    if not error_status[str] then
        error_status[str] = true
        return true
    end
    return false
end

-- 工步保护状态检查
function seq_protect_check()
    -- 监控模块不需要设备状态检查，直接返回true
    return true
end

-- 工步保护动作执行
function seq_protect_act()
    -- 监控模块不需要保护动作，记录错误信息
    if (next(error_status) ~= nil) then
        for error_key, _ in pairs(error_status) do
            log_channel_message("通道监控过程中发生错误: " .. error_key)
        end
        -- 通过故障保护动作的组合返回做3种情况，只记录+不动作+继续执行，记录+动作+继续执行，记录+动作+停止执行
        -- 无函数体 不动作
        -- return 1 动作后返回1  则动作了 后面停止执行
        -- return 2 动作了返回2  则动作了 后面继续执行
    end
    return false  -- 继续监控
end

-- 自定精确延时函数（毫秒级）
-- @param x 总延时时间（毫秒）
function delayms(x)
    local sleepms = 10  -- 每次循环基础延时
    local loops = math.floor(x / sleepms)  -- 完整循环次数
    local remainder = x % sleepms  -- 剩余延时
    
    -- 执行完整循环延时
    for i = 1, loops do
        -- 休眠循环基础延时 sleep_ms 采用C++注册的方式，注册一个通用的接口给lua
        if sleep_ms then sleep_ms(sleepms) end
        if seq_protect_check() == false then
            return
        end
    end
    
    -- 执行剩余延时
    if remainder > 0 then
        -- 休眠毫秒剩余
        if sleep_ms then sleep_ms(remainder) end
        if seq_protect_check() == false then
            return
        end
    end
end
-- 固定功能end

-- ============================================================================
-- 测试通道监控状态和配置
-- ============================================================================

-- 通道监控状态
local channel_monitor_state = {
    is_running = false,
    start_time = 0,
    last_check_time = 0,
    check_interval = 500,   -- 通道级检查间隔(毫秒) - 更频繁
    emergency_stop = false,
    alert_count = 0,
    active_channels = {},   -- 活跃通道列表
    max_channels = 8        -- 最大支持通道数
}

-- 测试通道级监控阈值配置 (3级分级)
local channel_thresholds = {
    -- 电气参数阈值 (3级分级)
    voltage = {
        level1_min = 11.7, level1_max = 12.3,  -- 1级告警：参数调整
        level2_min = 11.4, level2_max = 12.6,  -- 2级告警：暂停测试
        level3_min = 11.0, level3_max = 13.0   -- 3级告警：紧急断开
    },
    current = {
        level1 = 8.0,   -- 1级告警：电流监控
        level2 = 9.5,   -- 2级告警：降低负载
        level3 = 11.0   -- 3级告警：紧急断开
    },
    power = {
        level1 = 100,   -- 1级告警：功率监控
        level2 = 120,   -- 2级告警：功率限制
        level3 = 150    -- 3级告警：功率保护
    },
    
    -- 测试质量阈值 (3级分级)
    data_loss_rate = {
        level1 = 1.0,   -- 1级告警：数据质量下降
        level2 = 5.0,   -- 2级告警：重新测试
        level3 = 10.0   -- 3级告警：停止测试
    },
    signal_noise_ratio = {
        level1 = 40,    -- 1级告警：信号质量下降
        level2 = 30,    -- 2级告警：调整参数
        level3 = 20     -- 3级告警：更换设备
    },
    test_timeout = {
        level1 = 30000, -- 1级告警：测试超时(30s)
        level2 = 60000, -- 2级告警：严重超时(60s)
        level3 = 120000 -- 3级告警：异常超时(120s)
    },
    
    -- 通道资源阈值 (3级分级)
    channel_temperature = {
        level1 = 60,    -- 1级告警：通道温度升高
        level2 = 75,    -- 2级告警：通道过热
        level3 = 85     -- 3级告警：通道保护
    },
    resource_conflict = {
        level1 = 1,     -- 1级告警：轻微冲突
        level2 = 3,     -- 2级告警：严重冲突
        level3 = 5      -- 3级告警：资源死锁
    }
}

-- 测试通道状态缓存
local channel_states = {}

-- 初始化通道状态
local function init_channel_state(channel_id)
    channel_states[channel_id] = {
        channel_id = channel_id,
        is_active = false,
        test_status = "idle",  -- idle, running, paused, completed, error
        
        -- 电气参数
        electrical = {
            voltage = 0,
            current = 0,
            power = 0,
            resistance = 0,
            last_update = 0
        },
        
        -- 测试进度
        test_progress = {
            current_step = 0,
            total_steps = 0,
            progress_percent = 0,
            start_time = 0,
            estimated_completion = 0,
            last_update = 0
        },
        
        -- 数据质量
        data_quality = {
            samples_collected = 0,
            samples_lost = 0,
            data_loss_rate = 0,
            signal_noise_ratio = 0,
            last_update = 0
        },
        
        -- 通道资源
        channel_resources = {
            temperature = 0,
            resource_usage = 0,
            conflict_count = 0,
            last_update = 0
        },
        
        -- 被测件信息
        device_under_test = {
            device_id = "",
            device_type = "",
            test_sequence = "",
            connection_status = "disconnected"
        }
    }
end

-- 通道告警历史
local channel_alert_history = {}

-- ============================================================================
-- 测试通道监控检查
-- ============================================================================

-- 检查通道电气参数
local function check_channel_electrical_status(channel_id)
    local alerts = {}
    local current_time = os.time() * 1000
    local channel = channel_states[channel_id]
    
    if not channel or not channel.is_active then
        return alerts
    end
    
    -- 模拟获取电气参数
    local voltage = 11.5 + math.random() * 1.0  -- 11.5-12.5V
    local current = math.random() * 12.0         -- 0-12A
    local power = voltage * current              -- 功率计算
    
    channel.electrical.voltage = voltage
    channel.electrical.current = current
    channel.electrical.power = power
    channel.electrical.last_update = current_time
    
    -- 电压分级检查
    if voltage <= channel_thresholds.voltage.level3_min or 
       voltage >= channel_thresholds.voltage.level3_max then
        table.insert(alerts, {level = 3, type = "CRITICAL", category = "CHANNEL_VOLTAGE", 
                             channel_id = channel_id,
                             message = string.format("通道%d电压严重异常: %.2fV，紧急断开被测件", channel_id, voltage), 
                             value = voltage})
    elseif voltage <= channel_thresholds.voltage.level2_min or 
           voltage >= channel_thresholds.voltage.level2_max then
        table.insert(alerts, {level = 2, type = "WARNING", category = "CHANNEL_VOLTAGE", 
                             channel_id = channel_id,
                             message = string.format("通道%d电压异常: %.2fV，暂停当前测试", channel_id, voltage), 
                             value = voltage})
    elseif voltage <= channel_thresholds.voltage.level1_min or 
           voltage >= channel_thresholds.voltage.level1_max then
        table.insert(alerts, {level = 1, type = "INFO", category = "CHANNEL_VOLTAGE", 
                             channel_id = channel_id,
                             message = string.format("通道%d电压偏离: %.2fV，调整测试参数", channel_id, voltage), 
                             value = voltage})
    end
    
    -- 电流分级检查
    if current >= channel_thresholds.current.level3 then
        table.insert(alerts, {level = 3, type = "CRITICAL", category = "CHANNEL_CURRENT", 
                             channel_id = channel_id,
                             message = string.format("通道%d电流严重过载: %.2fA，紧急断开保护", channel_id, current), 
                             value = current})
    elseif current >= channel_thresholds.current.level2 then
        table.insert(alerts, {level = 2, type = "WARNING", category = "CHANNEL_CURRENT", 
                             channel_id = channel_id,
                             message = string.format("通道%d电流过载: %.2fA，降低测试负载", channel_id, current), 
                             value = current})
    elseif current >= channel_thresholds.current.level1 then
        table.insert(alerts, {level = 1, type = "INFO", category = "CHANNEL_CURRENT", 
                             channel_id = channel_id,
                             message = string.format("通道%d电流较高: %.2fA，监控电流变化", channel_id, current), 
                             value = current})
    end
    
    -- 功率分级检查
    if power >= channel_thresholds.power.level3 then
        table.insert(alerts, {level = 3, type = "CRITICAL", category = "CHANNEL_POWER", 
                             channel_id = channel_id,
                             message = string.format("通道%d功率严重过载: %.1fW，功率保护", channel_id, power), 
                             value = power})
    elseif power >= channel_thresholds.power.level2 then
        table.insert(alerts, {level = 2, type = "WARNING", category = "CHANNEL_POWER", 
                             channel_id = channel_id,
                             message = string.format("通道%d功率过载: %.1fW，功率限制", channel_id, power), 
                             value = power})
    elseif power >= channel_thresholds.power.level1 then
        table.insert(alerts, {level = 1, type = "INFO", category = "CHANNEL_POWER", 
                             channel_id = channel_id,
                             message = string.format("通道%d功率较高: %.1fW，功率监控", channel_id, power), 
                             value = power})
    end
    
    return alerts
end

-- 检查测试进度和质量
local function check_channel_test_quality(channel_id)
    local alerts = {}
    local current_time = os.time() * 1000
    local channel = channel_states[channel_id]
    
    if not channel or not channel.is_active or channel.test_status ~= "running" then
        return alerts
    end
    
    -- 模拟测试质量数据
    local samples_collected = math.random(1000, 10000)
    local samples_lost = math.random(0, 500)
    local data_loss_rate = (samples_lost / samples_collected) * 100
    local signal_noise_ratio = math.random(15, 50)
    local test_duration = current_time - channel.test_progress.start_time
    
    channel.data_quality.samples_collected = samples_collected
    channel.data_quality.samples_lost = samples_lost
    channel.data_quality.data_loss_rate = data_loss_rate
    channel.data_quality.signal_noise_ratio = signal_noise_ratio
    channel.data_quality.last_update = current_time
    
    -- 数据丢失率分级检查
    if data_loss_rate >= channel_thresholds.data_loss_rate.level3 then
        table.insert(alerts, {level = 3, type = "CRITICAL", category = "CHANNEL_DATA_QUALITY", 
                             channel_id = channel_id,
                             message = string.format("通道%d数据丢失严重: %.1f%%，停止测试", channel_id, data_loss_rate), 
                             value = data_loss_rate})
    elseif data_loss_rate >= channel_thresholds.data_loss_rate.level2 then
        table.insert(alerts, {level = 2, type = "WARNING", category = "CHANNEL_DATA_QUALITY", 
                             channel_id = channel_id,
                             message = string.format("通道%d数据丢失过多: %.1f%%，重新测试", channel_id, data_loss_rate), 
                             value = data_loss_rate})
    elseif data_loss_rate >= channel_thresholds.data_loss_rate.level1 then
        table.insert(alerts, {level = 1, type = "INFO", category = "CHANNEL_DATA_QUALITY", 
                             channel_id = channel_id,
                             message = string.format("通道%d数据质量下降: %.1f%%，检查连接", channel_id, data_loss_rate), 
                             value = data_loss_rate})
    end
    
    -- 信噪比分级检查
    if signal_noise_ratio <= channel_thresholds.signal_noise_ratio.level3 then
        table.insert(alerts, {level = 3, type = "CRITICAL", category = "CHANNEL_SIGNAL_QUALITY", 
                             channel_id = channel_id,
                             message = string.format("通道%d信号质量严重恶化: %ddB，更换设备", channel_id, signal_noise_ratio), 
                             value = signal_noise_ratio})
    elseif signal_noise_ratio <= channel_thresholds.signal_noise_ratio.level2 then
        table.insert(alerts, {level = 2, type = "WARNING", category = "CHANNEL_SIGNAL_QUALITY", 
                             channel_id = channel_id,
                             message = string.format("通道%d信号质量下降: %ddB，调整参数", channel_id, signal_noise_ratio), 
                             value = signal_noise_ratio})
    elseif signal_noise_ratio <= channel_thresholds.signal_noise_ratio.level1 then
        table.insert(alerts, {level = 1, type = "INFO", category = "CHANNEL_SIGNAL_QUALITY", 
                             channel_id = channel_id,
                             message = string.format("通道%d信号质量轻微下降: %ddB，监控信号", channel_id, signal_noise_ratio), 
                             value = signal_noise_ratio})
    end
    
    -- 测试超时分级检查
    if test_duration >= channel_thresholds.test_timeout.level3 then
        table.insert(alerts, {level = 3, type = "CRITICAL", category = "CHANNEL_TEST_TIMEOUT", 
                             channel_id = channel_id,
                             message = string.format("通道%d测试异常超时: %ds，强制停止", channel_id, test_duration/1000), 
                             value = test_duration})
    elseif test_duration >= channel_thresholds.test_timeout.level2 then
        table.insert(alerts, {level = 2, type = "WARNING", category = "CHANNEL_TEST_TIMEOUT", 
                             channel_id = channel_id,
                             message = string.format("通道%d测试严重超时: %ds，检查测试流程", channel_id, test_duration/1000), 
                             value = test_duration})
    elseif test_duration >= channel_thresholds.test_timeout.level1 then
        table.insert(alerts, {level = 1, type = "INFO", category = "CHANNEL_TEST_TIMEOUT", 
                             channel_id = channel_id,
                             message = string.format("通道%d测试超时: %ds，检查测试进度", channel_id, test_duration/1000), 
                             value = test_duration})
    end
    
    return alerts
end

-- 检查通道资源状态
local function check_channel_resource_status(channel_id)
    local alerts = {}
    local current_time = os.time() * 1000
    local channel = channel_states[channel_id]
    
    if not channel or not channel.is_active then
        return alerts
    end
    
    -- 模拟通道资源数据
    local channel_temp = math.random(40, 90)
    local resource_usage = math.random(20, 100)
    local conflict_count = math.random(0, 6)
    
    channel.channel_resources.temperature = channel_temp
    channel.channel_resources.resource_usage = resource_usage
    channel.channel_resources.conflict_count = conflict_count
    channel.channel_resources.last_update = current_time
    
    -- 通道温度分级检查
    if channel_temp >= channel_thresholds.channel_temperature.level3 then
        table.insert(alerts, {level = 3, type = "CRITICAL", category = "CHANNEL_TEMPERATURE", 
                             channel_id = channel_id,
                             message = string.format("通道%d温度严重过高: %d°C，通道保护", channel_id, channel_temp), 
                             value = channel_temp})
    elseif channel_temp >= channel_thresholds.channel_temperature.level2 then
        table.insert(alerts, {level = 2, type = "WARNING", category = "CHANNEL_TEMPERATURE", 
                             channel_id = channel_id,
                             message = string.format("通道%d温度过高: %d°C，降低负载", channel_id, channel_temp), 
                             value = channel_temp})
    elseif channel_temp >= channel_thresholds.channel_temperature.level1 then
        table.insert(alerts, {level = 1, type = "INFO", category = "CHANNEL_TEMPERATURE", 
                             channel_id = channel_id,
                             message = string.format("通道%d温度升高: %d°C，加强散热", channel_id, channel_temp), 
                             value = channel_temp})
    end
    
    -- 资源冲突分级检查
    if conflict_count >= channel_thresholds.resource_conflict.level3 then
        table.insert(alerts, {level = 3, type = "CRITICAL", category = "CHANNEL_RESOURCE_CONFLICT", 
                             channel_id = channel_id,
                             message = string.format("通道%d资源死锁: %d个冲突，重启通道", channel_id, conflict_count), 
                             value = conflict_count})
    elseif conflict_count >= channel_thresholds.resource_conflict.level2 then
        table.insert(alerts, {level = 2, type = "WARNING", category = "CHANNEL_RESOURCE_CONFLICT", 
                             channel_id = channel_id,
                             message = string.format("通道%d严重冲突: %d个冲突，重新分配资源", channel_id, conflict_count), 
                             value = conflict_count})
    elseif conflict_count >= channel_thresholds.resource_conflict.level1 then
        table.insert(alerts, {level = 1, type = "INFO", category = "CHANNEL_RESOURCE_CONFLICT", 
                             channel_id = channel_id,
                             message = string.format("通道%d轻微冲突: %d个冲突，优化资源分配", channel_id, conflict_count), 
                             value = conflict_count})
    end
    
    return alerts
end

-- ============================================================================
-- 通道级告警处理中心
-- ============================================================================

-- 记录通道告警
local function log_channel_alert(alert)
    local timestamp = os.date("%Y-%m-%d %H:%M:%S")
    local log_entry = {
        timestamp = timestamp,
        level = alert.level,
        type = alert.type,
        category = alert.category,
        channel_id = alert.channel_id,
        message = alert.message,
        value = alert.value
    }
    
    table.insert(channel_alert_history, log_entry)
    
    -- 保持告警历史记录在合理范围内
    if #channel_alert_history > 1000 then
        table.remove(channel_alert_history, 1)
    end
    
    -- 输出告警信息
    print(string.format("[CHANNEL][%s] Level-%d %s - %s: %s", 
          timestamp, alert.level, alert.type, alert.category, alert.message))
end

-- 处理通道告警
local function handle_channel_alerts(alerts)
    for _, alert in ipairs(alerts) do
        log_channel_alert(alert)
        channel_monitor_state.alert_count = channel_monitor_state.alert_count + 1
        
        -- 根据告警级别执行相应动作
        if alert.level == 3 then
            handle_channel_level3_alert(alert)
        elseif alert.level == 2 then
            handle_channel_level2_alert(alert)
        elseif alert.level == 1 then
            handle_channel_level1_alert(alert)
        end
    end
end

-- ============================================================================
-- 通道级3级分级响应动作
-- ============================================================================

-- 1级响应动作：参数调整和优化
function handle_channel_level1_alert(alert)
    local channel_id = alert.channel_id
    
    if alert.category == "CHANNEL_VOLTAGE" then
        print(string.format("执行通道%d 1级电压调整：微调测试参数", channel_id))
        adjust_channel_voltage_parameters(channel_id)
        
    elseif alert.category == "CHANNEL_CURRENT" then
        print(string.format("执行通道%d 1级电流监控：加强电流监测", channel_id))
        increase_current_monitoring(channel_id)
        
    elseif alert.category == "CHANNEL_POWER" then
        print(string.format("执行通道%d 1级功率监控：优化功率分配", channel_id))
        optimize_power_distribution(channel_id)
        
    elseif alert.category == "CHANNEL_DATA_QUALITY" then
        print(string.format("执行通道%d 1级数据质量优化：检查连接", channel_id))
        check_channel_connections(channel_id)
        
    elseif alert.category == "CHANNEL_SIGNAL_QUALITY" then
        print(string.format("执行通道%d 1级信号优化：调整信号参数", channel_id))
        adjust_signal_parameters(channel_id)
        
    elseif alert.category == "CHANNEL_TEST_TIMEOUT" then
        print(string.format("执行通道%d 1级超时处理：检查测试进度", channel_id))
        check_test_progress(channel_id)
        
    elseif alert.category == "CHANNEL_TEMPERATURE" then
        print(string.format("执行通道%d 1级散热：启动通道风扇", channel_id))
        activate_channel_cooling(channel_id)
        
    elseif alert.category == "CHANNEL_RESOURCE_CONFLICT" then
        print(string.format("执行通道%d 1级资源优化：重新分配资源", channel_id))
        optimize_resource_allocation(channel_id)
    end
end

-- 2级响应动作：暂停和保护
function handle_channel_level2_alert(alert)
    local channel_id = alert.channel_id
    
    if alert.category == "CHANNEL_VOLTAGE" then
        print(string.format("执行通道%d 2级电压保护：暂停当前测试", channel_id))
        pause_channel_test(channel_id)
        stabilize_channel_voltage(channel_id)
        
    elseif alert.category == "CHANNEL_CURRENT" then
        print(string.format("执行通道%d 2级电流保护：降低测试负载", channel_id))
        reduce_test_load(channel_id)
        
    elseif alert.category == "CHANNEL_POWER" then
        print(string.format("执行通道%d 2级功率保护：功率限制", channel_id))
        limit_channel_power(channel_id)
        
    elseif alert.category == "CHANNEL_DATA_QUALITY" then
        print(string.format("执行通道%d 2级数据保护：重新开始测试", channel_id))
        restart_channel_test(channel_id)
        
    elseif alert.category == "CHANNEL_SIGNAL_QUALITY" then
        print(string.format("执行通道%d 2级信号保护：切换备用设备", channel_id))
        switch_to_backup_equipment(channel_id)
        
    elseif alert.category == "CHANNEL_TEST_TIMEOUT" then
        print(string.format("执行通道%d 2级超时保护：重置测试流程", channel_id))
        reset_test_sequence(channel_id)
        
    elseif alert.category == "CHANNEL_TEMPERATURE" then
        print(string.format("执行通道%d 2级散热保护：降低通道负载", channel_id))
        reduce_channel_load(channel_id)
        
    elseif alert.category == "CHANNEL_RESOURCE_CONFLICT" then
        print(string.format("执行通道%d 2级资源保护：重新分配资源", channel_id))
        reallocate_channel_resources(channel_id)
    end
end

-- 3级响应动作：紧急断开和保护
function handle_channel_level3_alert(alert)
    local channel_id = alert.channel_id
    
    if alert.category == "CHANNEL_VOLTAGE" then
        print(string.format("执行通道%d 3级电压保护：紧急断开被测件", channel_id))
        emergency_disconnect_dut(channel_id)
        
    elseif alert.category == "CHANNEL_CURRENT" then
        print(string.format("执行通道%d 3级电流保护：紧急断开保护", channel_id))
        emergency_current_cutoff(channel_id)
        
    elseif alert.category == "CHANNEL_POWER" then
        print(string.format("执行通道%d 3级功率保护：切断通道电源", channel_id))
        cutoff_channel_power(channel_id)
        
    elseif alert.category == "CHANNEL_DATA_QUALITY" then
        print(string.format("执行通道%d 3级数据保护：停止测试保存数据", channel_id))
        stop_test_save_data(channel_id)
        
    elseif alert.category == "CHANNEL_SIGNAL_QUALITY" then
        print(string.format("执行通道%d 3级信号保护：更换测试设备", channel_id))
        replace_test_equipment(channel_id)
        
    elseif alert.category == "CHANNEL_TEST_TIMEOUT" then
        print(string.format("执行通道%d 3级超时保护：强制停止测试", channel_id))
        force_stop_test(channel_id)
        
    elseif alert.category == "CHANNEL_TEMPERATURE" then
        print(string.format("执行通道%d 3级散热保护：通道紧急停机", channel_id))
        emergency_channel_shutdown(channel_id)
        
    elseif alert.category == "CHANNEL_RESOURCE_CONFLICT" then
        print(string.format("执行通道%d 3级资源保护：重启通道", channel_id))
        restart_channel(channel_id)
    end
end

-- ============================================================================
-- 通道级响应动作实现函数
-- ============================================================================

-- 1级响应动作实现
function adjust_channel_voltage_parameters(channel_id)
    print(string.format("调整通道%d电压参数：微调电压设定值", channel_id))
    -- 实际实现：调整电压参数
end

function increase_current_monitoring(channel_id)
    print(string.format("增强通道%d电流监控：提高采样频率", channel_id))
    -- 实际实现：提高监控频率
end

function optimize_power_distribution(channel_id)
    print(string.format("优化通道%d功率分配：平衡功率负载", channel_id))
    -- 实际实现：功率优化
end

function check_channel_connections(channel_id)
    print(string.format("检查通道%d连接：验证所有连接状态", channel_id))
    -- 实际实现：连接检查
end

function adjust_signal_parameters(channel_id)
    print(string.format("调整通道%d信号参数：优化信号质量", channel_id))
    -- 实际实现：信号调整
end

function check_test_progress(channel_id)
    print(string.format("检查通道%d测试进度：分析测试状态", channel_id))
    -- 实际实现：进度检查
end

function activate_channel_cooling(channel_id)
    print(string.format("启动通道%d散热：开启通道风扇", channel_id))
    -- 实际实现：散热控制
end

function optimize_resource_allocation(channel_id)
    print(string.format("优化通道%d资源分配：重新分配资源", channel_id))
    -- 实际实现：资源优化
end

-- 2级响应动作实现
function pause_channel_test(channel_id)
    print(string.format("暂停通道%d测试：保存当前状态", channel_id))
    if channel_states[channel_id] then
        channel_states[channel_id].test_status = "paused"
    end
    -- 实际实现：暂停测试
end

function stabilize_channel_voltage(channel_id)
    print(string.format("稳定通道%d电压：调整电源输出", channel_id))
    -- 实际实现：电压稳定
end

function reduce_test_load(channel_id)
    print(string.format("降低通道%d测试负载：减少测试强度", channel_id))
    -- 实际实现：负载控制
end

function limit_channel_power(channel_id)
    print(string.format("限制通道%d功率：设置功率上限", channel_id))
    -- 实际实现：功率限制
end

function restart_channel_test(channel_id)
    print(string.format("重启通道%d测试：从头开始测试", channel_id))
    if channel_states[channel_id] then
        channel_states[channel_id].test_status = "running"
        channel_states[channel_id].test_progress.current_step = 0
        channel_states[channel_id].test_progress.start_time = os.time() * 1000
    end
    -- 实际实现：重启测试
end

function switch_to_backup_equipment(channel_id)
    print(string.format("切换通道%d备用设备：使用备用测试设备", channel_id))
    -- 实际实现：设备切换
end

function reset_test_sequence(channel_id)
    print(string.format("重置通道%d测试序列：重新加载测试流程", channel_id))
    -- 实际实现：序列重置
end

function reduce_channel_load(channel_id)
    print(string.format("降低通道%d负载：减少通道使用率", channel_id))
    -- 实际实现：负载降低
end

function reallocate_channel_resources(channel_id)
    print(string.format("重新分配通道%d资源：解决资源冲突", channel_id))
    -- 实际实现：资源重分配
end

-- 3级响应动作实现
function emergency_disconnect_dut(channel_id)
    print(string.format("紧急断开通道%d被测件：保护被测件安全", channel_id))
    if channel_states[channel_id] then
        channel_states[channel_id].device_under_test.connection_status = "disconnected"
        channel_states[channel_id].test_status = "error"
    end
    -- 实际实现：紧急断开
end

function emergency_current_cutoff(channel_id)
    print(string.format("紧急切断通道%d电流：防止设备损坏", channel_id))
    -- 实际实现：电流切断
end

function cutoff_channel_power(channel_id)
    print(string.format("切断通道%d电源：完全断电保护", channel_id))
    if channel_states[channel_id] then
        channel_states[channel_id].is_active = false
        channel_states[channel_id].test_status = "error"
    end
    -- 实际实现：断电保护
end

function stop_test_save_data(channel_id)
    print(string.format("停止通道%d测试并保存数据：保护测试数据", channel_id))
    if channel_states[channel_id] then
        channel_states[channel_id].test_status = "completed"
    end
    -- 实际实现：停止并保存
end

function replace_test_equipment(channel_id)
    print(string.format("更换通道%d测试设备：使用新的测试设备", channel_id))
    -- 实际实现：设备更换
end

function force_stop_test(channel_id)
    print(string.format("强制停止通道%d测试：立即终止测试", channel_id))
    if channel_states[channel_id] then
        channel_states[channel_id].test_status = "error"
        channel_states[channel_id].is_active = false
    end
    -- 实际实现：强制停止
end

function emergency_channel_shutdown(channel_id)
    print(string.format("紧急停机通道%d：通道完全停机", channel_id))
    if channel_states[channel_id] then
        channel_states[channel_id].is_active = false
        channel_states[channel_id].test_status = "error"
    end
    -- 实际实现：紧急停机
end

function restart_channel(channel_id)
    print(string.format("重启通道%d：完全重启通道系统", channel_id))
    -- 重新初始化通道状态
    init_channel_state(channel_id)
    -- 实际实现：通道重启
end

-- ============================================================================
-- 通道管理函数
-- ============================================================================

-- 激活测试通道
function activate_test_channel(channel_id, device_info)
    if channel_id < 1 or channel_id > channel_monitor_state.max_channels then
        print(string.format("错误：通道ID %d 超出范围 (1-%d)", channel_id, channel_monitor_state.max_channels))
        return false
    end
    
    -- 初始化通道状态
    init_channel_state(channel_id)
    
    local channel = channel_states[channel_id]
    channel.is_active = true
    channel.test_status = "idle"
    
    if device_info then
        channel.device_under_test.device_id = device_info.device_id or ""
        channel.device_under_test.device_type = device_info.device_type or ""
        channel.device_under_test.test_sequence = device_info.test_sequence or ""
        channel.device_under_test.connection_status = "connected"
    end
    
    -- 添加到活跃通道列表
    table.insert(channel_monitor_state.active_channels, channel_id)
    
    print(string.format("通道%d已激活，设备类型：%s", channel_id, channel.device_under_test.device_type))
    return true
end

-- 停用测试通道
function deactivate_test_channel(channel_id)
    if not channel_states[channel_id] then
        print(string.format("错误：通道%d未初始化", channel_id))
        return false
    end
    
    local channel = channel_states[channel_id]
    channel.is_active = false
    channel.test_status = "idle"
    channel.device_under_test.connection_status = "disconnected"
    
    -- 从活跃通道列表中移除
    for i, active_id in ipairs(channel_monitor_state.active_channels) do
        if active_id == channel_id then
            table.remove(channel_monitor_state.active_channels, i)
            break
        end
    end
    
    print(string.format("通道%d已停用", channel_id))
    return true
end

-- 开始通道测试
function start_channel_test(channel_id, test_config)
    if not channel_states[channel_id] or not channel_states[channel_id].is_active then
        print(string.format("错误：通道%d未激活", channel_id))
        return false
    end
    
    local channel = channel_states[channel_id]
    channel.test_status = "running"
    channel.test_progress.start_time = os.time() * 1000
    channel.test_progress.current_step = 0
    
    if test_config then
        channel.test_progress.total_steps = test_config.total_steps or 100
        channel.device_under_test.test_sequence = test_config.test_sequence or ""
    end
    
    print(string.format("通道%d开始测试，测试序列：%s", channel_id, channel.device_under_test.test_sequence))
    return true
end

-- ============================================================================
-- 通道级主监控循环
-- ============================================================================

-- 执行通道级监控检查
local function perform_channel_monitoring_check()
    local all_alerts = {}
    
    -- 检查所有活跃通道
    for _, channel_id in ipairs(channel_monitor_state.active_channels) do
        -- 执行各项通道检查
        local electrical_alerts = check_channel_electrical_status(channel_id)
        local quality_alerts = check_channel_test_quality(channel_id)
        local resource_alerts = check_channel_resource_status(channel_id)
        
        -- 合并通道告警
        for _, alert in ipairs(electrical_alerts) do table.insert(all_alerts, alert) end
        for _, alert in ipairs(quality_alerts) do table.insert(all_alerts, alert) end
        for _, alert in ipairs(resource_alerts) do table.insert(all_alerts, alert) end
    end
    
    -- 处理告警
    if #all_alerts > 0 then
        handle_channel_alerts(all_alerts)
    end
    
    return all_alerts
end

-- 通道级监控主循环
function start_channel_monitor()
    if channel_monitor_state.is_running then
        print("通道级监控已在运行中")
        return false
    end
    
    channel_monitor_state.is_running = true
    channel_monitor_state.start_time = os.time() * 1000
    channel_monitor_state.emergency_stop = false
    
    print("启动ATE通道级监控...")
    print("监控范围：电气参数、测试质量、通道资源、被测件状态")
    print("保护级别：3级分级响应机制")
    print(string.format("支持通道数：%d个并行通道", channel_monitor_state.max_channels))
    
    while channel_monitor_state.is_running and not channel_monitor_state.emergency_stop do
        local current_time = os.time() * 1000
        
        if current_time - channel_monitor_state.last_check_time >= channel_monitor_state.check_interval then
            channel_monitor_state.last_check_time = current_time
            
            -- 执行通道级监控检查
            local alerts = perform_channel_monitoring_check()
            
            -- 输出通道状态摘要
            if #alerts == 0 and #channel_monitor_state.active_channels > 0 then
                print(string.format("[通道级监控] %d个通道运行正常", #channel_monitor_state.active_channels))
            elseif #channel_monitor_state.active_channels == 0 then
                print("[通道级监控] 无活跃测试通道")
            end
        end
        
        -- 短暂休眠，避免过度占用CPU
        os.execute("timeout /t 1 /nobreak > nul 2>&1")  -- Windows
        -- os.execute("sleep 1")  -- Linux/Unix
    end
    
    print("通道级监控已停止")
    return true
end

-- 停止通道级监控
function stop_channel_monitor()
    if not channel_monitor_state.is_running then
        print("通道级监控未在运行")
        return false
    end
    
    channel_monitor_state.is_running = false
    print("正在停止通道级监控...")
    return true
end

-- 获取通道级监控状态
function get_channel_monitor_status()
    return {
        is_running = channel_monitor_state.is_running,
        start_time = channel_monitor_state.start_time,
        alert_count = channel_monitor_state.alert_count,
        emergency_stop = channel_monitor_state.emergency_stop,
        active_channels = channel_monitor_state.active_channels,
        max_channels = channel_monitor_state.max_channels,
        channel_states = channel_states,
        recent_alerts = channel_alert_history
    }
end

-- 获取特定通道状态
function get_channel_status(channel_id)
    if not channel_states[channel_id] then
        return nil
    end
    return channel_states[channel_id]
end

-- 更新通道级监控配置
function update_channel_monitor_config(new_config)
    if new_config.check_interval then
        channel_monitor_state.check_interval = new_config.check_interval
    end
    
    if new_config.max_channels then
        channel_monitor_state.max_channels = new_config.max_channels
    end
    
    if new_config.thresholds then
        for key, value in pairs(new_config.thresholds) do
            if channel_thresholds[key] then
                channel_thresholds[key] = value
            end
        end
    end
    
    print("通道级监控配置已更新")
    return true
end

-- ============================================================================
-- 通道级监控初始化
-- ============================================================================

print("ATE测试通道级监控模块已加载")
print("功能：电气参数、测试质量、通道资源、被测件监控")
print("保护机制：3级分级响应 (1级调整 -> 2级保护 -> 3级紧急)")
print("通道管理：activate_test_channel(id, device_info) 激活通道")
print("测试控制：start_channel_test(id, test_config) 开始测试")
print("监控控制：start_channel_monitor() 启动监控")
print("状态查询：get_channel_monitor_status() 获取状态")
print("配置更新：update_channel_monitor_config(config) 更新配置")
print("停止监控：stop_channel_monitor() 停止监控")

-- 脚本加载完成，等待外部调用启动函数